/**
 * Pinecone Control Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import type { DeletionProtection } from './DeletionProtection';
import type { IndexModelSpec } from './IndexModelSpec';
import type { IndexModelStatus } from './IndexModelStatus';
/**
 * The IndexModel describes the configuration and status of a Pinecone index.
 * @export
 * @interface IndexModel
 */
export interface IndexModel {
    /**
     * The name of the index. Resource name must be 1-45 characters long, start and end with an alphanumeric character, and consist only of lower case alphanumeric characters or '-'.
     * @type {string}
     * @memberof IndexModel
     */
    name: string;
    /**
     * The dimensions of the vectors to be inserted in the index.
     * @type {number}
     * @memberof IndexModel
     */
    dimension: number;
    /**
     * The distance metric to be used for similarity search. You can use 'euclidean', 'cosine', or 'dotproduct'.
     * @type {string}
     * @memberof IndexModel
     */
    metric: IndexModelMetricEnum;
    /**
     * The URL address where the index is hosted.
     * @type {string}
     * @memberof IndexModel
     */
    host: string;
    /**
     *
     * @type {DeletionProtection}
     * @memberof IndexModel
     */
    deletionProtection?: DeletionProtection;
    /**
     * Custom user tags added to an index. Keys must be alphanumeric and 80 characters or less. Values must be 120 characters or less.
     * @type {{ [key: string]: string; }}
     * @memberof IndexModel
     */
    tags?: {
        [key: string]: string;
    } | null;
    /**
     *
     * @type {IndexModelSpec}
     * @memberof IndexModel
     */
    spec: IndexModelSpec;
    /**
     *
     * @type {IndexModelStatus}
     * @memberof IndexModel
     */
    status: IndexModelStatus;
}
/**
 * @export
 */
export declare const IndexModelMetricEnum: {
    readonly Cosine: "cosine";
    readonly Euclidean: "euclidean";
    readonly Dotproduct: "dotproduct";
};
export type IndexModelMetricEnum = typeof IndexModelMetricEnum[keyof typeof IndexModelMetricEnum];
/**
 * Check if a given object implements the IndexModel interface.
 */
export declare function instanceOfIndexModel(value: object): boolean;
export declare function IndexModelFromJSON(json: any): IndexModel;
export declare function IndexModelFromJSONTyped(json: any, ignoreDiscriminator: boolean): IndexModel;
export declare function IndexModelToJSON(value?: IndexModel | null): any;

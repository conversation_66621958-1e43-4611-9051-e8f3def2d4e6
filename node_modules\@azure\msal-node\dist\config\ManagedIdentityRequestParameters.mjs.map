{"version": 3, "file": "ManagedIdentityRequestParameters.mjs", "sources": ["../../src/config/ManagedIdentityRequestParameters.ts"], "sourcesContent": [null], "names": [], "mappings": ";;;;AAAA;;;AAGG;MAKU,gCAAgC,CAAA;IAOzC,WAAY,CAAA,UAAsB,EAAE,QAAgB,EAAA;AAChD,QAAA,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;AAC7B,QAAA,IAAI,CAAC,aAAa,GAAG,QAAQ,CAAC;AAC9B,QAAA,IAAI,CAAC,OAAO,GAAG,EAA4B,CAAC;AAC5C,QAAA,IAAI,CAAC,cAAc,GAAG,EAA4B,CAAC;AACnD,QAAA,IAAI,CAAC,eAAe,GAAG,EAA4B,CAAC;KACvD;IAEM,UAAU,GAAA;AACb,QAAA,MAAM,gBAAgB,GAAG,IAAI,uBAAuB,EAAE,CAAC;QAEvD,IAAI,IAAI,CAAC,eAAe,EAAE;AACtB,YAAA,gBAAgB,CAAC,uBAAuB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;AAClE,SAAA;AAED,QAAA,MAAM,qBAAqB,GAAG,gBAAgB,CAAC,iBAAiB,EAAE,CAAC;QAEnE,OAAO,SAAS,CAAC,iBAAiB,CAC9B,IAAI,CAAC,aAAa,EAClB,qBAAqB,CACxB,CAAC;KACL;IAEM,2BAA2B,GAAA;AAC9B,QAAA,MAAM,gBAAgB,GAAG,IAAI,uBAAuB,EAAE,CAAC;QAEvD,IAAI,IAAI,CAAC,cAAc,EAAE;AACrB,YAAA,gBAAgB,CAAC,uBAAuB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;AACjE,SAAA;AAED,QAAA,OAAO,gBAAgB,CAAC,iBAAiB,EAAE,CAAC;KAC/C;AACJ;;;;"}
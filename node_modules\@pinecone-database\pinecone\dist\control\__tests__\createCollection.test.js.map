{"version": 3, "file": "createCollection.test.js", "sourceRoot": "", "sources": ["../../../src/control/__tests__/createCollection.test.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,wDAAuD;AACvD,uCAAqD;AAQrD,IAAM,kBAAkB,GAAG,UAAC,4BAA4B;IACtD,IAAM,oBAAoB,GAEM,IAAI;SACjC,EAAE,EAAE;SACJ,kBAAkB,CAAC,4BAA4B,CAAC,CAAC;IACpD,IAAM,eAAe,GAA6B,IAAI;SACnD,EAAE,EAAE;SACJ,kBAAkB,CAAC;QAClB,OAAA,OAAO,CAAC,OAAO,CAAC;YACd,OAAO,EAAE;gBACP;oBACE,IAAI,EAAE,SAAS;oBACf,SAAS,EAAE,CAAC;oBACZ,MAAM,EAAE,QAAQ;oBAChB,IAAI,EAAE,iBAAiB;oBACvB,IAAI,EAAE;wBACJ,GAAG,EAAE;4BACH,WAAW,EAAE,UAAU;4BACvB,QAAQ,EAAE,CAAC;4BACX,MAAM,EAAE,CAAC;4BACT,OAAO,EAAE,OAAO;4BAChB,IAAI,EAAE,CAAC;yBACR;qBACF;oBACD,MAAM,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE;iBACxC;gBACD;oBACE,IAAI,EAAE,SAAS;oBACf,SAAS,EAAE,CAAC;oBACZ,MAAM,EAAE,QAAQ;oBAChB,IAAI,EAAE,iBAAiB;oBACvB,IAAI,EAAE;wBACJ,GAAG,EAAE;4BACH,WAAW,EAAE,UAAU;4BACvB,QAAQ,EAAE,CAAC;4BACX,MAAM,EAAE,CAAC;4BACT,OAAO,EAAE,OAAO;4BAChB,IAAI,EAAE,CAAC;yBACR;qBACF;oBACD,MAAM,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE;iBACxC;aACF;SACF,CAAC;IAnCF,CAmCE,CACH,CAAC;IACJ,IAAM,GAAG,GAAG;QACV,gBAAgB,EAAE,oBAAoB;QACtC,WAAW,EAAE,eAAe;KACT,CAAC;IAEtB,OAAO,GAAG,CAAC;AACb,CAAC,CAAC;AAEF,QAAQ,CAAC,kBAAkB,EAAE;IAC3B,QAAQ,CAAC,sBAAsB,EAAE;QAC/B,IAAI,CAAC,qCAAqC,EAAE;;;;;wBACpC,GAAG,GAAG,kBAAkB,CAAC,cAAM,OAAA,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,EAAnB,CAAmB,CAAC,CAAC;wBACpD,OAAO,GAAG;;;;oCACd,aAAa;oCACb,qBAAM,IAAA,mCAAgB,EAAC,GAAG,CAAC,EAAE,EAAA;;wCAD7B,aAAa;wCACb,SAA6B,CAAC;;;;6BAC/B,CAAC;wBAEF,qBAAM,MAAM,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,8BAAqB,CAAC,EAAA;;wBAAjE,SAAiE,CAAC;wBAClE,qBAAM,MAAM,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,YAAY,CACxC,mGAAmG,CACpG,EAAA;;wBAFD,SAEC,CAAC;;;;aACH,CAAC,CAAC;QAEH,IAAI,CAAC,wBAAwB,EAAE;;;;;wBACvB,GAAG,GAAG,kBAAkB,CAAC,cAAM,OAAA,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,EAAnB,CAAmB,CAAC,CAAC;wBACpD,OAAO,GAAG;;;;oCACd,aAAa;oCACb,qBAAM,IAAA,mCAAgB,EAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAA;;wCAD/B,aAAa;wCACb,SAA+B,CAAC;;;;6BACjC,CAAC;wBAEF,qBAAM,MAAM,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,8BAAqB,CAAC,EAAA;;wBAAjE,SAAiE,CAAC;wBAClE,qBAAM,MAAM,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,YAAY,CACxC,mFAAmF,CACpF,EAAA;;wBAFD,SAEC,CAAC;;;;aACH,CAAC,CAAC;QAEH,IAAI,CAAC,qCAAqC,EAAE;;;;;wBACpC,GAAG,GAAG,kBAAkB,CAAC,cAAM,OAAA,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,EAAnB,CAAmB,CAAC,CAAC;wBACpD,OAAO,GAAG;;;4CACd,qBAAM,IAAA,mCAAgB,EAAC,GAAG,CAAC,CAAC;4CAC1B,IAAI,EAAE,iBAAiB;4CACvB,MAAM,EAAE,YAAY;4CACpB,aAAa;4CACb,OAAO,EAAE,UAAU;yCACpB,CAAC,EAAA;;wCALF,SAKE,CAAC;;;;6BACJ,CAAC;wBAEF,qBAAM,MAAM,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,8BAAqB,CAAC,EAAA;;wBAAjE,SAAiE,CAAC;wBAClE,qBAAM,MAAM,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,YAAY,CACxC,sFAAsF,CACvF,EAAA;;wBAFD,SAEC,CAAC;;;;aACH,CAAC,CAAC;QAEH,IAAI,CAAC,wCAAwC,EAAE;;;;;wBACvC,GAAG,GAAG,kBAAkB,CAAC,cAAM,OAAA,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,EAAnB,CAAmB,CAAC,CAAC;wBACpD,OAAO,GAAG;;;4CACd,qBAAM,IAAA,mCAAgB,EAAC,GAAG,CAAC,CAAC;4CAC1B,IAAI,EAAE,iBAAiB;4CACvB,aAAa;4CACb,UAAU,EAAE,YAAY;yCACzB,CAAC,EAAA;;wCAJF,SAIE,CAAC;;;;6BACJ,CAAC;wBAEF,qBAAM,MAAM,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,8BAAqB,CAAC,EAAA;;wBAAjE,SAAiE,CAAC;wBAClE,qBAAM,MAAM,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,YAAY,CACxC,yFAAyF,CAC1F,EAAA;;wBAFD,SAEC,CAAC;;;;aACH,CAAC,CAAC;QAEH,IAAI,CAAC,gCAAgC,EAAE;;;;;wBAC/B,GAAG,GAAG,kBAAkB,CAAC,cAAM,OAAA,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,EAAnB,CAAmB,CAAC,CAAC;wBACpD,OAAO,GAAG;;;4CACd,qBAAM,IAAA,mCAAgB,EAAC,GAAG,CAAC,CAAC;4CAC1B,IAAI,EAAE,EAAE;4CACR,MAAM,EAAE,YAAY;yCACrB,CAAC,EAAA;;wCAHF,SAGE,CAAC;;;;6BACJ,CAAC;wBAEF,qBAAM,MAAM,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,8BAAqB,CAAC,EAAA;;wBAAjE,SAAiE,CAAC;wBAClE,qBAAM,MAAM,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,YAAY,CACxC,8EAA8E,CAC/E,EAAA;;wBAFD,SAEC,CAAC;;;;aACH,CAAC,CAAC;QAEH,IAAI,CAAC,kCAAkC,EAAE;;;;;wBACjC,GAAG,GAAG,kBAAkB,CAAC,cAAM,OAAA,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,EAAnB,CAAmB,CAAC,CAAC;wBACpD,OAAO,GAAG;;;;oCACd,aAAa;oCACb,qBAAM,IAAA,mCAAgB,EAAC,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,iBAAiB,EAAE,CAAC,EAAA;;wCADxD,aAAa;wCACb,SAAwD,CAAC;;;;6BAC1D,CAAC;wBAEF,qBAAM,MAAM,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,8BAAqB,CAAC,EAAA;;wBAAjE,SAAiE,CAAC;wBAClE,qBAAM,MAAM,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,YAAY,CACxC,gFAAgF,CACjF,EAAA;;wBAFD,SAEC,CAAC;;;;aACH,CAAC,CAAC;QAEH,IAAI,CAAC,2BAA2B,EAAE;;;;;wBAC1B,GAAG,GAAG,kBAAkB,CAAC,cAAM,OAAA,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,EAAnB,CAAmB,CAAC,CAAC;wBACpD,OAAO,GAAG;;;4CACd,qBAAM,IAAA,mCAAgB,EAAC,GAAG,CAAC,CAAC;4CAC1B,IAAI,EAAE,iBAAiB;4CACvB,MAAM,EAAE,EAAE;yCACX,CAAC,EAAA;;wCAHF,SAGE,CAAC;;;;6BACJ,CAAC;wBAEF,qBAAM,MAAM,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,8BAAqB,CAAC,EAAA;;wBAAjE,SAAiE,CAAC;wBAClE,qBAAM,MAAM,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,YAAY,CACxC,gFAAgF,CACjF,EAAA;;wBAFD,SAEC,CAAC;;;;aACH,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,8CAA8C,EAAE;;;;;oBAC7C,eAAe,GAAG;wBACtB,IAAI,EAAE,iBAAiB;wBACvB,IAAI,EAAE,KAAK;wBACX,MAAM,EAAE,cAAc;wBACtB,SAAS,EAAE,CAAC;wBACZ,WAAW,EAAE,EAAE;wBACf,WAAW,EAAE,cAAc;qBAC5B,CAAC;oBACI,GAAG,GAAG,kBAAkB,CAAC,cAAM,OAAA,OAAO,CAAC,OAAO,CAAC,eAAe,CAAC,EAAhC,CAAgC,CAAC,CAAC;oBACtD,qBAAM,IAAA,mCAAgB,EAAC,GAAG,CAAC,CAAC;4BAC3C,IAAI,EAAE,iBAAiB;4BACvB,MAAM,EAAE,YAAY;yBACrB,CAAC,EAAA;;oBAHI,QAAQ,GAAG,SAGf;oBAEF,MAAM,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;oBAC1C,MAAM,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC,oBAAoB,CAAC;wBAChD,uBAAuB,EAAE;4BACvB,IAAI,EAAE,iBAAiB;4BACvB,MAAM,EAAE,YAAY;yBACrB;qBACF,CAAC,CAAC;;;;SACJ,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}
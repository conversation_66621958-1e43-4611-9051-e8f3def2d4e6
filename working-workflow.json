{"name": "GUARANTEED Working Bot", "nodes": [{"parameters": {"httpMethod": "POST", "path": "guaranteed-bot", "options": {}}, "id": "webhook1", "name": "Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [300, 300]}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"success\": true,\n  \"message\": \"🎉 I AM WORKING! You said: {{ $json.body.message || 'Hello' }}\",\n  \"webhook\": \"http://localhost:2410/webhook/guaranteed-bot\",\n  \"timestamp\": \"{{ new Date().toISOString() }}\"\n}"}, "id": "respond1", "name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [500, 300]}], "connections": {"Webhook": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}}, "settings": {"timezone": "UTC"}}
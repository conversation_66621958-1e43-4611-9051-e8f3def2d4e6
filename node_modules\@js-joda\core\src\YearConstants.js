/*
 * @copyright (c) 2016, <PERSON> & Pat<PERSON><PERSON>
 * @license BSD-3-Clause (see LICENSE.md in the root directory of this source tree)
 */

/**
 * attempt to avoid dependency cycles... define all constants here and they could be used
 * so instead of using e.g. Year.MAX_VALUE we could use YearConstants.MAX_VALUE to avoid the cycle
 */
export class YearConstants {}

export function _init() {
    /**
     * The minimum supported year
     */
    YearConstants.MIN_VALUE = -999999;
    /**
     * The maximum supported year
     */
    YearConstants.MAX_VALUE = 999999;
}

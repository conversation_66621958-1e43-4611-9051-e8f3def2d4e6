{"version": 3, "file": "StorageSharedKeyCredentialPolicyV2.js", "sourceRoot": "", "sources": ["../../../../src/policies/StorageSharedKeyCredentialPolicyV2.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,UAAU,EAAE,MAAM,QAAQ,CAAC;AAOpC,OAAO,EAAE,eAAe,EAAE,MAAM,oBAAoB,CAAC;AACrD,OAAO,EAAE,UAAU,EAAE,aAAa,EAAE,MAAM,uBAAuB,CAAC;AAClE,OAAO,EAAE,aAAa,EAAE,MAAM,8BAA8B,CAAC;AAE7D;;GAEG;AACH,MAAM,CAAC,MAAM,oCAAoC,GAAG,kCAAkC,CAAC;AAUvF;;GAEG;AACH,MAAM,UAAU,gCAAgC,CAC9C,OAAgD;IAEhD,SAAS,WAAW,CAAC,OAAwB;QAC3C,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,CAAC;QAEzE,IACE,OAAO,CAAC,IAAI;YACZ,CAAC,OAAO,OAAO,CAAC,IAAI,KAAK,QAAQ,IAAI,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACnE,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EACvB,CAAC;YACD,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,cAAc,EAAE,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;QACvF,CAAC;QAED,MAAM,YAAY,GAChB;YACE,OAAO,CAAC,MAAM,CAAC,WAAW,EAAE;YAC5B,oBAAoB,CAAC,OAAO,EAAE,eAAe,CAAC,gBAAgB,CAAC;YAC/D,oBAAoB,CAAC,OAAO,EAAE,eAAe,CAAC,gBAAgB,CAAC;YAC/D,oBAAoB,CAAC,OAAO,EAAE,eAAe,CAAC,cAAc,CAAC;YAC7D,oBAAoB,CAAC,OAAO,EAAE,eAAe,CAAC,WAAW,CAAC;YAC1D,oBAAoB,CAAC,OAAO,EAAE,eAAe,CAAC,YAAY,CAAC;YAC3D,oBAAoB,CAAC,OAAO,EAAE,eAAe,CAAC,IAAI,CAAC;YACnD,oBAAoB,CAAC,OAAO,EAAE,eAAe,CAAC,iBAAiB,CAAC;YAChE,oBAAoB,CAAC,OAAO,EAAE,eAAe,CAAC,QAAQ,CAAC;YACvD,oBAAoB,CAAC,OAAO,EAAE,eAAe,CAAC,aAAa,CAAC;YAC5D,oBAAoB,CAAC,OAAO,EAAE,eAAe,CAAC,mBAAmB,CAAC;YAClE,oBAAoB,CAAC,OAAO,EAAE,eAAe,CAAC,KAAK,CAAC;SACrD,CAAC,IAAI,CAAC,IAAI,CAAC;YACZ,IAAI;YACJ,6BAA6B,CAAC,OAAO,CAAC;YACtC,8BAA8B,CAAC,OAAO,CAAC,CAAC;QAE1C,MAAM,SAAS,GAAW,UAAU,CAAC,QAAQ,EAAE,OAAO,CAAC,UAAU,CAAC;aAC/D,MAAM,CAAC,YAAY,EAAE,MAAM,CAAC;aAC5B,MAAM,CAAC,QAAQ,CAAC,CAAC;QACpB,OAAO,CAAC,OAAO,CAAC,GAAG,CACjB,eAAe,CAAC,aAAa,EAC7B,aAAa,OAAO,CAAC,WAAW,IAAI,SAAS,EAAE,CAChD,CAAC;QAEF,uCAAuC;QACvC,0DAA0D;QAC1D,mEAAmE;QACnE,+EAA+E;IACjF,CAAC;IAED;;;OAGG;IACH,SAAS,oBAAoB,CAAC,OAAwB,EAAE,UAAkB;QACxE,MAAM,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAE9C,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,0EAA0E;QAC1E,sEAAsE;QACtE,yFAAyF;QACzF,IAAI,UAAU,KAAK,eAAe,CAAC,cAAc,IAAI,KAAK,KAAK,GAAG,EAAE,CAAC;YACnE,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;;;;;;;;;;OAWG;IACH,SAAS,6BAA6B,CAAC,OAAwB;QAC7D,IAAI,YAAY,GAA2C,EAAE,CAAC;QAC9D,KAAK,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;YAC5C,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC,UAAU,CAAC,eAAe,CAAC,kBAAkB,CAAC,EAAE,CAAC;gBACtE,YAAY,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC;YACrC,CAAC;QACH,CAAC;QAED,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAU,EAAE;YACjC,OAAO,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;QAEH,2BAA2B;QAC3B,YAAY,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE;YACzD,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;gBAClF,OAAO,KAAK,CAAC;YACf,CAAC;YACD,OAAO,IAAI,CAAC;QACd,CAAC,CAAC,CAAC;QAEH,IAAI,gCAAgC,GAAW,EAAE,CAAC;QAClD,YAAY,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;YAC9B,gCAAgC,IAAI,GAAG,MAAM,CAAC,IAAI;iBAC/C,WAAW,EAAE;iBACb,SAAS,EAAE,IAAI,MAAM,CAAC,KAAK,CAAC,QAAQ,EAAE,IAAI,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,OAAO,gCAAgC,CAAC;IAC1C,CAAC;IAED,SAAS,8BAA8B,CAAC,OAAwB;QAC9D,MAAM,IAAI,GAAG,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC;QAE5C,IAAI,2BAA2B,GAAW,EAAE,CAAC;QAC7C,2BAA2B,IAAI,IAAI,OAAO,CAAC,WAAW,GAAG,IAAI,EAAE,CAAC;QAEhE,MAAM,OAAO,GAAG,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAC3C,MAAM,gBAAgB,GAA8B,EAAE,CAAC;QACvD,IAAI,OAAO,EAAE,CAAC;YACZ,MAAM,SAAS,GAAa,EAAE,CAAC;YAC/B,KAAK,MAAM,GAAG,IAAI,OAAO,EAAE,CAAC;gBAC1B,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,EAAE,CAAC;oBACvD,MAAM,YAAY,GAAG,GAAG,CAAC,WAAW,EAAE,CAAC;oBACvC,gBAAgB,CAAC,YAAY,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;oBAC9C,SAAS,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBAC/B,CAAC;YACH,CAAC;YAED,SAAS,CAAC,IAAI,EAAE,CAAC;YACjB,KAAK,MAAM,GAAG,IAAI,SAAS,EAAE,CAAC;gBAC5B,2BAA2B,IAAI,KAAK,GAAG,IAAI,kBAAkB,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;YACzF,CAAC;QACH,CAAC;QAED,OAAO,2BAA2B,CAAC;IACrC,CAAC;IAED,OAAO;QACL,IAAI,EAAE,oCAAoC;QAC1C,KAAK,CAAC,WAAW,CAAC,OAAwB,EAAE,IAAiB;YAC3D,WAAW,CAAC,OAAO,CAAC,CAAC;YACrB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;QACvB,CAAC;KACF,CAAC;AACJ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport { createHmac } from \"crypto\";\nimport type {\n  PipelineRequest,\n  PipelineResponse,\n  SendRequest,\n  PipelinePolicy,\n} from \"@azure/core-rest-pipeline\";\nimport { HeaderConstants } from \"../utils/constants\";\nimport { getURLPath, getURLQueries } from \"../utils/utils.common\";\nimport { compareHeader } from \"../utils/SharedKeyComparator\";\n\n/**\n * The programmatic identifier of the storageSharedKeyCredentialPolicy.\n */\nexport const storageSharedKeyCredentialPolicyName = \"storageSharedKeyCredentialPolicy\";\n\n/**\n * Options used to configure StorageSharedKeyCredentialPolicy.\n */\nexport interface StorageSharedKeyCredentialPolicyOptions {\n  accountName: string;\n  accountKey: Buffer;\n}\n\n/**\n * storageSharedKeyCredentialPolicy handles signing requests using storage account keys.\n */\nexport function storageSharedKeyCredentialPolicy(\n  options: StorageSharedKeyCredentialPolicyOptions,\n): PipelinePolicy {\n  function signRequest(request: PipelineRequest): void {\n    request.headers.set(HeaderConstants.X_MS_DATE, new Date().toUTCString());\n\n    if (\n      request.body &&\n      (typeof request.body === \"string\" || Buffer.isBuffer(request.body)) &&\n      request.body.length > 0\n    ) {\n      request.headers.set(HeaderConstants.CONTENT_LENGTH, Buffer.byteLength(request.body));\n    }\n\n    const stringToSign: string =\n      [\n        request.method.toUpperCase(),\n        getHeaderValueToSign(request, HeaderConstants.CONTENT_LANGUAGE),\n        getHeaderValueToSign(request, HeaderConstants.CONTENT_ENCODING),\n        getHeaderValueToSign(request, HeaderConstants.CONTENT_LENGTH),\n        getHeaderValueToSign(request, HeaderConstants.CONTENT_MD5),\n        getHeaderValueToSign(request, HeaderConstants.CONTENT_TYPE),\n        getHeaderValueToSign(request, HeaderConstants.DATE),\n        getHeaderValueToSign(request, HeaderConstants.IF_MODIFIED_SINCE),\n        getHeaderValueToSign(request, HeaderConstants.IF_MATCH),\n        getHeaderValueToSign(request, HeaderConstants.IF_NONE_MATCH),\n        getHeaderValueToSign(request, HeaderConstants.IF_UNMODIFIED_SINCE),\n        getHeaderValueToSign(request, HeaderConstants.RANGE),\n      ].join(\"\\n\") +\n      \"\\n\" +\n      getCanonicalizedHeadersString(request) +\n      getCanonicalizedResourceString(request);\n\n    const signature: string = createHmac(\"sha256\", options.accountKey)\n      .update(stringToSign, \"utf8\")\n      .digest(\"base64\");\n    request.headers.set(\n      HeaderConstants.AUTHORIZATION,\n      `SharedKey ${options.accountName}:${signature}`,\n    );\n\n    // console.log(`[URL]:${request.url}`);\n    // console.log(`[HEADERS]:${request.headers.toString()}`);\n    // console.log(`[STRING TO SIGN]:${JSON.stringify(stringToSign)}`);\n    // console.log(`[KEY]: ${request.headers.get(HeaderConstants.AUTHORIZATION)}`);\n  }\n\n  /**\n   * Retrieve header value according to shared key sign rules.\n   * @see https://docs.microsoft.com/en-us/rest/api/storageservices/authenticate-with-shared-key\n   */\n  function getHeaderValueToSign(request: PipelineRequest, headerName: string): string {\n    const value = request.headers.get(headerName);\n\n    if (!value) {\n      return \"\";\n    }\n\n    // When using version 2015-02-21 or later, if Content-Length is zero, then\n    // set the Content-Length part of the StringToSign to an empty string.\n    // https://docs.microsoft.com/en-us/rest/api/storageservices/authenticate-with-shared-key\n    if (headerName === HeaderConstants.CONTENT_LENGTH && value === \"0\") {\n      return \"\";\n    }\n\n    return value;\n  }\n\n  /**\n   * To construct the CanonicalizedHeaders portion of the signature string, follow these steps:\n   * 1. Retrieve all headers for the resource that begin with x-ms-, including the x-ms-date header.\n   * 2. Convert each HTTP header name to lowercase.\n   * 3. Sort the headers lexicographically by header name, in ascending order.\n   *    Each header may appear only once in the string.\n   * 4. Replace any linear whitespace in the header value with a single space.\n   * 5. Trim any whitespace around the colon in the header.\n   * 6. Finally, append a new-line character to each canonicalized header in the resulting list.\n   *    Construct the CanonicalizedHeaders string by concatenating all headers in this list into a single string.\n   *\n   */\n  function getCanonicalizedHeadersString(request: PipelineRequest): string {\n    let headersArray: Array<{ name: string; value: string }> = [];\n    for (const [name, value] of request.headers) {\n      if (name.toLowerCase().startsWith(HeaderConstants.PREFIX_FOR_STORAGE)) {\n        headersArray.push({ name, value });\n      }\n    }\n\n    headersArray.sort((a, b): number => {\n      return compareHeader(a.name.toLowerCase(), b.name.toLowerCase());\n    });\n\n    // Remove duplicate headers\n    headersArray = headersArray.filter((value, index, array) => {\n      if (index > 0 && value.name.toLowerCase() === array[index - 1].name.toLowerCase()) {\n        return false;\n      }\n      return true;\n    });\n\n    let canonicalizedHeadersStringToSign: string = \"\";\n    headersArray.forEach((header) => {\n      canonicalizedHeadersStringToSign += `${header.name\n        .toLowerCase()\n        .trimRight()}:${header.value.trimLeft()}\\n`;\n    });\n\n    return canonicalizedHeadersStringToSign;\n  }\n\n  function getCanonicalizedResourceString(request: PipelineRequest): string {\n    const path = getURLPath(request.url) || \"/\";\n\n    let canonicalizedResourceString: string = \"\";\n    canonicalizedResourceString += `/${options.accountName}${path}`;\n\n    const queries = getURLQueries(request.url);\n    const lowercaseQueries: { [key: string]: string } = {};\n    if (queries) {\n      const queryKeys: string[] = [];\n      for (const key in queries) {\n        if (Object.prototype.hasOwnProperty.call(queries, key)) {\n          const lowercaseKey = key.toLowerCase();\n          lowercaseQueries[lowercaseKey] = queries[key];\n          queryKeys.push(lowercaseKey);\n        }\n      }\n\n      queryKeys.sort();\n      for (const key of queryKeys) {\n        canonicalizedResourceString += `\\n${key}:${decodeURIComponent(lowercaseQueries[key])}`;\n      }\n    }\n\n    return canonicalizedResourceString;\n  }\n\n  return {\n    name: storageSharedKeyCredentialPolicyName,\n    async sendRequest(request: PipelineRequest, next: SendRequest): Promise<PipelineResponse> {\n      signRequest(request);\n      return next(request);\n    },\n  };\n}\n"]}
"use strict";
/* tslint:disable */
/* eslint-disable */
/**
 * Pinecone Inference API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmbeddingsListToJSON = exports.EmbeddingsListFromJSONTyped = exports.EmbeddingsListFromJSON = exports.instanceOfEmbeddingsList = void 0;
var Embedding_1 = require("./Embedding");
var EmbeddingsListUsage_1 = require("./EmbeddingsListUsage");
/**
 * Check if a given object implements the EmbeddingsList interface.
 */
function instanceOfEmbeddingsList(value) {
    var isInstance = true;
    isInstance = isInstance && "model" in value;
    isInstance = isInstance && "data" in value;
    isInstance = isInstance && "usage" in value;
    return isInstance;
}
exports.instanceOfEmbeddingsList = instanceOfEmbeddingsList;
function EmbeddingsListFromJSON(json) {
    return EmbeddingsListFromJSONTyped(json, false);
}
exports.EmbeddingsListFromJSON = EmbeddingsListFromJSON;
function EmbeddingsListFromJSONTyped(json, ignoreDiscriminator) {
    if ((json === undefined) || (json === null)) {
        return json;
    }
    return {
        'model': json['model'],
        'data': (json['data'].map(Embedding_1.EmbeddingFromJSON)),
        'usage': (0, EmbeddingsListUsage_1.EmbeddingsListUsageFromJSON)(json['usage']),
    };
}
exports.EmbeddingsListFromJSONTyped = EmbeddingsListFromJSONTyped;
function EmbeddingsListToJSON(value) {
    if (value === undefined) {
        return undefined;
    }
    if (value === null) {
        return null;
    }
    return {
        'model': value.model,
        'data': (value.data.map(Embedding_1.EmbeddingToJSON)),
        'usage': (0, EmbeddingsListUsage_1.EmbeddingsListUsageToJSON)(value.usage),
    };
}
exports.EmbeddingsListToJSON = EmbeddingsListToJSON;
//# sourceMappingURL=EmbeddingsList.js.map
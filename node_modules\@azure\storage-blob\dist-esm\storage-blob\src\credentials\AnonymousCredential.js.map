{"version": 3, "file": "AnonymousCredential.js", "sourceRoot": "", "sources": ["../../../../src/credentials/AnonymousCredential.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAOlC,OAAO,EAAE,yBAAyB,EAAE,MAAM,uCAAuC,CAAC;AAClF,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAE1C;;;;;GAKG;AACH,MAAM,OAAO,mBAAoB,SAAQ,UAAU;IACjD;;;;;OAKG;IACI,MAAM,CACX,UAAyB,EACzB,OAA6B;QAE7B,OAAO,IAAI,yBAAyB,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;IAC5D,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type {\n  RequestPolicy,\n  RequestPolicyOptionsLike as RequestPolicyOptions,\n} from \"@azure/core-http-compat\";\n\nimport { AnonymousCredentialPolicy } from \"../policies/AnonymousCredentialPolicy\";\nimport { Credential } from \"./Credential\";\n\n/**\n * AnonymousCredential provides a credentialPolicyCreator member used to create\n * AnonymousCredentialPolicy objects. AnonymousCredentialPolicy is used with\n * HTTP(S) requests that read public resources or for use with Shared Access\n * Signatures (SAS).\n */\nexport class AnonymousCredential extends Credential {\n  /**\n   * Creates an {@link AnonymousCredentialPolicy} object.\n   *\n   * @param nextPolicy -\n   * @param options -\n   */\n  public create(\n    nextPolicy: RequestPolicy,\n    options: RequestPolicyOptions,\n  ): AnonymousCredentialPolicy {\n    return new AnonymousCredentialPolicy(nextPolicy, options);\n  }\n}\n"]}
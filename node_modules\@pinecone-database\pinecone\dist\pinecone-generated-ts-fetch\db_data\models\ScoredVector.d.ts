/**
 * Pinecone Data Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import type { SparseValues } from './SparseValues';
/**
 *
 * @export
 * @interface ScoredVector
 */
export interface ScoredVector {
    /**
     * This is the vector's unique id.
     * @type {string}
     * @memberof ScoredVector
     */
    id: string;
    /**
     * This is a measure of similarity between this vector and the query vector.  The higher the score, the more they are similar.
     * @type {number}
     * @memberof ScoredVector
     */
    score?: number;
    /**
     * This is the vector data, if it is requested.
     * @type {Array<number>}
     * @memberof ScoredVector
     */
    values?: Array<number>;
    /**
     *
     * @type {SparseValues}
     * @memberof ScoredVector
     */
    sparseValues?: SparseValues;
    /**
     * This is the metadata, if it is requested.
     * @type {object}
     * @memberof ScoredVector
     */
    metadata?: object;
}
/**
 * Check if a given object implements the ScoredVector interface.
 */
export declare function instanceOfScoredVector(value: object): boolean;
export declare function ScoredVectorFromJSON(json: any): ScoredVector;
export declare function ScoredVectorFromJSONTyped(json: any, ignoreDiscriminator: boolean): ScoredVector;
export declare function ScoredVectorToJSON(value?: ScoredVector | null): any;

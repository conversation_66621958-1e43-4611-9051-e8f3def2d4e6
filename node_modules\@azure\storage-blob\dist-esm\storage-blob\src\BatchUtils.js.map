{"version": 3, "file": "BatchUtils.js", "sourceRoot": "", "sources": ["../../../src/BatchUtils.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EAAE,eAAe,EAAE,MAAM,oBAAoB,CAAC;AACrD,OAAO,EAAE,0BAA0B,EAAE,MAAM,mBAAmB,CAAC;AAE/D,MAAM,CAAC,KAAK,UAAU,aAAa,CACjC,aAA8C;IAE9C,IAAI,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,0BAA0B,CAAC,CAAC;IAEtD,MAAM,cAAc,GAAG,MAAM,eAAe,CAC1C,aAAa,CAAC,kBAA2C,EACzD,MAAM,CACP,CAAC;IAEF,6CAA6C;IAC7C,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC;IAEzC,OAAO,MAAM,CAAC,QAAQ,EAAE,CAAC;AAC3B,CAAC;AAED,MAAM,UAAU,cAAc,CAAC,GAAW;IACxC,OAAO,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;AAChC,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { ServiceSubmitBatchResponseModel } from \"./generatedModels\";\nimport { streamToBuffer2 } from \"./utils/utils.node\";\nimport { BATCH_MAX_PAYLOAD_IN_BYTES } from \"./utils/constants\";\n\nexport async function getBodyAsText(\n  batchResponse: ServiceSubmitBatchResponseModel,\n): Promise<string> {\n  let buffer = Buffer.alloc(BATCH_MAX_PAYLOAD_IN_BYTES);\n\n  const responseLength = await streamToBuffer2(\n    batchResponse.readableStreamBody as NodeJS.ReadableStream,\n    buffer,\n  );\n\n  // Slice the buffer to trim the empty ending.\n  buffer = buffer.slice(0, responseLength);\n\n  return buffer.toString();\n}\n\nexport function utf8ByteLength(str: string): number {\n  return Buffer.byteLength(str);\n}\n"]}
/**
 * Pinecone Control Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import * as runtime from '../runtime';
import type { CollectionList, CollectionModel, ConfigureIndexRequest, CreateCollectionRequest, CreateIndexRequest, IndexList, IndexModel } from '../models/index';
export interface ConfigureIndexOperationRequest {
    indexName: string;
    configureIndexRequest: ConfigureIndexRequest;
}
export interface CreateCollectionOperationRequest {
    createCollectionRequest: CreateCollectionRequest;
}
export interface CreateIndexOperationRequest {
    createIndexRequest: CreateIndexRequest;
}
export interface DeleteCollectionRequest {
    collectionName: string;
}
export interface DeleteIndexRequest {
    indexName: string;
}
export interface DescribeCollectionRequest {
    collectionName: string;
}
export interface DescribeIndexRequest {
    indexName: string;
}
/**
 *
 */
export declare class ManageIndexesApi extends runtime.BaseAPI {
    /**
     * This operation configures an existing index.   For serverless indexes, you can configure only index deletion protection and tags. For pod-based indexes, you can configure the pod size, number of replicas, tags, and index deletion protection.   It is not possible to change the pod type of a pod-based index. However, you can create a collection from a pod-based index and then [create a new pod-based index with a different pod type](http://docs.pinecone.io/guides/indexes/create-an-index#create-an-index-from-a-collection) from the collection. For guidance and examples, see [Configure an index](http://docs.pinecone.io/guides/indexes/configure-an-index).
     * Configure an index
     */
    configureIndexRaw(requestParameters: ConfigureIndexOperationRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<IndexModel>>;
    /**
     * This operation configures an existing index.   For serverless indexes, you can configure only index deletion protection and tags. For pod-based indexes, you can configure the pod size, number of replicas, tags, and index deletion protection.   It is not possible to change the pod type of a pod-based index. However, you can create a collection from a pod-based index and then [create a new pod-based index with a different pod type](http://docs.pinecone.io/guides/indexes/create-an-index#create-an-index-from-a-collection) from the collection. For guidance and examples, see [Configure an index](http://docs.pinecone.io/guides/indexes/configure-an-index).
     * Configure an index
     */
    configureIndex(requestParameters: ConfigureIndexOperationRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<IndexModel>;
    /**
     * This operation creates a Pinecone collection.    Serverless indexes do not support collections.
     * Create a collection
     */
    createCollectionRaw(requestParameters: CreateCollectionOperationRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<CollectionModel>>;
    /**
     * This operation creates a Pinecone collection.    Serverless indexes do not support collections.
     * Create a collection
     */
    createCollection(requestParameters: CreateCollectionOperationRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<CollectionModel>;
    /**
     * This operation deploys a Pinecone index. This is where you specify the measure of similarity, the dimension of vectors to be stored in the index, which cloud provider you would like to deploy with, and more.    For guidance and examples, see [Create an index](https://docs.pinecone.io/guides/indexes/create-an-index#create-a-serverless-index).
     * Create an index
     */
    createIndexRaw(requestParameters: CreateIndexOperationRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<IndexModel>>;
    /**
     * This operation deploys a Pinecone index. This is where you specify the measure of similarity, the dimension of vectors to be stored in the index, which cloud provider you would like to deploy with, and more.    For guidance and examples, see [Create an index](https://docs.pinecone.io/guides/indexes/create-an-index#create-a-serverless-index).
     * Create an index
     */
    createIndex(requestParameters: CreateIndexOperationRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<IndexModel>;
    /**
     * This operation deletes an existing collection. Serverless indexes do not support collections.
     * Delete a collection
     */
    deleteCollectionRaw(requestParameters: DeleteCollectionRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<void>>;
    /**
     * This operation deletes an existing collection. Serverless indexes do not support collections.
     * Delete a collection
     */
    deleteCollection(requestParameters: DeleteCollectionRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<void>;
    /**
     * This operation deletes an existing index.
     * Delete an index
     */
    deleteIndexRaw(requestParameters: DeleteIndexRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<void>>;
    /**
     * This operation deletes an existing index.
     * Delete an index
     */
    deleteIndex(requestParameters: DeleteIndexRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<void>;
    /**
     * This operation gets a description of a collection. Serverless indexes do not support collections.
     * Describe a collection
     */
    describeCollectionRaw(requestParameters: DescribeCollectionRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<CollectionModel>>;
    /**
     * This operation gets a description of a collection. Serverless indexes do not support collections.
     * Describe a collection
     */
    describeCollection(requestParameters: DescribeCollectionRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<CollectionModel>;
    /**
     * Get a description of an index.
     * Describe an index
     */
    describeIndexRaw(requestParameters: DescribeIndexRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<IndexModel>>;
    /**
     * Get a description of an index.
     * Describe an index
     */
    describeIndex(requestParameters: DescribeIndexRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<IndexModel>;
    /**
     * This operation returns a list of all collections in a project. Serverless indexes do not support collections.
     * List collections
     */
    listCollectionsRaw(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<CollectionList>>;
    /**
     * This operation returns a list of all collections in a project. Serverless indexes do not support collections.
     * List collections
     */
    listCollections(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<CollectionList>;
    /**
     * This operation returns a list of all indexes in a project.
     * List indexes
     */
    listIndexesRaw(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<IndexList>>;
    /**
     * This operation returns a list of all indexes in a project.
     * List indexes
     */
    listIndexes(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<IndexList>;
}

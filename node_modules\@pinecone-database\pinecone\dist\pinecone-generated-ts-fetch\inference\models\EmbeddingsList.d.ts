/**
 * Pinecone Inference API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import type { Embedding } from './Embedding';
import type { EmbeddingsListUsage } from './EmbeddingsListUsage';
/**
 * Embeddings generated for the input
 * @export
 * @interface EmbeddingsList
 */
export interface EmbeddingsList {
    /**
     * The model used to generate the embeddings
     * @type {string}
     * @memberof EmbeddingsList
     */
    model: string;
    /**
     * The embeddings generated for the inputs.
     * @type {Array<Embedding>}
     * @memberof EmbeddingsList
     */
    data: Array<Embedding>;
    /**
     *
     * @type {EmbeddingsListUsage}
     * @memberof EmbeddingsList
     */
    usage: EmbeddingsListUsage;
}
/**
 * Check if a given object implements the EmbeddingsList interface.
 */
export declare function instanceOfEmbeddingsList(value: object): boolean;
export declare function EmbeddingsListFromJSON(json: any): EmbeddingsList;
export declare function EmbeddingsListFromJSONTyped(json: any, ignoreDiscriminator: boolean): EmbeddingsList;
export declare function EmbeddingsListToJSON(value?: EmbeddingsList | null): any;

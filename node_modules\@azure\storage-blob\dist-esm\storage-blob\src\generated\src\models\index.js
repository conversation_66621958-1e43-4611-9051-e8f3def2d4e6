/*
 * Copyright (c) Microsoft Corporation.
 * Licensed under the MIT License.
 *
 * Code generated by Microsoft (R) AutoRest Code Generator.
 * Changes may cause incorrect behavior and will be lost if the code is regenerated.
 */
/** Known values of {@link EncryptionAlgorithmType} that the service accepts. */
export var KnownEncryptionAlgorithmType;
(function (KnownEncryptionAlgorithmType) {
    /** AES256 */
    KnownEncryptionAlgorithmType["AES256"] = "AES256";
})(KnownEncryptionAlgorithmType || (KnownEncryptionAlgorithmType = {}));
/** Known values of {@link BlobExpiryOptions} that the service accepts. */
export var KnownBlobExpiryOptions;
(function (KnownBlobExpiryOptions) {
    /** NeverExpire */
    KnownBlobExpiryOptions["NeverExpire"] = "NeverExpire";
    /** RelativeToCreation */
    KnownBlobExpiryOptions["RelativeToCreation"] = "RelativeToCreation";
    /** RelativeToNow */
    KnownBlobExpiryOptions["RelativeToNow"] = "RelativeToNow";
    /** Absolute */
    KnownBlobExpiryOptions["Absolute"] = "Absolute";
})(KnownBlobExpiryOptions || (KnownBlobExpiryOptions = {}));
/** Known values of {@link StorageErrorCode} that the service accepts. */
export var KnownStorageErrorCode;
(function (KnownStorageErrorCode) {
    /** AccountAlreadyExists */
    KnownStorageErrorCode["AccountAlreadyExists"] = "AccountAlreadyExists";
    /** AccountBeingCreated */
    KnownStorageErrorCode["AccountBeingCreated"] = "AccountBeingCreated";
    /** AccountIsDisabled */
    KnownStorageErrorCode["AccountIsDisabled"] = "AccountIsDisabled";
    /** AuthenticationFailed */
    KnownStorageErrorCode["AuthenticationFailed"] = "AuthenticationFailed";
    /** AuthorizationFailure */
    KnownStorageErrorCode["AuthorizationFailure"] = "AuthorizationFailure";
    /** ConditionHeadersNotSupported */
    KnownStorageErrorCode["ConditionHeadersNotSupported"] = "ConditionHeadersNotSupported";
    /** ConditionNotMet */
    KnownStorageErrorCode["ConditionNotMet"] = "ConditionNotMet";
    /** EmptyMetadataKey */
    KnownStorageErrorCode["EmptyMetadataKey"] = "EmptyMetadataKey";
    /** InsufficientAccountPermissions */
    KnownStorageErrorCode["InsufficientAccountPermissions"] = "InsufficientAccountPermissions";
    /** InternalError */
    KnownStorageErrorCode["InternalError"] = "InternalError";
    /** InvalidAuthenticationInfo */
    KnownStorageErrorCode["InvalidAuthenticationInfo"] = "InvalidAuthenticationInfo";
    /** InvalidHeaderValue */
    KnownStorageErrorCode["InvalidHeaderValue"] = "InvalidHeaderValue";
    /** InvalidHttpVerb */
    KnownStorageErrorCode["InvalidHttpVerb"] = "InvalidHttpVerb";
    /** InvalidInput */
    KnownStorageErrorCode["InvalidInput"] = "InvalidInput";
    /** InvalidMd5 */
    KnownStorageErrorCode["InvalidMd5"] = "InvalidMd5";
    /** InvalidMetadata */
    KnownStorageErrorCode["InvalidMetadata"] = "InvalidMetadata";
    /** InvalidQueryParameterValue */
    KnownStorageErrorCode["InvalidQueryParameterValue"] = "InvalidQueryParameterValue";
    /** InvalidRange */
    KnownStorageErrorCode["InvalidRange"] = "InvalidRange";
    /** InvalidResourceName */
    KnownStorageErrorCode["InvalidResourceName"] = "InvalidResourceName";
    /** InvalidUri */
    KnownStorageErrorCode["InvalidUri"] = "InvalidUri";
    /** InvalidXmlDocument */
    KnownStorageErrorCode["InvalidXmlDocument"] = "InvalidXmlDocument";
    /** InvalidXmlNodeValue */
    KnownStorageErrorCode["InvalidXmlNodeValue"] = "InvalidXmlNodeValue";
    /** Md5Mismatch */
    KnownStorageErrorCode["Md5Mismatch"] = "Md5Mismatch";
    /** MetadataTooLarge */
    KnownStorageErrorCode["MetadataTooLarge"] = "MetadataTooLarge";
    /** MissingContentLengthHeader */
    KnownStorageErrorCode["MissingContentLengthHeader"] = "MissingContentLengthHeader";
    /** MissingRequiredQueryParameter */
    KnownStorageErrorCode["MissingRequiredQueryParameter"] = "MissingRequiredQueryParameter";
    /** MissingRequiredHeader */
    KnownStorageErrorCode["MissingRequiredHeader"] = "MissingRequiredHeader";
    /** MissingRequiredXmlNode */
    KnownStorageErrorCode["MissingRequiredXmlNode"] = "MissingRequiredXmlNode";
    /** MultipleConditionHeadersNotSupported */
    KnownStorageErrorCode["MultipleConditionHeadersNotSupported"] = "MultipleConditionHeadersNotSupported";
    /** OperationTimedOut */
    KnownStorageErrorCode["OperationTimedOut"] = "OperationTimedOut";
    /** OutOfRangeInput */
    KnownStorageErrorCode["OutOfRangeInput"] = "OutOfRangeInput";
    /** OutOfRangeQueryParameterValue */
    KnownStorageErrorCode["OutOfRangeQueryParameterValue"] = "OutOfRangeQueryParameterValue";
    /** RequestBodyTooLarge */
    KnownStorageErrorCode["RequestBodyTooLarge"] = "RequestBodyTooLarge";
    /** ResourceTypeMismatch */
    KnownStorageErrorCode["ResourceTypeMismatch"] = "ResourceTypeMismatch";
    /** RequestUrlFailedToParse */
    KnownStorageErrorCode["RequestUrlFailedToParse"] = "RequestUrlFailedToParse";
    /** ResourceAlreadyExists */
    KnownStorageErrorCode["ResourceAlreadyExists"] = "ResourceAlreadyExists";
    /** ResourceNotFound */
    KnownStorageErrorCode["ResourceNotFound"] = "ResourceNotFound";
    /** ServerBusy */
    KnownStorageErrorCode["ServerBusy"] = "ServerBusy";
    /** UnsupportedHeader */
    KnownStorageErrorCode["UnsupportedHeader"] = "UnsupportedHeader";
    /** UnsupportedXmlNode */
    KnownStorageErrorCode["UnsupportedXmlNode"] = "UnsupportedXmlNode";
    /** UnsupportedQueryParameter */
    KnownStorageErrorCode["UnsupportedQueryParameter"] = "UnsupportedQueryParameter";
    /** UnsupportedHttpVerb */
    KnownStorageErrorCode["UnsupportedHttpVerb"] = "UnsupportedHttpVerb";
    /** AppendPositionConditionNotMet */
    KnownStorageErrorCode["AppendPositionConditionNotMet"] = "AppendPositionConditionNotMet";
    /** BlobAlreadyExists */
    KnownStorageErrorCode["BlobAlreadyExists"] = "BlobAlreadyExists";
    /** BlobImmutableDueToPolicy */
    KnownStorageErrorCode["BlobImmutableDueToPolicy"] = "BlobImmutableDueToPolicy";
    /** BlobNotFound */
    KnownStorageErrorCode["BlobNotFound"] = "BlobNotFound";
    /** BlobOverwritten */
    KnownStorageErrorCode["BlobOverwritten"] = "BlobOverwritten";
    /** BlobTierInadequateForContentLength */
    KnownStorageErrorCode["BlobTierInadequateForContentLength"] = "BlobTierInadequateForContentLength";
    /** BlobUsesCustomerSpecifiedEncryption */
    KnownStorageErrorCode["BlobUsesCustomerSpecifiedEncryption"] = "BlobUsesCustomerSpecifiedEncryption";
    /** BlockCountExceedsLimit */
    KnownStorageErrorCode["BlockCountExceedsLimit"] = "BlockCountExceedsLimit";
    /** BlockListTooLong */
    KnownStorageErrorCode["BlockListTooLong"] = "BlockListTooLong";
    /** CannotChangeToLowerTier */
    KnownStorageErrorCode["CannotChangeToLowerTier"] = "CannotChangeToLowerTier";
    /** CannotVerifyCopySource */
    KnownStorageErrorCode["CannotVerifyCopySource"] = "CannotVerifyCopySource";
    /** ContainerAlreadyExists */
    KnownStorageErrorCode["ContainerAlreadyExists"] = "ContainerAlreadyExists";
    /** ContainerBeingDeleted */
    KnownStorageErrorCode["ContainerBeingDeleted"] = "ContainerBeingDeleted";
    /** ContainerDisabled */
    KnownStorageErrorCode["ContainerDisabled"] = "ContainerDisabled";
    /** ContainerNotFound */
    KnownStorageErrorCode["ContainerNotFound"] = "ContainerNotFound";
    /** ContentLengthLargerThanTierLimit */
    KnownStorageErrorCode["ContentLengthLargerThanTierLimit"] = "ContentLengthLargerThanTierLimit";
    /** CopyAcrossAccountsNotSupported */
    KnownStorageErrorCode["CopyAcrossAccountsNotSupported"] = "CopyAcrossAccountsNotSupported";
    /** CopyIdMismatch */
    KnownStorageErrorCode["CopyIdMismatch"] = "CopyIdMismatch";
    /** FeatureVersionMismatch */
    KnownStorageErrorCode["FeatureVersionMismatch"] = "FeatureVersionMismatch";
    /** IncrementalCopyBlobMismatch */
    KnownStorageErrorCode["IncrementalCopyBlobMismatch"] = "IncrementalCopyBlobMismatch";
    /** IncrementalCopyOfEarlierVersionSnapshotNotAllowed */
    KnownStorageErrorCode["IncrementalCopyOfEarlierVersionSnapshotNotAllowed"] = "IncrementalCopyOfEarlierVersionSnapshotNotAllowed";
    /** IncrementalCopySourceMustBeSnapshot */
    KnownStorageErrorCode["IncrementalCopySourceMustBeSnapshot"] = "IncrementalCopySourceMustBeSnapshot";
    /** InfiniteLeaseDurationRequired */
    KnownStorageErrorCode["InfiniteLeaseDurationRequired"] = "InfiniteLeaseDurationRequired";
    /** InvalidBlobOrBlock */
    KnownStorageErrorCode["InvalidBlobOrBlock"] = "InvalidBlobOrBlock";
    /** InvalidBlobTier */
    KnownStorageErrorCode["InvalidBlobTier"] = "InvalidBlobTier";
    /** InvalidBlobType */
    KnownStorageErrorCode["InvalidBlobType"] = "InvalidBlobType";
    /** InvalidBlockId */
    KnownStorageErrorCode["InvalidBlockId"] = "InvalidBlockId";
    /** InvalidBlockList */
    KnownStorageErrorCode["InvalidBlockList"] = "InvalidBlockList";
    /** InvalidOperation */
    KnownStorageErrorCode["InvalidOperation"] = "InvalidOperation";
    /** InvalidPageRange */
    KnownStorageErrorCode["InvalidPageRange"] = "InvalidPageRange";
    /** InvalidSourceBlobType */
    KnownStorageErrorCode["InvalidSourceBlobType"] = "InvalidSourceBlobType";
    /** InvalidSourceBlobUrl */
    KnownStorageErrorCode["InvalidSourceBlobUrl"] = "InvalidSourceBlobUrl";
    /** InvalidVersionForPageBlobOperation */
    KnownStorageErrorCode["InvalidVersionForPageBlobOperation"] = "InvalidVersionForPageBlobOperation";
    /** LeaseAlreadyPresent */
    KnownStorageErrorCode["LeaseAlreadyPresent"] = "LeaseAlreadyPresent";
    /** LeaseAlreadyBroken */
    KnownStorageErrorCode["LeaseAlreadyBroken"] = "LeaseAlreadyBroken";
    /** LeaseIdMismatchWithBlobOperation */
    KnownStorageErrorCode["LeaseIdMismatchWithBlobOperation"] = "LeaseIdMismatchWithBlobOperation";
    /** LeaseIdMismatchWithContainerOperation */
    KnownStorageErrorCode["LeaseIdMismatchWithContainerOperation"] = "LeaseIdMismatchWithContainerOperation";
    /** LeaseIdMismatchWithLeaseOperation */
    KnownStorageErrorCode["LeaseIdMismatchWithLeaseOperation"] = "LeaseIdMismatchWithLeaseOperation";
    /** LeaseIdMissing */
    KnownStorageErrorCode["LeaseIdMissing"] = "LeaseIdMissing";
    /** LeaseIsBreakingAndCannotBeAcquired */
    KnownStorageErrorCode["LeaseIsBreakingAndCannotBeAcquired"] = "LeaseIsBreakingAndCannotBeAcquired";
    /** LeaseIsBreakingAndCannotBeChanged */
    KnownStorageErrorCode["LeaseIsBreakingAndCannotBeChanged"] = "LeaseIsBreakingAndCannotBeChanged";
    /** LeaseIsBrokenAndCannotBeRenewed */
    KnownStorageErrorCode["LeaseIsBrokenAndCannotBeRenewed"] = "LeaseIsBrokenAndCannotBeRenewed";
    /** LeaseLost */
    KnownStorageErrorCode["LeaseLost"] = "LeaseLost";
    /** LeaseNotPresentWithBlobOperation */
    KnownStorageErrorCode["LeaseNotPresentWithBlobOperation"] = "LeaseNotPresentWithBlobOperation";
    /** LeaseNotPresentWithContainerOperation */
    KnownStorageErrorCode["LeaseNotPresentWithContainerOperation"] = "LeaseNotPresentWithContainerOperation";
    /** LeaseNotPresentWithLeaseOperation */
    KnownStorageErrorCode["LeaseNotPresentWithLeaseOperation"] = "LeaseNotPresentWithLeaseOperation";
    /** MaxBlobSizeConditionNotMet */
    KnownStorageErrorCode["MaxBlobSizeConditionNotMet"] = "MaxBlobSizeConditionNotMet";
    /** NoAuthenticationInformation */
    KnownStorageErrorCode["NoAuthenticationInformation"] = "NoAuthenticationInformation";
    /** NoPendingCopyOperation */
    KnownStorageErrorCode["NoPendingCopyOperation"] = "NoPendingCopyOperation";
    /** OperationNotAllowedOnIncrementalCopyBlob */
    KnownStorageErrorCode["OperationNotAllowedOnIncrementalCopyBlob"] = "OperationNotAllowedOnIncrementalCopyBlob";
    /** PendingCopyOperation */
    KnownStorageErrorCode["PendingCopyOperation"] = "PendingCopyOperation";
    /** PreviousSnapshotCannotBeNewer */
    KnownStorageErrorCode["PreviousSnapshotCannotBeNewer"] = "PreviousSnapshotCannotBeNewer";
    /** PreviousSnapshotNotFound */
    KnownStorageErrorCode["PreviousSnapshotNotFound"] = "PreviousSnapshotNotFound";
    /** PreviousSnapshotOperationNotSupported */
    KnownStorageErrorCode["PreviousSnapshotOperationNotSupported"] = "PreviousSnapshotOperationNotSupported";
    /** SequenceNumberConditionNotMet */
    KnownStorageErrorCode["SequenceNumberConditionNotMet"] = "SequenceNumberConditionNotMet";
    /** SequenceNumberIncrementTooLarge */
    KnownStorageErrorCode["SequenceNumberIncrementTooLarge"] = "SequenceNumberIncrementTooLarge";
    /** SnapshotCountExceeded */
    KnownStorageErrorCode["SnapshotCountExceeded"] = "SnapshotCountExceeded";
    /** SnapshotOperationRateExceeded */
    KnownStorageErrorCode["SnapshotOperationRateExceeded"] = "SnapshotOperationRateExceeded";
    /** SnapshotsPresent */
    KnownStorageErrorCode["SnapshotsPresent"] = "SnapshotsPresent";
    /** SourceConditionNotMet */
    KnownStorageErrorCode["SourceConditionNotMet"] = "SourceConditionNotMet";
    /** SystemInUse */
    KnownStorageErrorCode["SystemInUse"] = "SystemInUse";
    /** TargetConditionNotMet */
    KnownStorageErrorCode["TargetConditionNotMet"] = "TargetConditionNotMet";
    /** UnauthorizedBlobOverwrite */
    KnownStorageErrorCode["UnauthorizedBlobOverwrite"] = "UnauthorizedBlobOverwrite";
    /** BlobBeingRehydrated */
    KnownStorageErrorCode["BlobBeingRehydrated"] = "BlobBeingRehydrated";
    /** BlobArchived */
    KnownStorageErrorCode["BlobArchived"] = "BlobArchived";
    /** BlobNotArchived */
    KnownStorageErrorCode["BlobNotArchived"] = "BlobNotArchived";
    /** AuthorizationSourceIPMismatch */
    KnownStorageErrorCode["AuthorizationSourceIPMismatch"] = "AuthorizationSourceIPMismatch";
    /** AuthorizationProtocolMismatch */
    KnownStorageErrorCode["AuthorizationProtocolMismatch"] = "AuthorizationProtocolMismatch";
    /** AuthorizationPermissionMismatch */
    KnownStorageErrorCode["AuthorizationPermissionMismatch"] = "AuthorizationPermissionMismatch";
    /** AuthorizationServiceMismatch */
    KnownStorageErrorCode["AuthorizationServiceMismatch"] = "AuthorizationServiceMismatch";
    /** AuthorizationResourceTypeMismatch */
    KnownStorageErrorCode["AuthorizationResourceTypeMismatch"] = "AuthorizationResourceTypeMismatch";
    /** BlobAccessTierNotSupportedForAccountType */
    KnownStorageErrorCode["BlobAccessTierNotSupportedForAccountType"] = "BlobAccessTierNotSupportedForAccountType";
})(KnownStorageErrorCode || (KnownStorageErrorCode = {}));
//# sourceMappingURL=index.js.map
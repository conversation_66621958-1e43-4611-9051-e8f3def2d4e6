"use strict";
/* tslint:disable */
/* eslint-disable */
/**
 * Pinecone Inference API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ErrorResponseToJSON = exports.ErrorResponseFromJSONTyped = exports.ErrorResponseFromJSON = exports.instanceOfErrorResponse = void 0;
var ErrorResponseError_1 = require("./ErrorResponseError");
/**
 * Check if a given object implements the ErrorResponse interface.
 */
function instanceOfErrorResponse(value) {
    var isInstance = true;
    isInstance = isInstance && "status" in value;
    isInstance = isInstance && "error" in value;
    return isInstance;
}
exports.instanceOfErrorResponse = instanceOfErrorResponse;
function ErrorResponseFromJSON(json) {
    return ErrorResponseFromJSONTyped(json, false);
}
exports.ErrorResponseFromJSON = ErrorResponseFromJSON;
function ErrorResponseFromJSONTyped(json, ignoreDiscriminator) {
    if ((json === undefined) || (json === null)) {
        return json;
    }
    return {
        'status': json['status'],
        'error': (0, ErrorResponseError_1.ErrorResponseErrorFromJSON)(json['error']),
    };
}
exports.ErrorResponseFromJSONTyped = ErrorResponseFromJSONTyped;
function ErrorResponseToJSON(value) {
    if (value === undefined) {
        return undefined;
    }
    if (value === null) {
        return null;
    }
    return {
        'status': value.status,
        'error': (0, ErrorResponseError_1.ErrorResponseErrorToJSON)(value.error),
    };
}
exports.ErrorResponseToJSON = ErrorResponseToJSON;
//# sourceMappingURL=ErrorResponse.js.map
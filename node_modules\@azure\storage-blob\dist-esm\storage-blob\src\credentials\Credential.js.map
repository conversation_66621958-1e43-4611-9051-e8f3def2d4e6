{"version": 3, "file": "Credential.js", "sourceRoot": "", "sources": ["../../../../src/credentials/Credential.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AASlC;;;GAGG;AACH,MAAM,OAAgB,UAAU;IAC9B;;;;;OAKG;IACI,MAAM,CAAC,WAA0B,EAAE,QAA8B;QACtE,MAAM,IAAI,KAAK,CAAC,mDAAmD,CAAC,CAAC;IACvE,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type {\n  RequestPolicy,\n  RequestPolicyFactory,\n  RequestPolicyOptionsLike as RequestPolicyOptions,\n} from \"@azure/core-http-compat\";\nimport type { CredentialPolicy } from \"../policies/CredentialPolicy\";\n\n/**\n * Credential is an abstract class for Azure Storage HTTP requests signing. This\n * class will host an credentialPolicyCreator factory which generates CredentialPolicy.\n */\nexport abstract class Credential implements RequestPolicyFactory {\n  /**\n   * Creates a RequestPolicy object.\n   *\n   * @param _nextPolicy -\n   * @param _options -\n   */\n  public create(_nextPolicy: RequestPolicy, _options: RequestPolicyOptions): RequestPolicy {\n    throw new Error(\"Method should be implemented in children classes.\");\n  }\n}\n\n/**\n * A factory function that creates a new CredentialPolicy that uses the provided nextPolicy.\n */\nexport type CredentialPolicyCreator = (\n  nextPolicy: RequestPolicy,\n  options: RequestPolicyOptions,\n) => CredentialPolicy;\n"]}
/**
 * Pinecone Data Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
/**
 * `NullValue` is a singleton enumeration to represent the null value for the `Value` type union.
 * The JSON representation for `NullValue` is JSON `null`.
 * @export
 */
export declare const ProtobufNullValue: {
    readonly NullValue: "NULL_VALUE";
};
export type ProtobufNullValue = typeof ProtobufNullValue[keyof typeof ProtobufNullValue];
export declare function ProtobufNullValueFromJSON(json: any): ProtobufNullValue;
export declare function ProtobufNullValueFromJSONTyped(json: any, ignoreDiscriminator: boolean): ProtobufNullValue;
export declare function ProtobufNullValueToJSON(value?: ProtobufNullValue | null): any;

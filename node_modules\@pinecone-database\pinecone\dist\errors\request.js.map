{"version": 3, "file": "request.js", "sourceRoot": "", "sources": ["../../src/errors/request.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAAA,+BAA2C;AAG3C;;;;;;;;;;;;;;;;;;;;;;;;;KAyBK;AACL;IAA6C,2CAAiB;IAC5D,iCAAY,CAAQ,EAAE,GAAY;QAAlC,iBAWC;QAVC,IAAI,UAAU,GAAG,EAAE,CAAC;QACpB,IAAI,GAAG,EAAE;YACP,UAAU,GAAG,yBAAkB,GAAG,CAAE,CAAC;SACtC;gBAED,kBACE,0CAAmC,UAAU,gPAA6O,EAC1R,CAAC,CACF;QACD,KAAI,CAAC,IAAI,GAAG,yBAAyB,CAAC;;IACxC,CAAC;IACH,8BAAC;AAAD,CAAC,AAbD,CAA6C,wBAAiB,GAa7D;AAbY,0DAAuB;AAepC;;;;GAIG;AACH;IAA0C,wCAAiB;IACzD,8BAAY,OAAqB;QAAjC,iBAYC;QAXC,IAAI,OAAO,CAAC,QAAQ,EAAE;YACpB,QAAA,kBACE,0CAAmC,OAAO,CAAC,IAAI,CAAC,MAAM,cAAI,OAAO,CAAC,GAAG,0BAAgB,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAE,EAC9G,OAAO,CAAC,KAAc,CACvB,SAAC;SACH;aAAM;YACL,QAAA,kBACE,0CAAmC,OAAO,CAAC,IAAI,CAAC,MAAM,cAAI,OAAO,CAAC,GAAG,CAAE,EACvE,OAAO,CAAC,KAAc,CACvB,SAAC;SACH;;IACH,CAAC;IACH,2BAAC;AAAD,CAAC,AAdD,CAA0C,wBAAiB,GAc1D;AAdY,oDAAoB"}
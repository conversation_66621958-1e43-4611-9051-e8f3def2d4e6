"use strict";
/* tslint:disable */
/* eslint-disable */
/**
 * Pinecone Data Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.StartImportResponseToJSON = exports.StartImportResponseFromJSONTyped = exports.StartImportResponseFromJSON = exports.instanceOfStartImportResponse = void 0;
var runtime_1 = require("../runtime");
/**
 * Check if a given object implements the StartImportResponse interface.
 */
function instanceOfStartImportResponse(value) {
    var isInstance = true;
    return isInstance;
}
exports.instanceOfStartImportResponse = instanceOfStartImportResponse;
function StartImportResponseFromJSON(json) {
    return StartImportResponseFromJSONTyped(json, false);
}
exports.StartImportResponseFromJSON = StartImportResponseFromJSON;
function StartImportResponseFromJSONTyped(json, ignoreDiscriminator) {
    if ((json === undefined) || (json === null)) {
        return json;
    }
    return {
        'id': !(0, runtime_1.exists)(json, 'id') ? undefined : json['id'],
    };
}
exports.StartImportResponseFromJSONTyped = StartImportResponseFromJSONTyped;
function StartImportResponseToJSON(value) {
    if (value === undefined) {
        return undefined;
    }
    if (value === null) {
        return null;
    }
    return {
        'id': value.id,
    };
}
exports.StartImportResponseToJSON = StartImportResponseToJSON;
//# sourceMappingURL=StartImportResponse.js.map
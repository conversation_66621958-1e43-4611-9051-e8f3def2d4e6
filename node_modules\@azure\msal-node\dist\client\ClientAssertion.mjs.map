{"version": 3, "file": "ClientAssertion.mjs", "sources": ["../../src/client/ClientAssertion.ts"], "sourcesContent": [null], "names": [], "mappings": ";;;;;;;AAAA;;;AAGG;AAaH;;;AAGG;MACU,eAAe,CAAA;AAUxB;;;AAGG;IACI,OAAO,aAAa,CAAC,SAAiB,EAAA;AACzC,QAAA,MAAM,eAAe,GAAG,IAAI,eAAe,EAAE,CAAC;AAC9C,QAAA,eAAe,CAAC,GAAG,GAAG,SAAS,CAAC;AAChC,QAAA,OAAO,eAAe,CAAC;KAC1B;AAED;;;;;;AAMG;AACI,IAAA,OAAO,eAAe,CACzB,UAAkB,EAClB,UAAkB,EAClB,iBAA0B,EAAA;AAE1B,QAAA,MAAM,eAAe,GAAG,IAAI,eAAe,EAAE,CAAC;AAC9C,QAAA,eAAe,CAAC,UAAU,GAAG,UAAU,CAAC;AACxC,QAAA,eAAe,CAAC,UAAU,GAAG,UAAU,CAAC;AACxC,QAAA,eAAe,CAAC,SAAS,GAAG,KAAK,CAAC;AAClC,QAAA,IAAI,iBAAiB,EAAE;AACnB,YAAA,eAAe,CAAC,iBAAiB;AAC7B,gBAAA,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,CAAC;AAChD,SAAA;AACD,QAAA,OAAO,eAAe,CAAC;KAC1B;AAED;;;;;AAKG;AACI,IAAA,OAAO,mCAAmC,CAC7C,UAAkB,EAClB,UAAkB,EAClB,iBAA0B,EAAA;AAE1B,QAAA,MAAM,eAAe,GAAG,IAAI,eAAe,EAAE,CAAC;AAC9C,QAAA,eAAe,CAAC,UAAU,GAAG,UAAU,CAAC;AACxC,QAAA,eAAe,CAAC,UAAU,GAAG,UAAU,CAAC;AACxC,QAAA,eAAe,CAAC,SAAS,GAAG,IAAI,CAAC;AACjC,QAAA,IAAI,iBAAiB,EAAE;AACnB,YAAA,eAAe,CAAC,iBAAiB;AAC7B,gBAAA,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,CAAC;AAChD,SAAA;AACD,QAAA,OAAO,eAAe,CAAC;KAC1B;AAED;;;;;AAKG;AACI,IAAA,MAAM,CACT,cAA8B,EAC9B,MAAc,EACd,WAAmB,EAAA;;AAGnB,QAAA,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,EAAE;YACpC,IACI,IAAI,CAAC,GAAG;gBACR,CAAC,IAAI,CAAC,SAAS,EAAE;gBACjB,MAAM,KAAK,IAAI,CAAC,MAAM;AACtB,gBAAA,WAAW,KAAK,IAAI,CAAC,WAAW,EAClC;gBACE,OAAO,IAAI,CAAC,GAAG,CAAC;AACnB,aAAA;YAED,OAAO,IAAI,CAAC,SAAS,CAAC,cAAc,EAAE,MAAM,EAAE,WAAW,CAAC,CAAC;AAC9D,SAAA;AAED;;;AAGG;QACH,IAAI,IAAI,CAAC,GAAG,EAAE;YACV,OAAO,IAAI,CAAC,GAAG,CAAC;AACnB,SAAA;AAED,QAAA,MAAM,qBAAqB,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,CAAC;KACtE;AAED;;AAEG;AACK,IAAA,SAAS,CACb,cAA8B,EAC9B,MAAc,EACd,WAAmB,EAAA;AAEnB,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACrB,QAAA,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;AAC/B,QAAA,MAAM,QAAQ,GAAG,SAAS,CAAC,UAAU,EAAE,CAAC;AACxC,QAAA,IAAI,CAAC,cAAc,GAAG,QAAQ,GAAG,GAAG,CAAC;AAErC,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS;cAC1B,YAAY,CAAC,OAAO;AACtB,cAAE,YAAY,CAAC,OAAO,CAAC;AAC3B,QAAA,MAAM,MAAM,GAAkB;AAC1B,YAAA,GAAG,EAAE,SAAS;SACjB,CAAC;AAEF,QAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,SAAS;cACjC,YAAY,CAAC,OAAO;AACtB,cAAE,YAAY,CAAC,GAAG,CAAC;AACvB,QAAA,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE;AAClB,YAAA,CAAC,gBAAgB,GAAG,aAAa,CAAC,eAAe,CAC7C,IAAI,CAAC,UAAU,EACf,KAAK,CACR;AACsB,SAAA,CAAC,CAAC;QAE7B,IAAI,IAAI,CAAC,iBAAiB,EAAE;AACxB,YAAA,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE;AAClB,gBAAA,CAAC,YAAY,CAAC,GAAG,GAAG,IAAI,CAAC,iBAAiB;AACnB,aAAA,CAAC,CAAC;AAChC,SAAA;AAED,QAAA,MAAM,OAAO,GAAG;AACZ,YAAA,CAAC,YAAY,CAAC,QAAQ,GAAG,IAAI,CAAC,WAAW;AACzC,YAAA,CAAC,YAAY,CAAC,eAAe,GAAG,IAAI,CAAC,cAAc;AACnD,YAAA,CAAC,YAAY,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM;AAClC,YAAA,CAAC,YAAY,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM;AACnC,YAAA,CAAC,YAAY,CAAC,UAAU,GAAG,QAAQ;YACnC,CAAC,YAAY,CAAC,MAAM,GAAG,cAAc,CAAC,aAAa,EAAE;SACxD,CAAC;AAEF,QAAA,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,UAAU,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;QAC1D,OAAO,IAAI,CAAC,GAAG,CAAC;KACnB;AAED;;AAEG;IACK,SAAS,GAAA;QACb,OAAO,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC,UAAU,EAAE,CAAC;KACvD;AAED;;;AAGG;IACI,OAAO,gBAAgB,CAAC,iBAAyB,EAAA;AACpD;;;;;;AAMG;QACH,MAAM,gBAAgB,GAClB,uEAAuE,CAAC;QAC5E,MAAM,KAAK,GAAa,EAAE,CAAC;AAE3B,QAAA,IAAI,OAAO,CAAC;AACZ,QAAA,OAAO,CAAC,OAAO,GAAG,gBAAgB,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAM,IAAI,EAAE;;AAElE,YAAA,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,EAAE,SAAS,CAAC,YAAY,CAAC,CAAC,CAAC;AACpE,SAAA;AAED,QAAA,OAAO,KAAK,CAAC;KAChB;AACJ;;;;"}
"use strict";
/* tslint:disable */
/* eslint-disable */
/**
 * Pinecone Data Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.IndexDescriptionToJSON = exports.IndexDescriptionFromJSONTyped = exports.IndexDescriptionFromJSON = exports.instanceOfIndexDescription = void 0;
var runtime_1 = require("../runtime");
var NamespaceSummary_1 = require("./NamespaceSummary");
/**
 * Check if a given object implements the IndexDescription interface.
 */
function instanceOfIndexDescription(value) {
    var isInstance = true;
    return isInstance;
}
exports.instanceOfIndexDescription = instanceOfIndexDescription;
function IndexDescriptionFromJSON(json) {
    return IndexDescriptionFromJSONTyped(json, false);
}
exports.IndexDescriptionFromJSON = IndexDescriptionFromJSON;
function IndexDescriptionFromJSONTyped(json, ignoreDiscriminator) {
    if ((json === undefined) || (json === null)) {
        return json;
    }
    return {
        'namespaces': !(0, runtime_1.exists)(json, 'namespaces') ? undefined : ((0, runtime_1.mapValues)(json['namespaces'], NamespaceSummary_1.NamespaceSummaryFromJSON)),
        'dimension': !(0, runtime_1.exists)(json, 'dimension') ? undefined : json['dimension'],
        'indexFullness': !(0, runtime_1.exists)(json, 'indexFullness') ? undefined : json['indexFullness'],
        'totalVectorCount': !(0, runtime_1.exists)(json, 'totalVectorCount') ? undefined : json['totalVectorCount'],
    };
}
exports.IndexDescriptionFromJSONTyped = IndexDescriptionFromJSONTyped;
function IndexDescriptionToJSON(value) {
    if (value === undefined) {
        return undefined;
    }
    if (value === null) {
        return null;
    }
    return {
        'namespaces': value.namespaces === undefined ? undefined : ((0, runtime_1.mapValues)(value.namespaces, NamespaceSummary_1.NamespaceSummaryToJSON)),
        'dimension': value.dimension,
        'indexFullness': value.indexFullness,
        'totalVectorCount': value.totalVectorCount,
    };
}
exports.IndexDescriptionToJSON = IndexDescriptionToJSON;
//# sourceMappingURL=IndexDescription.js.map
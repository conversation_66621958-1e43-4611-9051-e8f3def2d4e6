{"name": "n8n-ai-agent", "version": "1.0.0", "description": "An AI agent built with n8n for automation and intelligent workflows", "main": "index.js", "scripts": {"start": "docker-compose up -d", "stop": "docker-compose down", "logs": "docker-compose logs -f", "restart": "docker-compose restart", "setup": "docker-compose up -d && echo 'n8n is starting up. Please wait a moment and then visit http://localhost:5678'"}, "keywords": ["n8n", "automation", "ai", "agent", "workflow"], "author": "", "license": "MIT", "devDependencies": {"n8n": "^1.0.0"}}
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const codegen_1 = require("../../compile/codegen");
const util_1 = require("../../compile/util");
const validate_1 = require("../../compile/validate");
const code_1 = require("../code");
const def = {
    keyword: "items",
    type: "array",
    schemaType: ["object", "array", "boolean"],
    before: "uniqueItems",
    code(cxt) {
        const { gen, schema, it } = cxt;
        if (Array.isArray(schema)) {
            if (it.opts.unevaluated && schema.length && it.items !== true) {
                it.items = util_1.mergeEvaluated.items(gen, schema.length, it.items);
            }
            validateTuple(schema);
        }
        else {
            it.items = true;
            if (util_1.alwaysValidSchema(it, schema))
                return;
            cxt.ok(code_1.validateArray(cxt));
        }
        function validateTuple(schArr) {
            const { parentSchema, data } = cxt;
            if (it.opts.strictTuples && !fullTupleSchema(schArr.length, parentSchema)) {
                const msg = `"items" is ${schArr.length}-tuple, but minItems or maxItems/additionalItems are not specified or different`;
                validate_1.checkStrictMode(it, msg, it.opts.strictTuples);
            }
            const valid = gen.name("valid");
            const len = gen.const("len", codegen_1._ `${data}.length`);
            schArr.forEach((sch, i) => {
                if (util_1.alwaysValidSchema(it, sch))
                    return;
                gen.if(codegen_1._ `${len} > ${i}`, () => cxt.subschema({
                    keyword: "items",
                    schemaProp: i,
                    dataProp: i,
                }, valid));
                cxt.ok(valid);
            });
        }
    },
};
function fullTupleSchema(len, sch) {
    return len === sch.minItems && (len === sch.maxItems || sch.additionalItems === false);
}
exports.default = def;
//# sourceMappingURL=items.js.map
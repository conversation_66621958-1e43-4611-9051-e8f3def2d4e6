{"version": 3, "file": "BlobQuickQueryStream.js", "sourceRoot": "", "sources": ["../../../../src/utils/BlobQuickQueryStream.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,QAAQ,EAAE,MAAM,QAAQ,CAAC;AAKlC,OAAO,EAAE,sBAAsB,EAAE,UAAU,EAAE,MAAM,oCAAoC,CAAC;AAqBxF;;;;GAIG;AACH,MAAM,OAAO,oBAAqB,SAAQ,QAAQ;IAQhD;;;;;OAKG;IACH,YAAmB,MAA6B,EAAE,UAAuC,EAAE;QACzF,KAAK,EAAE,CAAC;QAXF,eAAU,GAAY,IAAI,CAAC;QAYjC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;QACrC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;QAC/B,IAAI,CAAC,UAAU,GAAG,IAAI,UAAU,CAAC,IAAI,sBAAsB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;QAC1E,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE,WAAW,EAAE,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC;IACrF,CAAC;IAEM,KAAK;QACV,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACpB,IAAI,CAAC,YAAY,EAAE,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,EAAE;gBAChC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;YAC1B,CAAC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,YAAY;QACxB,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QACxB,IAAI,QAAQ,CAAC;QACb,GAAG,CAAC;YACF,QAAQ,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;YACtC,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC;gBAClB,MAAM;YACR,CAAC;YACD,MAAM,GAAG,GAAG,QAAQ,CAAC,KAAK,CAAC;YAC3B,MAAM,MAAM,GAAI,GAAW,CAAC,OAAO,CAAC;YACpC,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;gBAC/B,MAAM,KAAK,CAAC,gCAAgC,CAAC,CAAC;YAChD,CAAC;YAED,QAAQ,MAAM,EAAE,CAAC;gBACf,KAAK,0DAA0D;oBAC7D,CAAC;wBACC,MAAM,IAAI,GAAI,GAAW,CAAC,IAAI,CAAC;wBAC/B,IAAI,IAAI,YAAY,UAAU,KAAK,KAAK,EAAE,CAAC;4BACzC,MAAM,KAAK,CAAC,qCAAqC,CAAC,CAAC;wBACrD,CAAC;wBACD,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC;4BAClC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;wBACzB,CAAC;oBACH,CAAC;oBACD,MAAM;gBACR,KAAK,wDAAwD;oBAC3D,CAAC;wBACC,MAAM,YAAY,GAAI,GAAW,CAAC,YAAY,CAAC;wBAC/C,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE,CAAC;4BACrC,MAAM,KAAK,CAAC,+CAA+C,CAAC,CAAC;wBAC/D,CAAC;wBACD,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;4BACpB,IAAI,CAAC,UAAU,CAAC,EAAE,WAAW,EAAE,YAAY,EAAE,CAAC,CAAC;wBACjD,CAAC;oBACH,CAAC;oBACD,MAAM;gBACR,KAAK,mDAAmD;oBACtD,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;wBACpB,MAAM,UAAU,GAAI,GAAW,CAAC,UAAU,CAAC;wBAC3C,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE,CAAC;4BACnC,MAAM,KAAK,CAAC,wCAAwC,CAAC,CAAC;wBACxD,CAAC;wBACD,IAAI,CAAC,UAAU,CAAC,EAAE,WAAW,EAAE,UAAU,EAAE,CAAC,CAAC;oBAC/C,CAAC;oBACD,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBAChB,MAAM;gBACR,KAAK,qDAAqD;oBACxD,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;wBACjB,MAAM,KAAK,GAAI,GAAW,CAAC,KAAK,CAAC;wBACjC,IAAI,OAAO,KAAK,KAAK,SAAS,EAAE,CAAC;4BAC/B,MAAM,KAAK,CAAC,qCAAqC,CAAC,CAAC;wBACrD,CAAC;wBACD,MAAM,IAAI,GAAI,GAAW,CAAC,IAAI,CAAC;wBAC/B,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;4BAC7B,MAAM,KAAK,CAAC,oCAAoC,CAAC,CAAC;wBACpD,CAAC;wBACD,MAAM,WAAW,GAAI,GAAW,CAAC,WAAW,CAAC;wBAC7C,IAAI,OAAO,WAAW,KAAK,QAAQ,EAAE,CAAC;4BACpC,MAAM,KAAK,CAAC,2CAA2C,CAAC,CAAC;wBAC3D,CAAC;wBACD,MAAM,QAAQ,GAAI,GAAW,CAAC,QAAQ,CAAC;wBACvC,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,CAAC;4BACjC,MAAM,KAAK,CAAC,wCAAwC,CAAC,CAAC;wBACxD,CAAC;wBACD,IAAI,CAAC,OAAO,CAAC;4BACX,QAAQ;4BACR,IAAI;4BACJ,OAAO,EAAE,KAAK;4BACd,WAAW;yBACZ,CAAC,CAAC;oBACL,CAAC;oBACD,MAAM;gBACR;oBACE,MAAM,KAAK,CAAC,kBAAkB,MAAM,2BAA2B,CAAC,CAAC;YACrE,CAAC;QACH,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;IAC/C,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport { Readable } from \"stream\";\n\nimport type { AbortSignalLike } from \"@azure/abort-controller\";\nimport type { TransferProgressEvent } from \"@azure/core-rest-pipeline\";\n\nimport { AvroReadableFromStream, AvroReader } from \"../../../storage-internal-avro/src\";\nimport type { BlobQueryError } from \"../Clients\";\n\nexport interface BlobQuickQueryStreamOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n\n  /**\n   * Read progress event handler\n   */\n  onProgress?: (progress: TransferProgressEvent) => void;\n\n  /**\n   * Callback to receive error events during the query operaiton.\n   */\n  onError?: (error: BlobQueryError) => void;\n}\n\n/**\n * ONLY AVAILABLE IN NODE.JS RUNTIME.\n *\n * A Node.js BlobQuickQueryStream will internally parse avro data stream for blob query.\n */\nexport class BlobQuickQueryStream extends Readable {\n  private source: NodeJS.ReadableStream;\n  private avroReader: AvroReader;\n  private avroIter: AsyncIterableIterator<unknown | null>;\n  private avroPaused: boolean = true;\n  private onProgress?: (progress: TransferProgressEvent) => void;\n  private onError?: (error: BlobQueryError) => void;\n\n  /**\n   * Creates an instance of BlobQuickQueryStream.\n   *\n   * @param source - The current ReadableStream returned from getter\n   * @param options -\n   */\n  public constructor(source: NodeJS.ReadableStream, options: BlobQuickQueryStreamOptions = {}) {\n    super();\n    this.source = source;\n    this.onProgress = options.onProgress;\n    this.onError = options.onError;\n    this.avroReader = new AvroReader(new AvroReadableFromStream(this.source));\n    this.avroIter = this.avroReader.parseObjects({ abortSignal: options.abortSignal });\n  }\n\n  public _read(): void {\n    if (this.avroPaused) {\n      this.readInternal().catch((err) => {\n        this.emit(\"error\", err);\n      });\n    }\n  }\n\n  private async readInternal() {\n    this.avroPaused = false;\n    let avroNext;\n    do {\n      avroNext = await this.avroIter.next();\n      if (avroNext.done) {\n        break;\n      }\n      const obj = avroNext.value;\n      const schema = (obj as any).$schema;\n      if (typeof schema !== \"string\") {\n        throw Error(\"Missing schema in avro record.\");\n      }\n\n      switch (schema) {\n        case \"com.microsoft.azure.storage.queryBlobContents.resultData\":\n          {\n            const data = (obj as any).data;\n            if (data instanceof Uint8Array === false) {\n              throw Error(\"Invalid data in avro result record.\");\n            }\n            if (!this.push(Buffer.from(data))) {\n              this.avroPaused = true;\n            }\n          }\n          break;\n        case \"com.microsoft.azure.storage.queryBlobContents.progress\":\n          {\n            const bytesScanned = (obj as any).bytesScanned;\n            if (typeof bytesScanned !== \"number\") {\n              throw Error(\"Invalid bytesScanned in avro progress record.\");\n            }\n            if (this.onProgress) {\n              this.onProgress({ loadedBytes: bytesScanned });\n            }\n          }\n          break;\n        case \"com.microsoft.azure.storage.queryBlobContents.end\":\n          if (this.onProgress) {\n            const totalBytes = (obj as any).totalBytes;\n            if (typeof totalBytes !== \"number\") {\n              throw Error(\"Invalid totalBytes in avro end record.\");\n            }\n            this.onProgress({ loadedBytes: totalBytes });\n          }\n          this.push(null);\n          break;\n        case \"com.microsoft.azure.storage.queryBlobContents.error\":\n          if (this.onError) {\n            const fatal = (obj as any).fatal;\n            if (typeof fatal !== \"boolean\") {\n              throw Error(\"Invalid fatal in avro error record.\");\n            }\n            const name = (obj as any).name;\n            if (typeof name !== \"string\") {\n              throw Error(\"Invalid name in avro error record.\");\n            }\n            const description = (obj as any).description;\n            if (typeof description !== \"string\") {\n              throw Error(\"Invalid description in avro error record.\");\n            }\n            const position = (obj as any).position;\n            if (typeof position !== \"number\") {\n              throw Error(\"Invalid position in avro error record.\");\n            }\n            this.onError({\n              position,\n              name,\n              isFatal: fatal,\n              description,\n            });\n          }\n          break;\n        default:\n          throw Error(`Unknown schema ${schema} in avro progress record.`);\n      }\n    } while (!avroNext.done && !this.avroPaused);\n  }\n}\n"]}
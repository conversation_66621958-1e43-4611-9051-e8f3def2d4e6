"use strict";
/* tslint:disable */
/* eslint-disable */
/**
 * Pinecone Data Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ImportModelToJSON = exports.ImportModelFromJSONTyped = exports.ImportModelFromJSON = exports.instanceOfImportModel = exports.ImportModelStatusEnum = void 0;
var runtime_1 = require("../runtime");
/**
 * @export
 */
exports.ImportModelStatusEnum = {
    Pending: 'Pending',
    InProgress: 'InProgress',
    Failed: 'Failed',
    Completed: 'Completed',
    Cancelled: 'Cancelled'
};
/**
 * Check if a given object implements the ImportModel interface.
 */
function instanceOfImportModel(value) {
    var isInstance = true;
    return isInstance;
}
exports.instanceOfImportModel = instanceOfImportModel;
function ImportModelFromJSON(json) {
    return ImportModelFromJSONTyped(json, false);
}
exports.ImportModelFromJSON = ImportModelFromJSON;
function ImportModelFromJSONTyped(json, ignoreDiscriminator) {
    if ((json === undefined) || (json === null)) {
        return json;
    }
    return {
        'id': !(0, runtime_1.exists)(json, 'id') ? undefined : json['id'],
        'uri': !(0, runtime_1.exists)(json, 'uri') ? undefined : json['uri'],
        'status': !(0, runtime_1.exists)(json, 'status') ? undefined : json['status'],
        'createdAt': !(0, runtime_1.exists)(json, 'createdAt') ? undefined : (new Date(json['createdAt'])),
        'finishedAt': !(0, runtime_1.exists)(json, 'finishedAt') ? undefined : (new Date(json['finishedAt'])),
        'percentComplete': !(0, runtime_1.exists)(json, 'percentComplete') ? undefined : json['percentComplete'],
        'recordsImported': !(0, runtime_1.exists)(json, 'recordsImported') ? undefined : json['recordsImported'],
        'error': !(0, runtime_1.exists)(json, 'error') ? undefined : json['error'],
    };
}
exports.ImportModelFromJSONTyped = ImportModelFromJSONTyped;
function ImportModelToJSON(value) {
    if (value === undefined) {
        return undefined;
    }
    if (value === null) {
        return null;
    }
    return {
        'id': value.id,
        'uri': value.uri,
        'status': value.status,
        'createdAt': value.createdAt === undefined ? undefined : (value.createdAt.toISOString()),
        'finishedAt': value.finishedAt === undefined ? undefined : (value.finishedAt.toISOString()),
        'percentComplete': value.percentComplete,
        'recordsImported': value.recordsImported,
        'error': value.error,
    };
}
exports.ImportModelToJSON = ImportModelToJSON;
//# sourceMappingURL=ImportModel.js.map
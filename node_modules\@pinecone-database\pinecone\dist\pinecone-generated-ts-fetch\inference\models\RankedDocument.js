"use strict";
/* tslint:disable */
/* eslint-disable */
/**
 * Pinecone Inference API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.RankedDocumentToJSON = exports.RankedDocumentFromJSONTyped = exports.RankedDocumentFromJSON = exports.instanceOfRankedDocument = void 0;
var runtime_1 = require("../runtime");
/**
 * Check if a given object implements the RankedDocument interface.
 */
function instanceOfRankedDocument(value) {
    var isInstance = true;
    isInstance = isInstance && "index" in value;
    isInstance = isInstance && "score" in value;
    return isInstance;
}
exports.instanceOfRankedDocument = instanceOfRankedDocument;
function RankedDocumentFromJSON(json) {
    return RankedDocumentFromJSONTyped(json, false);
}
exports.RankedDocumentFromJSON = RankedDocumentFromJSON;
function RankedDocumentFromJSONTyped(json, ignoreDiscriminator) {
    if ((json === undefined) || (json === null)) {
        return json;
    }
    return {
        'index': json['index'],
        'score': json['score'],
        'document': !(0, runtime_1.exists)(json, 'document') ? undefined : json['document'],
    };
}
exports.RankedDocumentFromJSONTyped = RankedDocumentFromJSONTyped;
function RankedDocumentToJSON(value) {
    if (value === undefined) {
        return undefined;
    }
    if (value === null) {
        return null;
    }
    return {
        'index': value.index,
        'score': value.score,
        'document': value.document,
    };
}
exports.RankedDocumentToJSON = RankedDocumentToJSON;
//# sourceMappingURL=RankedDocument.js.map
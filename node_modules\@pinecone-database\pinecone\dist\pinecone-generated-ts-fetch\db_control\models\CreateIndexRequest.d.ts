/**
 * Pinecone Control Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import type { DeletionProtection } from './DeletionProtection';
import type { IndexSpec } from './IndexSpec';
/**
 * The configuration needed to create a Pinecone index.
 * @export
 * @interface CreateIndexRequest
 */
export interface CreateIndexRequest {
    /**
     * The name of the index. Resource name must be 1-45 characters long, start and end with an alphanumeric character, and consist only of lower case alphanumeric characters or '-'.
     * @type {string}
     * @memberof CreateIndexRequest
     */
    name: string;
    /**
     * The dimensions of the vectors to be inserted in the index.
     * @type {number}
     * @memberof CreateIndexRequest
     */
    dimension: number;
    /**
     * The distance metric to be used for similarity search. You can use 'euclidean', 'cosine', or 'dotproduct'.
     * @type {string}
     * @memberof CreateIndexRequest
     */
    metric?: CreateIndexRequestMetricEnum;
    /**
     *
     * @type {DeletionProtection}
     * @memberof CreateIndexRequest
     */
    deletionProtection?: DeletionProtection;
    /**
     * Custom user tags added to an index. Keys must be alphanumeric and 80 characters or less. Values must be 120 characters or less.
     * @type {{ [key: string]: string; }}
     * @memberof CreateIndexRequest
     */
    tags?: {
        [key: string]: string;
    } | null;
    /**
     *
     * @type {IndexSpec}
     * @memberof CreateIndexRequest
     */
    spec: IndexSpec | null;
}
/**
 * @export
 */
export declare const CreateIndexRequestMetricEnum: {
    readonly Cosine: "cosine";
    readonly Euclidean: "euclidean";
    readonly Dotproduct: "dotproduct";
};
export type CreateIndexRequestMetricEnum = typeof CreateIndexRequestMetricEnum[keyof typeof CreateIndexRequestMetricEnum];
/**
 * Check if a given object implements the CreateIndexRequest interface.
 */
export declare function instanceOfCreateIndexRequest(value: object): boolean;
export declare function CreateIndexRequestFromJSON(json: any): CreateIndexRequest;
export declare function CreateIndexRequestFromJSONTyped(json: any, ignoreDiscriminator: boolean): CreateIndexRequest;
export declare function CreateIndexRequestToJSON(value?: CreateIndexRequest | null): any;

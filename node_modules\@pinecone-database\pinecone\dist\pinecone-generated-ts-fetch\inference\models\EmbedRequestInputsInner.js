"use strict";
/* tslint:disable */
/* eslint-disable */
/**
 * Pinecone Inference API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmbedRequestInputsInnerToJSON = exports.EmbedRequestInputsInnerFromJSONTyped = exports.EmbedRequestInputsInnerFromJSON = exports.instanceOfEmbedRequestInputsInner = void 0;
var runtime_1 = require("../runtime");
/**
 * Check if a given object implements the EmbedRequestInputsInner interface.
 */
function instanceOfEmbedRequestInputsInner(value) {
    var isInstance = true;
    return isInstance;
}
exports.instanceOfEmbedRequestInputsInner = instanceOfEmbedRequestInputsInner;
function EmbedRequestInputsInnerFromJSON(json) {
    return EmbedRequestInputsInnerFromJSONTyped(json, false);
}
exports.EmbedRequestInputsInnerFromJSON = EmbedRequestInputsInnerFromJSON;
function EmbedRequestInputsInnerFromJSONTyped(json, ignoreDiscriminator) {
    if ((json === undefined) || (json === null)) {
        return json;
    }
    return {
        'text': !(0, runtime_1.exists)(json, 'text') ? undefined : json['text'],
    };
}
exports.EmbedRequestInputsInnerFromJSONTyped = EmbedRequestInputsInnerFromJSONTyped;
function EmbedRequestInputsInnerToJSON(value) {
    if (value === undefined) {
        return undefined;
    }
    if (value === null) {
        return null;
    }
    return {
        'text': value.text,
    };
}
exports.EmbedRequestInputsInnerToJSON = EmbedRequestInputsInnerToJSON;
//# sourceMappingURL=EmbedRequestInputsInner.js.map
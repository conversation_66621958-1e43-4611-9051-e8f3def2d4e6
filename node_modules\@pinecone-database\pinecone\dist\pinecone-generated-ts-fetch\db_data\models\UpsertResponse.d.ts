/**
 * Pinecone Data Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
/**
 * The response for the `upsert` operation.
 * @export
 * @interface UpsertResponse
 */
export interface UpsertResponse {
    /**
     * The number of vectors upserted.
     * @type {number}
     * @memberof UpsertResponse
     */
    upsertedCount?: number;
}
/**
 * Check if a given object implements the UpsertResponse interface.
 */
export declare function instanceOfUpsertResponse(value: object): boolean;
export declare function UpsertResponseFromJSON(json: any): UpsertResponse;
export declare function UpsertResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): UpsertResponse;
export declare function UpsertResponseToJSON(value?: UpsertResponse | null): any;

{"version": 3, "file": "StartImportRequest.js", "sourceRoot": "", "sources": ["../../../../src/pinecone-generated-ts-fetch/db_data/models/StartImportRequest.ts"], "names": [], "mappings": ";AAAA,oBAAoB;AACpB,oBAAoB;AACpB;;;;;;;;;;GAUG;;;AAEH,sCAA+C;AAE/C,qDAI2B;AA4B3B;;GAEG;AACH,SAAgB,4BAA4B,CAAC,KAAa;IACtD,IAAI,UAAU,GAAG,IAAI,CAAC;IACtB,UAAU,GAAG,UAAU,IAAI,KAAK,IAAI,KAAK,CAAC;IAE1C,OAAO,UAAU,CAAC;AACtB,CAAC;AALD,oEAKC;AAED,SAAgB,0BAA0B,CAAC,IAAS;IAChD,OAAO,+BAA+B,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AACxD,CAAC;AAFD,gEAEC;AAED,SAAgB,+BAA+B,CAAC,IAAS,EAAE,mBAA4B;IACnF,IAAI,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,EAAE;QACzC,OAAO,IAAI,CAAC;KACf;IACD,OAAO;QAEH,eAAe,EAAE,CAAC,IAAA,gBAAM,EAAC,IAAI,EAAE,eAAe,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC;QACnF,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC;QAClB,WAAW,EAAE,CAAC,IAAA,gBAAM,EAAC,IAAI,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAA,yCAAuB,EAAC,IAAI,CAAC,WAAW,CAAC,CAAC;KACnG,CAAC;AACN,CAAC;AAVD,0EAUC;AAED,SAAgB,wBAAwB,CAAC,KAAiC;IACtE,IAAI,KAAK,KAAK,SAAS,EAAE;QACrB,OAAO,SAAS,CAAC;KACpB;IACD,IAAI,KAAK,KAAK,IAAI,EAAE;QAChB,OAAO,IAAI,CAAC;KACf;IACD,OAAO;QAEH,eAAe,EAAE,KAAK,CAAC,aAAa;QACpC,KAAK,EAAE,KAAK,CAAC,GAAG;QAChB,WAAW,EAAE,IAAA,uCAAqB,EAAC,KAAK,CAAC,SAAS,CAAC;KACtD,CAAC;AACN,CAAC;AAbD,4DAaC"}
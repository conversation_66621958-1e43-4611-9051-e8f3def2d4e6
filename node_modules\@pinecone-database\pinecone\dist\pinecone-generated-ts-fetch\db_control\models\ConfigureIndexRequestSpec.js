"use strict";
/* tslint:disable */
/* eslint-disable */
/**
 * Pinecone Control Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConfigureIndexRequestSpecToJSON = exports.ConfigureIndexRequestSpecFromJSONTyped = exports.ConfigureIndexRequestSpecFromJSON = exports.instanceOfConfigureIndexRequestSpec = void 0;
var ConfigureIndexRequestSpecPod_1 = require("./ConfigureIndexRequestSpecPod");
/**
 * Check if a given object implements the ConfigureIndexRequestSpec interface.
 */
function instanceOfConfigureIndexRequestSpec(value) {
    var isInstance = true;
    isInstance = isInstance && "pod" in value;
    return isInstance;
}
exports.instanceOfConfigureIndexRequestSpec = instanceOfConfigureIndexRequestSpec;
function ConfigureIndexRequestSpecFromJSON(json) {
    return ConfigureIndexRequestSpecFromJSONTyped(json, false);
}
exports.ConfigureIndexRequestSpecFromJSON = ConfigureIndexRequestSpecFromJSON;
function ConfigureIndexRequestSpecFromJSONTyped(json, ignoreDiscriminator) {
    if ((json === undefined) || (json === null)) {
        return json;
    }
    return {
        'pod': (0, ConfigureIndexRequestSpecPod_1.ConfigureIndexRequestSpecPodFromJSON)(json['pod']),
    };
}
exports.ConfigureIndexRequestSpecFromJSONTyped = ConfigureIndexRequestSpecFromJSONTyped;
function ConfigureIndexRequestSpecToJSON(value) {
    if (value === undefined) {
        return undefined;
    }
    if (value === null) {
        return null;
    }
    return {
        'pod': (0, ConfigureIndexRequestSpecPod_1.ConfigureIndexRequestSpecPodToJSON)(value.pod),
    };
}
exports.ConfigureIndexRequestSpecToJSON = ConfigureIndexRequestSpecToJSON;
//# sourceMappingURL=ConfigureIndexRequestSpec.js.map
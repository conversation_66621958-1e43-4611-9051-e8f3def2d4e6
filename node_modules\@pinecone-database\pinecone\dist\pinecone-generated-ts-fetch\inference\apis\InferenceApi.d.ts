/**
 * Pinecone Inference API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import * as runtime from '../runtime';
import type { EmbedRequest, EmbeddingsList, RerankRequest, RerankResult } from '../models/index';
export interface EmbedOperationRequest {
    embedRequest?: EmbedRequest;
}
export interface RerankOperationRequest {
    rerankRequest?: RerankRequest;
}
/**
 *
 */
export declare class InferenceApi extends runtime.BaseAPI {
    /**
     * Generate embeddings for input data.  For guidance and examples, see [Generate embeddings](https://docs.pinecone.io/guides/inference/generate-embeddings).
     * Embed data
     */
    embedRaw(requestParameters: EmbedOperationRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<EmbeddingsList>>;
    /**
     * Generate embeddings for input data.  For guidance and examples, see [Generate embeddings](https://docs.pinecone.io/guides/inference/generate-embeddings).
     * Embed data
     */
    embed(requestParameters?: EmbedOperationRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<EmbeddingsList>;
    /**
     * Rerank documents according to their relevance to a query.  For guidance and examples, see [Rerank documents](https://docs.pinecone.io/guides/inference/rerank).
     * Rerank documents
     */
    rerankRaw(requestParameters: RerankOperationRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<RerankResult>>;
    /**
     * Rerank documents according to their relevance to a query.  For guidance and examples, see [Rerank documents](https://docs.pinecone.io/guides/inference/rerank).
     * Rerank documents
     */
    rerank(requestParameters?: RerankOperationRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<RerankResult>;
}

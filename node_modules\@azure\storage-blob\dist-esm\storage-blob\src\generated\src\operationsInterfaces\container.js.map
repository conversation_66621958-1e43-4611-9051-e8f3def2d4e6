{"version": 3, "file": "container.js", "sourceRoot": "", "sources": ["../../../../../../src/generated/src/operationsInterfaces/container.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG", "sourcesContent": ["/*\n * Copyright (c) Microsoft Corporation.\n * Licensed under the MIT License.\n *\n * Code generated by Microsoft (R) AutoRest Code Generator.\n * Changes may cause incorrect behavior and will be lost if the code is regenerated.\n */\n\nimport * as coreRestPipeline from \"@azure/core-rest-pipeline\";\nimport {\n  ContainerCreateOptionalParams,\n  ContainerCreateResponse,\n  ContainerGetPropertiesOptionalParams,\n  ContainerGetPropertiesResponse,\n  ContainerDeleteOptionalParams,\n  ContainerDeleteResponse,\n  ContainerSetMetadataOptionalParams,\n  ContainerSetMetadataResponse,\n  ContainerGetAccessPolicyOptionalParams,\n  ContainerGetAccessPolicyResponse,\n  ContainerSetAccessPolicyOptionalParams,\n  ContainerSetAccessPolicyResponse,\n  ContainerRestoreOptionalParams,\n  ContainerRestoreResponse,\n  ContainerRenameOptionalParams,\n  ContainerRenameResponse,\n  ContainerSubmitBatchOptionalParams,\n  ContainerSubmitBatchResponse,\n  ContainerFilterBlobsOptionalParams,\n  ContainerFilterBlobsResponse,\n  ContainerAcquireLeaseOptionalParams,\n  ContainerAcquireLeaseResponse,\n  ContainerReleaseLeaseOptionalParams,\n  ContainerReleaseLeaseResponse,\n  ContainerRenewLeaseOptionalParams,\n  ContainerRenewLeaseResponse,\n  ContainerBreakLeaseOptionalParams,\n  ContainerBreakLeaseResponse,\n  ContainerChangeLeaseOptionalParams,\n  ContainerChangeLeaseResponse,\n  ContainerListBlobFlatSegmentOptionalParams,\n  ContainerListBlobFlatSegmentResponse,\n  ContainerListBlobHierarchySegmentOptionalParams,\n  ContainerListBlobHierarchySegmentResponse,\n  ContainerGetAccountInfoOptionalParams,\n  ContainerGetAccountInfoResponse,\n} from \"../models\";\n\n/** Interface representing a Container. */\nexport interface Container {\n  /**\n   * creates a new container under the specified account. If the container with the same name already\n   * exists, the operation fails\n   * @param options The options parameters.\n   */\n  create(\n    options?: ContainerCreateOptionalParams,\n  ): Promise<ContainerCreateResponse>;\n  /**\n   * returns all user-defined metadata and system properties for the specified container. The data\n   * returned does not include the container's list of blobs\n   * @param options The options parameters.\n   */\n  getProperties(\n    options?: ContainerGetPropertiesOptionalParams,\n  ): Promise<ContainerGetPropertiesResponse>;\n  /**\n   * operation marks the specified container for deletion. The container and any blobs contained within\n   * it are later deleted during garbage collection\n   * @param options The options parameters.\n   */\n  delete(\n    options?: ContainerDeleteOptionalParams,\n  ): Promise<ContainerDeleteResponse>;\n  /**\n   * operation sets one or more user-defined name-value pairs for the specified container.\n   * @param options The options parameters.\n   */\n  setMetadata(\n    options?: ContainerSetMetadataOptionalParams,\n  ): Promise<ContainerSetMetadataResponse>;\n  /**\n   * gets the permissions for the specified container. The permissions indicate whether container data\n   * may be accessed publicly.\n   * @param options The options parameters.\n   */\n  getAccessPolicy(\n    options?: ContainerGetAccessPolicyOptionalParams,\n  ): Promise<ContainerGetAccessPolicyResponse>;\n  /**\n   * sets the permissions for the specified container. The permissions indicate whether blobs in a\n   * container may be accessed publicly.\n   * @param options The options parameters.\n   */\n  setAccessPolicy(\n    options?: ContainerSetAccessPolicyOptionalParams,\n  ): Promise<ContainerSetAccessPolicyResponse>;\n  /**\n   * Restores a previously-deleted container.\n   * @param options The options parameters.\n   */\n  restore(\n    options?: ContainerRestoreOptionalParams,\n  ): Promise<ContainerRestoreResponse>;\n  /**\n   * Renames an existing container.\n   * @param sourceContainerName Required.  Specifies the name of the container to rename.\n   * @param options The options parameters.\n   */\n  rename(\n    sourceContainerName: string,\n    options?: ContainerRenameOptionalParams,\n  ): Promise<ContainerRenameResponse>;\n  /**\n   * The Batch operation allows multiple API calls to be embedded into a single HTTP request.\n   * @param contentLength The length of the request.\n   * @param multipartContentType Required. The value of this header must be multipart/mixed with a batch\n   *                             boundary. Example header value: multipart/mixed; boundary=batch_<GUID>\n   * @param body Initial data\n   * @param options The options parameters.\n   */\n  submitBatch(\n    contentLength: number,\n    multipartContentType: string,\n    body: coreRestPipeline.RequestBodyType,\n    options?: ContainerSubmitBatchOptionalParams,\n  ): Promise<ContainerSubmitBatchResponse>;\n  /**\n   * The Filter Blobs operation enables callers to list blobs in a container whose tags match a given\n   * search expression.  Filter blobs searches within the given container.\n   * @param options The options parameters.\n   */\n  filterBlobs(\n    options?: ContainerFilterBlobsOptionalParams,\n  ): Promise<ContainerFilterBlobsResponse>;\n  /**\n   * [Update] establishes and manages a lock on a container for delete operations. The lock duration can\n   * be 15 to 60 seconds, or can be infinite\n   * @param options The options parameters.\n   */\n  acquireLease(\n    options?: ContainerAcquireLeaseOptionalParams,\n  ): Promise<ContainerAcquireLeaseResponse>;\n  /**\n   * [Update] establishes and manages a lock on a container for delete operations. The lock duration can\n   * be 15 to 60 seconds, or can be infinite\n   * @param leaseId Specifies the current lease ID on the resource.\n   * @param options The options parameters.\n   */\n  releaseLease(\n    leaseId: string,\n    options?: ContainerReleaseLeaseOptionalParams,\n  ): Promise<ContainerReleaseLeaseResponse>;\n  /**\n   * [Update] establishes and manages a lock on a container for delete operations. The lock duration can\n   * be 15 to 60 seconds, or can be infinite\n   * @param leaseId Specifies the current lease ID on the resource.\n   * @param options The options parameters.\n   */\n  renewLease(\n    leaseId: string,\n    options?: ContainerRenewLeaseOptionalParams,\n  ): Promise<ContainerRenewLeaseResponse>;\n  /**\n   * [Update] establishes and manages a lock on a container for delete operations. The lock duration can\n   * be 15 to 60 seconds, or can be infinite\n   * @param options The options parameters.\n   */\n  breakLease(\n    options?: ContainerBreakLeaseOptionalParams,\n  ): Promise<ContainerBreakLeaseResponse>;\n  /**\n   * [Update] establishes and manages a lock on a container for delete operations. The lock duration can\n   * be 15 to 60 seconds, or can be infinite\n   * @param leaseId Specifies the current lease ID on the resource.\n   * @param proposedLeaseId Proposed lease ID, in a GUID string format. The Blob service returns 400\n   *                        (Invalid request) if the proposed lease ID is not in the correct format. See Guid Constructor\n   *                        (String) for a list of valid GUID string formats.\n   * @param options The options parameters.\n   */\n  changeLease(\n    leaseId: string,\n    proposedLeaseId: string,\n    options?: ContainerChangeLeaseOptionalParams,\n  ): Promise<ContainerChangeLeaseResponse>;\n  /**\n   * [Update] The List Blobs operation returns a list of the blobs under the specified container\n   * @param options The options parameters.\n   */\n  listBlobFlatSegment(\n    options?: ContainerListBlobFlatSegmentOptionalParams,\n  ): Promise<ContainerListBlobFlatSegmentResponse>;\n  /**\n   * [Update] The List Blobs operation returns a list of the blobs under the specified container\n   * @param delimiter When the request includes this parameter, the operation returns a BlobPrefix\n   *                  element in the response body that acts as a placeholder for all blobs whose names begin with the\n   *                  same substring up to the appearance of the delimiter character. The delimiter may be a single\n   *                  character or a string.\n   * @param options The options parameters.\n   */\n  listBlobHierarchySegment(\n    delimiter: string,\n    options?: ContainerListBlobHierarchySegmentOptionalParams,\n  ): Promise<ContainerListBlobHierarchySegmentResponse>;\n  /**\n   * Returns the sku name and account kind\n   * @param options The options parameters.\n   */\n  getAccountInfo(\n    options?: ContainerGetAccountInfoOptionalParams,\n  ): Promise<ContainerGetAccountInfoResponse>;\n}\n"]}
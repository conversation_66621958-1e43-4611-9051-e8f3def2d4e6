"use strict";
/* tslint:disable */
/* eslint-disable */
/**
 * Pinecone Control Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateCollectionRequestToJSON = exports.CreateCollectionRequestFromJSONTyped = exports.CreateCollectionRequestFromJSON = exports.instanceOfCreateCollectionRequest = void 0;
/**
 * Check if a given object implements the CreateCollectionRequest interface.
 */
function instanceOfCreateCollectionRequest(value) {
    var isInstance = true;
    isInstance = isInstance && "name" in value;
    isInstance = isInstance && "source" in value;
    return isInstance;
}
exports.instanceOfCreateCollectionRequest = instanceOfCreateCollectionRequest;
function CreateCollectionRequestFromJSON(json) {
    return CreateCollectionRequestFromJSONTyped(json, false);
}
exports.CreateCollectionRequestFromJSON = CreateCollectionRequestFromJSON;
function CreateCollectionRequestFromJSONTyped(json, ignoreDiscriminator) {
    if ((json === undefined) || (json === null)) {
        return json;
    }
    return {
        'name': json['name'],
        'source': json['source'],
    };
}
exports.CreateCollectionRequestFromJSONTyped = CreateCollectionRequestFromJSONTyped;
function CreateCollectionRequestToJSON(value) {
    if (value === undefined) {
        return undefined;
    }
    if (value === null) {
        return null;
    }
    return {
        'name': value.name,
        'source': value.source,
    };
}
exports.CreateCollectionRequestToJSON = CreateCollectionRequestToJSON;
//# sourceMappingURL=CreateCollectionRequest.js.map
"use strict";
/* tslint:disable */
/* eslint-disable */
/**
 * Pinecone Data Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.VectorToJSON = exports.VectorFromJSONTyped = exports.VectorFromJSON = exports.instanceOfVector = void 0;
var runtime_1 = require("../runtime");
var SparseValues_1 = require("./SparseValues");
/**
 * Check if a given object implements the Vector interface.
 */
function instanceOfVector(value) {
    var isInstance = true;
    isInstance = isInstance && "id" in value;
    isInstance = isInstance && "values" in value;
    return isInstance;
}
exports.instanceOfVector = instanceOfVector;
function VectorFromJSON(json) {
    return VectorFromJSONTyped(json, false);
}
exports.VectorFromJSON = VectorFromJSON;
function VectorFromJSONTyped(json, ignoreDiscriminator) {
    if ((json === undefined) || (json === null)) {
        return json;
    }
    return {
        'id': json['id'],
        'values': json['values'],
        'sparseValues': !(0, runtime_1.exists)(json, 'sparseValues') ? undefined : (0, SparseValues_1.SparseValuesFromJSON)(json['sparseValues']),
        'metadata': !(0, runtime_1.exists)(json, 'metadata') ? undefined : json['metadata'],
    };
}
exports.VectorFromJSONTyped = VectorFromJSONTyped;
function VectorToJSON(value) {
    if (value === undefined) {
        return undefined;
    }
    if (value === null) {
        return null;
    }
    return {
        'id': value.id,
        'values': value.values,
        'sparseValues': (0, SparseValues_1.SparseValuesToJSON)(value.sparseValues),
        'metadata': value.metadata,
    };
}
exports.VectorToJSON = VectorToJSON;
//# sourceMappingURL=Vector.js.map
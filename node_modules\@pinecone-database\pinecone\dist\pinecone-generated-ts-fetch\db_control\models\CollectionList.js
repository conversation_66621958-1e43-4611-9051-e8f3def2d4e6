"use strict";
/* tslint:disable */
/* eslint-disable */
/**
 * Pinecone Control Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.CollectionListToJSON = exports.CollectionListFromJSONTyped = exports.CollectionListFromJSON = exports.instanceOfCollectionList = void 0;
var runtime_1 = require("../runtime");
var CollectionModel_1 = require("./CollectionModel");
/**
 * Check if a given object implements the CollectionList interface.
 */
function instanceOfCollectionList(value) {
    var isInstance = true;
    return isInstance;
}
exports.instanceOfCollectionList = instanceOfCollectionList;
function CollectionListFromJSON(json) {
    return CollectionListFromJSONTyped(json, false);
}
exports.CollectionListFromJSON = CollectionListFromJSON;
function CollectionListFromJSONTyped(json, ignoreDiscriminator) {
    if ((json === undefined) || (json === null)) {
        return json;
    }
    return {
        'collections': !(0, runtime_1.exists)(json, 'collections') ? undefined : (json['collections'].map(CollectionModel_1.CollectionModelFromJSON)),
    };
}
exports.CollectionListFromJSONTyped = CollectionListFromJSONTyped;
function CollectionListToJSON(value) {
    if (value === undefined) {
        return undefined;
    }
    if (value === null) {
        return null;
    }
    return {
        'collections': value.collections === undefined ? undefined : (value.collections.map(CollectionModel_1.CollectionModelToJSON)),
    };
}
exports.CollectionListToJSON = CollectionListToJSON;
//# sourceMappingURL=CollectionList.js.map
{"version": 3, "file": "ProtoSchema.js", "sourceRoot": "", "sources": ["../src/ProtoSchema.ts"], "names": [], "mappings": ";;;;;AACA,4DAAiC;AACjC,4CAAmF;AACnF,qCAGiB;AAEjB,MAAqB,WAAW;IAG9B,YAAY,MAA4B,EAAE,IAAmB;QAC3D,MAAM,aAAa,GAAG,oBAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;QACnD,MAAM,IAAI,GAAG,aAAa,CAAC,IAAI,CAAA;QAC/B,MAAM,iBAAiB,GAAG,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,iBAAiB,CAAA;QAEjD,gEAAgE;QAChE,IAAI,iBAAiB,EAAE,CAAC;YACtB,iBAAiB,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC,oBAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,MAAgB,EAAE,IAAI,CAAC,CAAC,CAAA;QAC1F,CAAC;QAED,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC,CAAA;IACvE,CAAC;IAEO,iBAAiB,CAAC,MAAqD;QAC7E,IAAI,CAAC,MAAM;YAAE,MAAM,IAAI,6CAAoC,CAAC,kBAAkB,CAAC,CAAA;QAC/E,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;QAChC,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAA;QAElC,sGAAsG;QACtG,IAAI,UAAU,YAAY,iBAAS,IAAI,CAAC,CAAC,UAAU,YAAY,YAAI,CAAC,IAAI,UAAU,CAAC,MAAM;YACvF,OAAO,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,MAAM,CAAC,CAAA;QAClD,OAAO,IAAI,CAAC,CAAC,CAAC,CAAA;IAChB,CAAC;IAEO,WAAW,CAAC,aAA4B,EAAE,IAAmB;QACnE,MAAM,IAAI,GAAG,aAAa,CAAC,IAAI,CAAA;QAC/B,MAAM,GAAG,GAAG,aAAa,CAAC,OAAO,CAAA;QACjC,MAAM,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;QAC9F,OAAO,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,IAAI,EAAE,CAAA;IAC1C,CAAC;IAEO,SAAS,CAAC,MAAc;QAC9B,MAAM,KAAK,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,KAAa,EAAE,EAAE,CAAC,KAAK,IAAI,CAAC,CAAC,CAAA;QAC7D,OAAO,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;IAC5B,CAAC;IAEM,QAAQ,CAAC,OAAe;QAC7B,MAAM,KAAK,GAAe,EAAE,CAAA;QAC5B,IACE,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;YACrB,SAAS,EAAE,CAAC,IAAmB,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;SACrD,CAAC,EACF,CAAC;YACD,MAAM,IAAI,+CAAsC,CAAC,iBAAiB,EAAE,KAAK,CAAC,CAAA;QAC5E,CAAC;QAED,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;QACjD,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,MAAM,EAAE,CAAC,CAAA;IAChE,CAAC;IAEM,UAAU,CAAC,MAAc;QAC9B,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;QACxC,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,CAAA;IACvC,CAAC;IAEM,OAAO,CACZ,OAAe,EACf,IAA2E;QAE3E,MAAM,MAAM,GAAkB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;QAC1D,IAAI,MAAM,EAAE,CAAC;YACX,IAAI,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,SAAS,EAAE,CAAC;gBACpB,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,EAAE,OAAO,CAAC,CAAA;YACnC,CAAC;YACD,OAAO,KAAK,CAAA;QACd,CAAC;QACD,OAAO,IAAI,CAAA;IACb,CAAC;CACF;AAvED,8BAuEC"}
/**
 * Pinecone Data Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import type { Usage } from './Usage';
import type { Vector } from './Vector';
/**
 * The response for the `fetch` operation.
 * @export
 * @interface FetchResponse
 */
export interface FetchResponse {
    /**
     *
     * @type {{ [key: string]: Vector; }}
     * @memberof FetchResponse
     */
    vectors?: {
        [key: string]: Vector;
    };
    /**
     * The namespace of the vectors.
     * @type {string}
     * @memberof FetchResponse
     */
    namespace?: string;
    /**
     *
     * @type {Usage}
     * @memberof FetchResponse
     */
    usage?: Usage;
}
/**
 * Check if a given object implements the FetchResponse interface.
 */
export declare function instanceOfFetchResponse(value: object): boolean;
export declare function FetchResponseFromJSON(json: any): FetchResponse;
export declare function FetchResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): FetchResponse;
export declare function FetchResponseToJSON(value?: FetchResponse | null): any;

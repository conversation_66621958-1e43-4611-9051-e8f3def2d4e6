{"version": 3, "file": "PooledBuffer.js", "sourceRoot": "", "sources": ["../../../../storage-common/src/PooledBuffer.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAGhD;;GAEG;AACH,OAAO,MAAM,MAAM,QAAQ,CAAC;AAC5B,MAAM,eAAe,GAAG,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC;AAEpD;;;;;;;GAOG;AACH,MAAM,OAAO,YAAY;IAiBvB;;OAEG;IACH,IAAW,IAAI;QACb,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;IAsBD,YAAY,QAAgB,EAAE,OAAkB,EAAE,WAAoB;QA3CtE;;;WAGG;QACK,YAAO,GAAa,EAAE,CAAC;QAwC7B,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;QAEf,WAAW;QACX,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,eAAe,CAAC,CAAC;QACxD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE,CAAC;YACnC,IAAI,GAAG,GAAG,CAAC,KAAK,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,eAAe,CAAC,CAAC,CAAC,eAAe,CAAC;YAC7E,IAAI,GAAG,KAAK,CAAC,EAAE,CAAC;gBACd,GAAG,GAAG,eAAe,CAAC;YACxB,CAAC;YACD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC;QAC7C,CAAC;QAED,IAAI,OAAO,EAAE,CAAC;YACZ,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,WAAY,CAAC,CAAC;QACnC,CAAC;IACH,CAAC;IAED;;;;;;;;OAQG;IACI,IAAI,CAAC,OAAiB,EAAE,WAAmB;QAChD,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;QAElD,IAAI,CAAC,GAAG,CAAC,EACP,CAAC,GAAG,CAAC,EACL,YAAY,GAAG,CAAC,EAChB,YAAY,GAAG,CAAC,EAChB,cAAc,GAAG,CAAC,CAAC;QACrB,OAAO,cAAc,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;YACnC,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YAC1B,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YAC/B,MAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,YAAY,EAAE,YAAY,CAAC,CAAC;YAElE,cAAc,IAAI,SAAS,CAAC;YAC5B,YAAY,IAAI,SAAS,CAAC;YAC1B,YAAY,IAAI,SAAS,CAAC;YAC1B,IAAI,YAAY,KAAK,MAAM,CAAC,MAAM,EAAE,CAAC;gBACnC,CAAC,EAAE,CAAC;gBACJ,YAAY,GAAG,CAAC,CAAC;YACnB,CAAC;YACD,IAAI,YAAY,KAAK,MAAM,CAAC,MAAM,EAAE,CAAC;gBACnC,CAAC,EAAE,CAAC;gBACJ,YAAY,GAAG,CAAC,CAAC;YACnB,CAAC;QACH,CAAC;QAED,mCAAmC;QACnC,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACrB,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACvB,OAAO,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;QAC9C,CAAC;IACH,CAAC;IAED;;;OAGG;IACI,iBAAiB;QACtB,OAAO,IAAI,aAAa,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;IACpD,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport { BuffersStream } from \"./BuffersStream\";\nimport { Readable } from \"stream\";\n\n/**\n * maxBufferLength is max size of each buffer in the pooled buffers.\n */\nimport buffer from \"buffer\";\nconst maxBufferLength = buffer.constants.MAX_LENGTH;\n\n/**\n * This class provides a buffer container which conceptually has no hard size limit.\n * It accepts a capacity, an array of input buffers and the total length of input data.\n * It will allocate an internal \"buffer\" of the capacity and fill the data in the input buffers\n * into the internal \"buffer\" serially with respect to the total length.\n * Then by calling PooledBuffer.getReadableStream(), you can get a readable stream\n * assembled from all the data in the internal \"buffer\".\n */\nexport class PooledBuffer {\n  /**\n   * Internal buffers used to keep the data.\n   * Each buffer has a length of the maxBufferLength except last one.\n   */\n  private buffers: Buffer[] = [];\n\n  /**\n   * The total size of internal buffers.\n   */\n  private readonly capacity: number;\n\n  /**\n   * The total size of data contained in internal buffers.\n   */\n  private _size: number;\n\n  /**\n   * The size of the data contained in the pooled buffers.\n   */\n  public get size(): number {\n    return this._size;\n  }\n\n  /**\n   * Creates an instance of <PERSON><PERSON><PERSON>uff<PERSON> with given capacity.\n   * Internal buffers are allocated but contains no data.\n   * Users may call the {@link PooledBuffer.fill} method to fill this\n   * pooled buffer with data.\n   *\n   * @param capacity - Total capacity of the internal buffers\n   */\n  constructor(capacity: number);\n\n  /**\n   * Creates an instance of PooledBuffer with given capacity.\n   * Internal buffers are allocated and filled with data in the input buffers serially\n   * with respect to the total length.\n   *\n   * @param capacity - Total capacity of the internal buffers\n   * @param buffers - Input buffers containing the data to be filled in the pooled buffer\n   * @param totalLength - Total length of the data to be filled in.\n   */\n  constructor(capacity: number, buffers: Buffer[], totalLength: number);\n  constructor(capacity: number, buffers?: Buffer[], totalLength?: number) {\n    this.capacity = capacity;\n    this._size = 0;\n\n    // allocate\n    const bufferNum = Math.ceil(capacity / maxBufferLength);\n    for (let i = 0; i < bufferNum; i++) {\n      let len = i === bufferNum - 1 ? capacity % maxBufferLength : maxBufferLength;\n      if (len === 0) {\n        len = maxBufferLength;\n      }\n      this.buffers.push(Buffer.allocUnsafe(len));\n    }\n\n    if (buffers) {\n      this.fill(buffers, totalLength!);\n    }\n  }\n\n  /**\n   * Fill the internal buffers with data in the input buffers serially\n   * with respect to the total length and the total capacity of the internal buffers.\n   * Data copied will be shift out of the input buffers.\n   *\n   * @param buffers - Input buffers containing the data to be filled in the pooled buffer\n   * @param totalLength - Total length of the data to be filled in.\n   *\n   */\n  public fill(buffers: Buffer[], totalLength: number) {\n    this._size = Math.min(this.capacity, totalLength);\n\n    let i = 0,\n      j = 0,\n      targetOffset = 0,\n      sourceOffset = 0,\n      totalCopiedNum = 0;\n    while (totalCopiedNum < this._size) {\n      const source = buffers[i];\n      const target = this.buffers[j];\n      const copiedNum = source.copy(target, targetOffset, sourceOffset);\n\n      totalCopiedNum += copiedNum;\n      sourceOffset += copiedNum;\n      targetOffset += copiedNum;\n      if (sourceOffset === source.length) {\n        i++;\n        sourceOffset = 0;\n      }\n      if (targetOffset === target.length) {\n        j++;\n        targetOffset = 0;\n      }\n    }\n\n    // clear copied from source buffers\n    buffers.splice(0, i);\n    if (buffers.length > 0) {\n      buffers[0] = buffers[0].slice(sourceOffset);\n    }\n  }\n\n  /**\n   * Get the readable stream assembled from all the data in the internal buffers.\n   *\n   */\n  public getReadableStream(): Readable {\n    return new BuffersStream(this.buffers, this.size);\n  }\n}\n"]}
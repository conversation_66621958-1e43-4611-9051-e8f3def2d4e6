"use strict";
/* tslint:disable */
/* eslint-disable */
/**
 * Pinecone Data Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ListImportsResponseToJSON = exports.ListImportsResponseFromJSONTyped = exports.ListImportsResponseFromJSON = exports.instanceOfListImportsResponse = void 0;
var runtime_1 = require("../runtime");
var ImportModel_1 = require("./ImportModel");
var Pagination_1 = require("./Pagination");
/**
 * Check if a given object implements the ListImportsResponse interface.
 */
function instanceOfListImportsResponse(value) {
    var isInstance = true;
    return isInstance;
}
exports.instanceOfListImportsResponse = instanceOfListImportsResponse;
function ListImportsResponseFromJSON(json) {
    return ListImportsResponseFromJSONTyped(json, false);
}
exports.ListImportsResponseFromJSON = ListImportsResponseFromJSON;
function ListImportsResponseFromJSONTyped(json, ignoreDiscriminator) {
    if ((json === undefined) || (json === null)) {
        return json;
    }
    return {
        'data': !(0, runtime_1.exists)(json, 'data') ? undefined : (json['data'].map(ImportModel_1.ImportModelFromJSON)),
        'pagination': !(0, runtime_1.exists)(json, 'pagination') ? undefined : (0, Pagination_1.PaginationFromJSON)(json['pagination']),
    };
}
exports.ListImportsResponseFromJSONTyped = ListImportsResponseFromJSONTyped;
function ListImportsResponseToJSON(value) {
    if (value === undefined) {
        return undefined;
    }
    if (value === null) {
        return null;
    }
    return {
        'data': value.data === undefined ? undefined : (value.data.map(ImportModel_1.ImportModelToJSON)),
        'pagination': (0, Pagination_1.PaginationToJSON)(value.pagination),
    };
}
exports.ListImportsResponseToJSON = ListImportsResponseToJSON;
//# sourceMappingURL=ListImportsResponse.js.map
{"version": 3, "file": "AvroHelper.js", "sourceRoot": "", "sources": ["../src/AvroHelper.ts"], "names": [], "mappings": ";;;;;AAUA,qCAA+D;AAC/D,gDAA2D;AAC3D,qCAAqD;AAGrD,MAAqB,UAAU;IACrB,gBAAgB,CAAC,MAAuB;QAC9C,OAAO,CAAC,OAAO,MAAM,CAAC,MAAM,KAAK,QAAQ;YACvC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC;YAC3B,CAAC,CAAC,MAAM,CAAC,MAAM,CAAkB,CAAA;IACrC,CAAC;IAEM,aAAa,CAAC,MAAuC,EAAE,IAAkB;QAC9E,MAAM,SAAS,GAAkB,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC;YAC3D,CAAC,CAAC,MAAM;YACR,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAA;QACjC,6CAA6C;QAE7C,MAAM,oBAAoB,GAAG,CAAC,QAAmB,EAAY,EAAE,CAAC,CAC9D,MAAmB,EACnB,IAAsB,EACtB,EAAE;;YACF,MAAM,QAAQ,GAAG,IAAmB,CAAA;YACpC,MAAA,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,iBAAiB,0CAAE,OAAO,CAAC,SAAS,CAAC,EAAE;gBAC/C,MAAM,YAAY,GAAG,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAA;gBACrD,QAAQ,CAAC,QAAQ,GAAG,QAAQ,CAAA;gBAC5B,cAAI,CAAC,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAA;YAC7C,CAAC,CAAC,CAAA;YACF,IAAI,QAAQ,EAAE,CAAC;gBACb,OAAO,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;YAC/B,CAAC;QACH,CAAC,CAAA;QAED,MAAM,UAAU,GAAG,cAAI,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;YAChD,GAAG,IAAI;YACP,QAAQ,EAAE,oBAAoB,CAAC,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,QAAQ,CAAC;SAC/C,CAAC,CAAA;QAEF,OAAO,UAAU,CAAA;IACnB,CAAC;IAEM,QAAQ,CAAC,UAAsB;QACpC,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;YACrB,MAAM,IAAI,6CAAoC,CAAC,iBAAiB,UAAU,CAAC,IAAI,EAAE,CAAC,CAAA;QACpF,CAAC;IACH,CAAC;IAEM,UAAU,CACf,MAA2B,EAC3B,WAAuB,EACvB,SAAiB;QAEjB,MAAM,SAAS,GAAkB,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAA;QAE9D,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC;YACzB,MAAM,IAAI,6CAAoC,CAAC,sBAAsB,SAAS,CAAC,SAAS,EAAE,CAAC,CAAA;QAC7F,CAAC;QAED,MAAM,OAAO,GAAqB;YAChC,IAAI,EAAE,CAAC,SAAS,CAAC,SAAS,EAAE,SAAS,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC;SAC5D,CAAA;QACD,OAAO,OAAO,CAAA;IAChB,CAAC;IAEO,eAAe,CAAC,MAAuC;QAC7D,MAAM,eAAe,GAAG,MAAuB,CAAA;QAC/C,OAAO,eAAe,CAAC,IAAI,IAAI,IAAI,IAAI,eAAe,CAAC,IAAI,IAAI,IAAI,CAAA;IACrE,CAAC;IAEM,iBAAiB,CAAC,IAAoB;QAC3C,OAAO,EAAE,IAAI,EAAE,mBAAU,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,UAAU,EAAE,IAAI,CAAC,UAAU,EAAE,CAAA;IACpF,CAAC;IAED,iCAAiC,CAC/B,iBAAwC,EACxC,UAA2B,EAAE;QAE7B,OAAO,EAAE,GAAG,OAAO,EAAE,CAAC,mBAAU,CAAC,IAAI,CAAC,EAAE,EAAE,GAAG,OAAO,CAAC,mBAAU,CAAC,IAAI,CAAC,EAAE,iBAAiB,EAAE,EAAE,CAAA;IAC9F,CAAC;CACF;AA1ED,6BA0EC"}
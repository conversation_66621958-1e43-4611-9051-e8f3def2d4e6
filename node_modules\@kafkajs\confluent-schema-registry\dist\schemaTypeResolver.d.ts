import { SchemaType, SchemaHelper, ConfluentSchema, SchemaRegistryAPIClientOptions, Schema, AvroSchema } from './@types';
export declare const schemaTypeFromString: (schemaTypeString: string) => SchemaType;
export declare const helperTypeFromSchemaType: (schemaType?: SchemaType) => SchemaHelper;
export declare const schemaFromConfluentSchema: (confluentSchema: ConfluentSchema, options?: SchemaRegistryAPIClientOptions) => Schema | AvroSchema;

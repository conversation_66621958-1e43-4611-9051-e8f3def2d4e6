# Meta AI Agent Builder - Usage Guide

## 🚀 What This Does
Your Meta AI Agent Builder can create ANY n8n workflow or AI agent from a single natural language instruction!

## 📥 How to Import the Workflow

1. **Open n8n** at http://localhost:2410
2. **Click "Import from file"** or go to Settings > Import
3. **Upload** the `meta-ai-agent-builder.json` file
4. **Configure OpenAI credentials** (required for the AI workflow generator)

## ⚙️ Setup Requirements

### 1. OpenAI API Key
- Go to **Credentials** in n8n
- Add **OpenAI** credential
- Enter your OpenAI API key

### 2. n8n API Access (Optional)
- For advanced features, you may need to enable n8n API access
- Check your n8n settings for API configuration

## 🎯 How to Use Your Meta Agent

### Method 1: Direct API Call
Send a POST request to: `http://localhost:2410/webhook/build-agent`

```json
{
  "instruction": "Create a chatbot that answers customer questions about our products using OpenAI"
}
```

### Method 2: Using curl (Command Line)
```bash
curl -X POST http://localhost:2410/webhook/build-agent \
  -H "Content-Type: application/json" \
  -d '{"instruction": "Build an email automation that sends welcome emails to new subscribers"}'
```

### Method 3: Using Postman/Insomnia
- URL: `http://localhost:2410/webhook/build-agent`
- Method: POST
- Body: JSON with your instruction

## 💡 Example Instructions You Can Give

### **Chatbots & AI Assistants**
- "Create a customer support chatbot using OpenAI"
- "Build a FAQ bot that answers questions about our services"
- "Make an AI assistant that helps with product recommendations"

### **Content Generation**
- "Create a blog post generator that writes articles about technology"
- "Build a social media content creator for LinkedIn posts"
- "Make an email newsletter generator"

### **Data Processing**
- "Create a workflow that analyzes customer feedback sentiment"
- "Build a data processor that extracts information from PDFs"
- "Make a report generator from CSV data"

### **Automation & Integration**
- "Create a workflow that monitors website uptime and sends alerts"
- "Build an automation that syncs data between Google Sheets and database"
- "Make a workflow that processes form submissions and sends emails"

### **E-commerce & Business**
- "Create an order processing automation"
- "Build a customer onboarding workflow"
- "Make an inventory management system"

## 📋 What Happens After You Send an Instruction

1. **AI Analysis**: Your instruction is analyzed by GPT-4
2. **Workflow Generation**: A complete n8n workflow is created
3. **Automatic Import**: The workflow is added to your n8n instance
4. **Response**: You get a success message with next steps

## ✅ Success Response Example
```json
{
  "success": true,
  "message": "AI Agent workflow created successfully!",
  "workflowId": "abc123",
  "workflowName": "Customer Support Chatbot",
  "instruction": "Create a customer support chatbot using OpenAI",
  "webhookUrl": "http://localhost:2410/webhook/abc123",
  "nextSteps": [
    "Go to your n8n workflows tab",
    "Find the new workflow: Customer Support Chatbot",
    "Configure any required credentials",
    "Activate the workflow when ready"
  ]
}
```

## 🔧 After Creation - Next Steps

1. **Find Your New Workflow**: Go to the Workflows tab in n8n
2. **Configure Credentials**: Set up any required API keys or credentials
3. **Test the Workflow**: Run a test execution
4. **Activate**: Turn on the workflow when ready
5. **Use**: Start using your new AI agent!

## 🎉 You're Ready!

Your Meta AI Agent Builder is now ready to create unlimited AI agents and workflows. Just describe what you need in plain English, and watch it build the complete automation for you!

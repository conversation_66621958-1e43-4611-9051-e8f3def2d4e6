import type { PineconeConfiguration } from './vectors/types';
import type { IndexName } from '../control';
export declare const IndexHostSingleton: {
    getHostUrl: (config: PineconeConfiguration, indexName: IndexName) => Promise<any>;
    _reset: () => void;
    _set: (config: PineconeConfiguration, indexName: IndexName, hostUrl: string) => void;
    _delete: (config: PineconeConfiguration, indexName: IndexName) => void;
};

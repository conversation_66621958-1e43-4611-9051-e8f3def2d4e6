/**
 * Pinecone Data Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import type { ImportModel } from './ImportModel';
import type { Pagination } from './Pagination';
/**
 * The response for the `list_imports` operation.
 * @export
 * @interface ListImportsResponse
 */
export interface ListImportsResponse {
    /**
     *
     * @type {Array<ImportModel>}
     * @memberof ListImportsResponse
     */
    data?: Array<ImportModel>;
    /**
     *
     * @type {Pagination}
     * @memberof ListImportsResponse
     */
    pagination?: Pagination;
}
/**
 * Check if a given object implements the ListImportsResponse interface.
 */
export declare function instanceOfListImportsResponse(value: object): boolean;
export declare function ListImportsResponseFromJSON(json: any): ListImportsResponse;
export declare function ListImportsResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): ListImportsResponse;
export declare function ListImportsResponseToJSON(value?: ListImportsResponse | null): any;

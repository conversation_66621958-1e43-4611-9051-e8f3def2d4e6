"use strict";
/* tslint:disable */
/* eslint-disable */
/**
 * Pinecone Data Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.DescribeIndexStatsRequestToJSON = exports.DescribeIndexStatsRequestFromJSONTyped = exports.DescribeIndexStatsRequestFromJSON = exports.instanceOfDescribeIndexStatsRequest = void 0;
var runtime_1 = require("../runtime");
/**
 * Check if a given object implements the DescribeIndexStatsRequest interface.
 */
function instanceOfDescribeIndexStatsRequest(value) {
    var isInstance = true;
    return isInstance;
}
exports.instanceOfDescribeIndexStatsRequest = instanceOfDescribeIndexStatsRequest;
function DescribeIndexStatsRequestFromJSON(json) {
    return DescribeIndexStatsRequestFromJSONTyped(json, false);
}
exports.DescribeIndexStatsRequestFromJSON = DescribeIndexStatsRequestFromJSON;
function DescribeIndexStatsRequestFromJSONTyped(json, ignoreDiscriminator) {
    if ((json === undefined) || (json === null)) {
        return json;
    }
    return {
        'filter': !(0, runtime_1.exists)(json, 'filter') ? undefined : json['filter'],
    };
}
exports.DescribeIndexStatsRequestFromJSONTyped = DescribeIndexStatsRequestFromJSONTyped;
function DescribeIndexStatsRequestToJSON(value) {
    if (value === undefined) {
        return undefined;
    }
    if (value === null) {
        return null;
    }
    return {
        'filter': value.filter,
    };
}
exports.DescribeIndexStatsRequestToJSON = DescribeIndexStatsRequestToJSON;
//# sourceMappingURL=DescribeIndexStatsRequest.js.map
"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
var _Float16Array = require("./Float16Array.cjs");
exports.Float16Array = _Float16Array.Float16Array;
exports.isFloat16Array = _Float16Array.isFloat16Array;
var _isTypedArray = require("./isTypedArray.cjs");
exports.isTypedArray = _isTypedArray.isTypedArray;
var _DataView = require("./DataView.cjs");
exports.getFloat16 = _DataView.getFloat16;
exports.setFloat16 = _DataView.setFloat16;
var _f16round = require("./f16round.cjs");
exports.f16round = _f16round.f16round;
exports.hfround = _f16round.f16round;
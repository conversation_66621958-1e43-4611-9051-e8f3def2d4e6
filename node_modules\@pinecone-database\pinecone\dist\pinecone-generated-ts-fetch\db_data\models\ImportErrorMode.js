"use strict";
/* tslint:disable */
/* eslint-disable */
/**
 * Pinecone Data Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ImportErrorModeToJSON = exports.ImportErrorModeFromJSONTyped = exports.ImportErrorModeFromJSON = exports.instanceOfImportErrorMode = exports.ImportErrorModeOnErrorEnum = void 0;
var runtime_1 = require("../runtime");
/**
 * @export
 */
exports.ImportErrorModeOnErrorEnum = {
    Abort: 'abort',
    Continue: 'continue'
};
/**
 * Check if a given object implements the ImportErrorMode interface.
 */
function instanceOfImportErrorMode(value) {
    var isInstance = true;
    return isInstance;
}
exports.instanceOfImportErrorMode = instanceOfImportErrorMode;
function ImportErrorModeFromJSON(json) {
    return ImportErrorModeFromJSONTyped(json, false);
}
exports.ImportErrorModeFromJSON = ImportErrorModeFromJSON;
function ImportErrorModeFromJSONTyped(json, ignoreDiscriminator) {
    if ((json === undefined) || (json === null)) {
        return json;
    }
    return {
        'onError': !(0, runtime_1.exists)(json, 'onError') ? undefined : json['onError'],
    };
}
exports.ImportErrorModeFromJSONTyped = ImportErrorModeFromJSONTyped;
function ImportErrorModeToJSON(value) {
    if (value === undefined) {
        return undefined;
    }
    if (value === null) {
        return null;
    }
    return {
        'onError': value.onError,
    };
}
exports.ImportErrorModeToJSON = ImportErrorModeToJSON;
//# sourceMappingURL=ImportErrorMode.js.map
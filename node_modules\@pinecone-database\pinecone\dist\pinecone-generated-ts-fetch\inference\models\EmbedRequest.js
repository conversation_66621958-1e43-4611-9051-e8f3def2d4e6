"use strict";
/* tslint:disable */
/* eslint-disable */
/**
 * Pinecone Inference API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmbedRequestToJSON = exports.EmbedRequestFromJSONTyped = exports.EmbedRequestFromJSON = exports.instanceOfEmbedRequest = void 0;
var runtime_1 = require("../runtime");
var EmbedRequestInputsInner_1 = require("./EmbedRequestInputsInner");
var EmbedRequestParameters_1 = require("./EmbedRequestParameters");
/**
 * Check if a given object implements the EmbedRequest interface.
 */
function instanceOfEmbedRequest(value) {
    var isInstance = true;
    isInstance = isInstance && "model" in value;
    isInstance = isInstance && "inputs" in value;
    return isInstance;
}
exports.instanceOfEmbedRequest = instanceOfEmbedRequest;
function EmbedRequestFromJSON(json) {
    return EmbedRequestFromJSONTyped(json, false);
}
exports.EmbedRequestFromJSON = EmbedRequestFromJSON;
function EmbedRequestFromJSONTyped(json, ignoreDiscriminator) {
    if ((json === undefined) || (json === null)) {
        return json;
    }
    return {
        'model': json['model'],
        'parameters': !(0, runtime_1.exists)(json, 'parameters') ? undefined : (0, EmbedRequestParameters_1.EmbedRequestParametersFromJSON)(json['parameters']),
        'inputs': (json['inputs'].map(EmbedRequestInputsInner_1.EmbedRequestInputsInnerFromJSON)),
    };
}
exports.EmbedRequestFromJSONTyped = EmbedRequestFromJSONTyped;
function EmbedRequestToJSON(value) {
    if (value === undefined) {
        return undefined;
    }
    if (value === null) {
        return null;
    }
    return {
        'model': value.model,
        'parameters': (0, EmbedRequestParameters_1.EmbedRequestParametersToJSON)(value.parameters),
        'inputs': (value.inputs.map(EmbedRequestInputsInner_1.EmbedRequestInputsInnerToJSON)),
    };
}
exports.EmbedRequestToJSON = EmbedRequestToJSON;
//# sourceMappingURL=EmbedRequest.js.map
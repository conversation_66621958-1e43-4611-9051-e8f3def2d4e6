// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
/**
 * The programmatic identifier of the storageCorrectContentLengthPolicy.
 */
export const storageCorrectContentLengthPolicyName = "StorageCorrectContentLengthPolicy";
/**
 * storageCorrectContentLengthPolicy to correctly set Content-Length header with request body length.
 */
export function storageCorrectContentLengthPolicy() {
    return {
        name: storageCorrectContentLengthPolicyName,
        async sendRequest(request, next) {
            return next(request);
        },
    };
}
//# sourceMappingURL=StorageCorrectContentLengthPolicy.browser.js.map
"use strict";
/* tslint:disable */
/* eslint-disable */
/**
 * Pinecone Data Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.StartImportRequestToJSON = exports.StartImportRequestFromJSONTyped = exports.StartImportRequestFromJSON = exports.instanceOfStartImportRequest = void 0;
var runtime_1 = require("../runtime");
var ImportErrorMode_1 = require("./ImportErrorMode");
/**
 * Check if a given object implements the StartImportRequest interface.
 */
function instanceOfStartImportRequest(value) {
    var isInstance = true;
    isInstance = isInstance && "uri" in value;
    return isInstance;
}
exports.instanceOfStartImportRequest = instanceOfStartImportRequest;
function StartImportRequestFromJSON(json) {
    return StartImportRequestFromJSONTyped(json, false);
}
exports.StartImportRequestFromJSON = StartImportRequestFromJSON;
function StartImportRequestFromJSONTyped(json, ignoreDiscriminator) {
    if ((json === undefined) || (json === null)) {
        return json;
    }
    return {
        'integrationId': !(0, runtime_1.exists)(json, 'integrationId') ? undefined : json['integrationId'],
        'uri': json['uri'],
        'errorMode': !(0, runtime_1.exists)(json, 'errorMode') ? undefined : (0, ImportErrorMode_1.ImportErrorModeFromJSON)(json['errorMode']),
    };
}
exports.StartImportRequestFromJSONTyped = StartImportRequestFromJSONTyped;
function StartImportRequestToJSON(value) {
    if (value === undefined) {
        return undefined;
    }
    if (value === null) {
        return null;
    }
    return {
        'integrationId': value.integrationId,
        'uri': value.uri,
        'errorMode': (0, ImportErrorMode_1.ImportErrorModeToJSON)(value.errorMode),
    };
}
exports.StartImportRequestToJSON = StartImportRequestToJSON;
//# sourceMappingURL=StartImportRequest.js.map
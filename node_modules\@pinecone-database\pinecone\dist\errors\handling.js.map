{"version": 3, "file": "handling.js", "sourceRoot": "", "sources": ["../../src/errors/handling.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,iCAAyC;AACzC,+BAA4C;AAC5C,qCAAoD;AAGpD,gBAAgB;AACT,IAAM,cAAc,GAAG,UAC5B,CAAU,EACV,aAGoB,EACpB,GAAY;;;;;qBAER,CAAA,CAAC,YAAY,KAAK,IAAI,CAAC,CAAC,IAAI,KAAK,eAAe,CAAA,EAAhD,wBAAgD;gBAC5C,aAAa,GAAG,CAAkB,CAAC;gBACtB,qBAAM,IAAA,sBAAc,EAAC,aAAa,CAAC,EAAA;;gBAAhD,UAAU,GAAG,SAAmC;gBAChD,UAAU,GAAG,aAAa,CAAC,QAAQ,CAAC,MAAM,CAAC;qBACjC,aAAa,EAAb,wBAAa;gBACzB,qBAAM,aAAa,CAAC,UAAU,EAAE,UAAU,CAAC,EAAA;;gBAA3C,KAAA,SAA2C,CAAA;;;gBAC3C,KAAA,UAAU,CAAA;;;gBAFR,OAAO,KAEC;gBAEd,sBAAO,IAAA,yBAAkB,EAAC;wBACxB,MAAM,EAAE,aAAa,CAAC,QAAQ,CAAC,MAAM;wBACrC,GAAG,EAAE,aAAa,CAAC,QAAQ,CAAC,GAAG,IAAI,GAAG;wBACtC,OAAO,EAAE,OAAO;qBACjB,CAAC,EAAC;;gBACE,IAAI,CAAC,YAAY,iCAAuB,EAAE;oBAC/C,sDAAsD;oBACtD,sBAAO,CAAC,EAAC;iBACV;qBAAM;oBAKC,GAAG,GAAG,CAAU,CAAC;oBACvB,sBAAO,IAAI,iCAAuB,CAAC,GAAG,CAAC,EAAC;iBACzC;;;;;KACF,CAAC;AAhCW,QAAA,cAAc,kBAgCzB"}
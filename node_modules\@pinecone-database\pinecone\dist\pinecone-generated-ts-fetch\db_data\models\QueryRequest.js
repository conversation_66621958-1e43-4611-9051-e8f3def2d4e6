"use strict";
/* tslint:disable */
/* eslint-disable */
/**
 * Pinecone Data Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.QueryRequestToJSON = exports.QueryRequestFromJSONTyped = exports.QueryRequestFromJSON = exports.instanceOfQueryRequest = void 0;
var runtime_1 = require("../runtime");
var QueryVector_1 = require("./QueryVector");
var SparseValues_1 = require("./SparseValues");
/**
 * Check if a given object implements the QueryRequest interface.
 */
function instanceOfQueryRequest(value) {
    var isInstance = true;
    isInstance = isInstance && "topK" in value;
    return isInstance;
}
exports.instanceOfQueryRequest = instanceOfQueryRequest;
function QueryRequestFromJSON(json) {
    return QueryRequestFromJSONTyped(json, false);
}
exports.QueryRequestFromJSON = QueryRequestFromJSON;
function QueryRequestFromJSONTyped(json, ignoreDiscriminator) {
    if ((json === undefined) || (json === null)) {
        return json;
    }
    return {
        'namespace': !(0, runtime_1.exists)(json, 'namespace') ? undefined : json['namespace'],
        'topK': json['topK'],
        'filter': !(0, runtime_1.exists)(json, 'filter') ? undefined : json['filter'],
        'includeValues': !(0, runtime_1.exists)(json, 'includeValues') ? undefined : json['includeValues'],
        'includeMetadata': !(0, runtime_1.exists)(json, 'includeMetadata') ? undefined : json['includeMetadata'],
        'queries': !(0, runtime_1.exists)(json, 'queries') ? undefined : (json['queries'].map(QueryVector_1.QueryVectorFromJSON)),
        'vector': !(0, runtime_1.exists)(json, 'vector') ? undefined : json['vector'],
        'sparseVector': !(0, runtime_1.exists)(json, 'sparseVector') ? undefined : (0, SparseValues_1.SparseValuesFromJSON)(json['sparseVector']),
        'id': !(0, runtime_1.exists)(json, 'id') ? undefined : json['id'],
    };
}
exports.QueryRequestFromJSONTyped = QueryRequestFromJSONTyped;
function QueryRequestToJSON(value) {
    if (value === undefined) {
        return undefined;
    }
    if (value === null) {
        return null;
    }
    return {
        'namespace': value.namespace,
        'topK': value.topK,
        'filter': value.filter,
        'includeValues': value.includeValues,
        'includeMetadata': value.includeMetadata,
        'queries': value.queries === undefined ? undefined : (value.queries.map(QueryVector_1.QueryVectorToJSON)),
        'vector': value.vector,
        'sparseVector': (0, SparseValues_1.SparseValuesToJSON)(value.sparseVector),
        'id': value.id,
    };
}
exports.QueryRequestToJSON = QueryRequestToJSON;
//# sourceMappingURL=QueryRequest.js.map
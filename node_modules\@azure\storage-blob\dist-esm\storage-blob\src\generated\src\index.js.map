{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../../../src/generated/src/index.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,cAAc,UAAU,CAAC;AACzB,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAChD,cAAc,wBAAwB,CAAC", "sourcesContent": ["/*\n * Copyright (c) Microsoft Corporation.\n * Licensed under the MIT License.\n *\n * Code generated by Microsoft (R) AutoRest Code Generator.\n * Changes may cause incorrect behavior and will be lost if the code is regenerated.\n */\n\nexport * from \"./models\";\nexport { StorageClient } from \"./storageClient\";\nexport * from \"./operationsInterfaces\";\n"]}
/**
 * Pinecone Data Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import * as runtime from '../runtime';
import type { ImportModel, ListImportsResponse, StartImportRequest, StartImportResponse } from '../models/index';
export interface CancelBulkImportRequest {
    id: string;
}
export interface DescribeBulkImportRequest {
    id: string;
}
export interface ListBulkImportsRequest {
    limit?: number;
    paginationToken?: string;
}
export interface StartBulkImportRequest {
    startImportRequest: StartImportRequest;
}
/**
 *
 */
export declare class BulkOperationsApi extends runtime.BaseAPI {
    /**
     * The `cancel_import` operation cancels an import operation if it is not yet finished. It has no effect if the operation is already finished. For guidance and examples, see [Import data](https://docs.pinecone.io/guides/data/import-data).
     * Cancel an import
     */
    cancelBulkImportRaw(requestParameters: CancelBulkImportRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<object>>;
    /**
     * The `cancel_import` operation cancels an import operation if it is not yet finished. It has no effect if the operation is already finished. For guidance and examples, see [Import data](https://docs.pinecone.io/guides/data/import-data).
     * Cancel an import
     */
    cancelBulkImport(requestParameters: CancelBulkImportRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<object>;
    /**
     * The `describe_import` operation returns details of a specific import operation. For guidance and examples,  see [Import data](https://docs.pinecone.io/guides/data/import-data).
     * Describe an import
     */
    describeBulkImportRaw(requestParameters: DescribeBulkImportRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<ImportModel>>;
    /**
     * The `describe_import` operation returns details of a specific import operation. For guidance and examples,  see [Import data](https://docs.pinecone.io/guides/data/import-data).
     * Describe an import
     */
    describeBulkImport(requestParameters: DescribeBulkImportRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<ImportModel>;
    /**
     * The `list_imports` operation lists all recent and ongoing import operations. For guidance and examples, see [Import data](https://docs.pinecone.io/guides/data/import-data).
     * List imports
     */
    listBulkImportsRaw(requestParameters: ListBulkImportsRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<ListImportsResponse>>;
    /**
     * The `list_imports` operation lists all recent and ongoing import operations. For guidance and examples, see [Import data](https://docs.pinecone.io/guides/data/import-data).
     * List imports
     */
    listBulkImports(requestParameters?: ListBulkImportsRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<ListImportsResponse>;
    /**
     * The `start_import` operation starts an asynchronous import of vectors from object storage into an index. For guidance and examples, see [Import data](https://docs.pinecone.io/guides/data/import-data).
     * Start import
     */
    startBulkImportRaw(requestParameters: StartBulkImportRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<StartImportResponse>>;
    /**
     * The `start_import` operation starts an asynchronous import of vectors from object storage into an index. For guidance and examples, see [Import data](https://docs.pinecone.io/guides/data/import-data).
     * Start import
     */
    startBulkImport(requestParameters: StartBulkImportRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<StartImportResponse>;
}

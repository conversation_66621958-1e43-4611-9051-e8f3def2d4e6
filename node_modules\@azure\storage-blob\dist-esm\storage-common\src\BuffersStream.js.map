{"version": 3, "file": "BuffersStream.js", "sourceRoot": "", "sources": ["../../../../storage-common/src/BuffersStream.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,QAAQ,EAAmB,MAAM,QAAQ,CAAC;AAOnD;;GAEG;AACH,MAAM,OAAO,aAAc,SAAQ,QAAQ;IAgBzC;;;;;;OAMG;IACH,YACU,OAAiB,EACjB,UAAkB,EAC1B,OAA8B;QAE9B,KAAK,CAAC,OAAO,CAAC,CAAC;QAJP,YAAO,GAAP,OAAO,CAAU;QACjB,eAAU,GAAV,UAAU,CAAQ;QAI1B,IAAI,CAAC,yBAAyB,GAAG,CAAC,CAAC;QACnC,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;QACrB,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC;QAE3B,4DAA4D;QAC5D,IAAI,aAAa,GAAG,CAAC,CAAC;QACtB,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YAC/B,aAAa,IAAI,GAAG,CAAC,UAAU,CAAC;QAClC,CAAC;QACD,IAAI,aAAa,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;YACpC,MAAM,IAAI,KAAK,CAAC,iEAAiE,CAAC,CAAC;QACrF,CAAC;IACH,CAAC;IAED;;;;OAIG;IACI,KAAK,CAAC,IAAa;QACxB,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YAC9C,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAClB,CAAC;QAED,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,IAAI,GAAG,IAAI,CAAC,qBAAqB,CAAC;QACpC,CAAC;QAED,MAAM,UAAU,GAAa,EAAE,CAAC;QAChC,IAAI,CAAC,GAAG,CAAC,CAAC;QACV,OAAO,CAAC,GAAG,IAAI,IAAI,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;YAC5D,2DAA2D;YAC3D,MAAM,yBAAyB,GAAG,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC;YAC3E,MAAM,6BAA6B,GACjC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,UAAU,GAAG,IAAI,CAAC,yBAAyB,CAAC;YAC7E,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,6BAA6B,EAAE,yBAAyB,CAAC,CAAC;YACrF,IAAI,SAAS,GAAG,IAAI,GAAG,CAAC,EAAE,CAAC;gBACzB,uBAAuB;gBACvB,MAAM,GAAG,GAAG,IAAI,CAAC,yBAAyB,GAAG,IAAI,GAAG,CAAC,CAAC;gBACtD,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,yBAAyB,EAAE,GAAG,CAAC,CAAC,CAAC;gBAC3F,IAAI,CAAC,iBAAiB,IAAI,IAAI,GAAG,CAAC,CAAC;gBACnC,IAAI,CAAC,yBAAyB,GAAG,GAAG,CAAC;gBACrC,CAAC,GAAG,IAAI,CAAC;gBACT,MAAM;YACR,CAAC;iBAAM,CAAC;gBACN,wBAAwB;gBACxB,MAAM,GAAG,GAAG,IAAI,CAAC,yBAAyB,GAAG,SAAS,CAAC;gBACvD,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,yBAAyB,EAAE,GAAG,CAAC,CAAC,CAAC;gBAC3F,IAAI,SAAS,KAAK,6BAA6B,EAAE,CAAC;oBAChD,4DAA4D;oBAC5D,IAAI,CAAC,yBAAyB,GAAG,CAAC,CAAC;oBACnC,IAAI,CAAC,WAAW,EAAE,CAAC;gBACrB,CAAC;qBAAM,CAAC;oBACN,IAAI,CAAC,yBAAyB,GAAG,GAAG,CAAC;gBACvC,CAAC;gBACD,IAAI,CAAC,iBAAiB,IAAI,SAAS,CAAC;gBACpC,CAAC,IAAI,SAAS,CAAC;YACjB,CAAC;QACH,CAAC;QAED,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC1B,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC;QACvC,CAAC;aAAM,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACnC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3B,CAAC;IACH,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport { Readable, ReadableOptions } from \"stream\";\n\n/**\n * Options to configure the BuffersStream.\n */\nexport interface BuffersStreamOptions extends ReadableOptions {}\n\n/**\n * This class generates a readable stream from the data in an array of buffers.\n */\nexport class BuffersStream extends Readable {\n  /**\n   * The offset of data to be read in the current buffer.\n   */\n  private byteOffsetInCurrentBuffer: number;\n\n  /**\n   * The index of buffer to be read in the array of buffers.\n   */\n  private bufferIndex: number;\n\n  /**\n   * The total length of data already read.\n   */\n  private pushedBytesLength: number;\n\n  /**\n   * Creates an instance of BuffersStream that will emit the data\n   * contained in the array of buffers.\n   *\n   * @param buffers - Array of buffers containing the data\n   * @param byteLength - The total length of data contained in the buffers\n   */\n  constructor(\n    private buffers: Buffer[],\n    private byteLength: number,\n    options?: BuffersStreamOptions\n  ) {\n    super(options);\n    this.byteOffsetInCurrentBuffer = 0;\n    this.bufferIndex = 0;\n    this.pushedBytesLength = 0;\n\n    // check byteLength is no larger than buffers[] total length\n    let buffersLength = 0;\n    for (const buf of this.buffers) {\n      buffersLength += buf.byteLength;\n    }\n    if (buffersLength < this.byteLength) {\n      throw new Error(\"Data size shouldn't be larger than the total length of buffers.\");\n    }\n  }\n\n  /**\n   * Internal _read() that will be called when the stream wants to pull more data in.\n   *\n   * @param size - Optional. The size of data to be read\n   */\n  public _read(size?: number) {\n    if (this.pushedBytesLength >= this.byteLength) {\n      this.push(null);\n    }\n\n    if (!size) {\n      size = this.readableHighWaterMark;\n    }\n\n    const outBuffers: Buffer[] = [];\n    let i = 0;\n    while (i < size && this.pushedBytesLength < this.byteLength) {\n      // The last buffer may be longer than the data it contains.\n      const remainingDataInAllBuffers = this.byteLength - this.pushedBytesLength;\n      const remainingCapacityInThisBuffer =\n        this.buffers[this.bufferIndex].byteLength - this.byteOffsetInCurrentBuffer;\n      const remaining = Math.min(remainingCapacityInThisBuffer, remainingDataInAllBuffers);\n      if (remaining > size - i) {\n        // chunkSize = size - i\n        const end = this.byteOffsetInCurrentBuffer + size - i;\n        outBuffers.push(this.buffers[this.bufferIndex].slice(this.byteOffsetInCurrentBuffer, end));\n        this.pushedBytesLength += size - i;\n        this.byteOffsetInCurrentBuffer = end;\n        i = size;\n        break;\n      } else {\n        // chunkSize = remaining\n        const end = this.byteOffsetInCurrentBuffer + remaining;\n        outBuffers.push(this.buffers[this.bufferIndex].slice(this.byteOffsetInCurrentBuffer, end));\n        if (remaining === remainingCapacityInThisBuffer) {\n          // this.buffers[this.bufferIndex] used up, shift to next one\n          this.byteOffsetInCurrentBuffer = 0;\n          this.bufferIndex++;\n        } else {\n          this.byteOffsetInCurrentBuffer = end;\n        }\n        this.pushedBytesLength += remaining;\n        i += remaining;\n      }\n    }\n\n    if (outBuffers.length > 1) {\n      this.push(Buffer.concat(outBuffers));\n    } else if (outBuffers.length === 1) {\n      this.push(outBuffers[0]);\n    }\n  }\n}\n"]}
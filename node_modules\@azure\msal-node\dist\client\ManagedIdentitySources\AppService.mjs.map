{"version": 3, "file": "AppService.mjs", "sources": ["../../../src/client/ManagedIdentitySources/AppService.ts"], "sourcesContent": [null], "names": [], "mappings": ";;;;;;AAAA;;;AAGG;AAkBH;AACA,MAAM,2BAA2B,GAAW,YAAY,CAAC;AAEzD;;AAEG;AACG,MAAO,UAAW,SAAQ,yBAAyB,CAAA;IAIrD,WACI,CAAA,MAAc,EACd,WAAwB,EACxB,aAA6B,EAC7B,cAA8B,EAC9B,gBAAwB,EACxB,cAAsB,EAAA;QAEtB,KAAK,CAAC,MAAM,EAAE,WAAW,EAAE,aAAa,EAAE,cAAc,CAAC,CAAC;AAE1D,QAAA,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;AACzC,QAAA,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;KACxC;AAEM,IAAA,OAAO,uBAAuB,GAAA;QACjC,MAAM,gBAAgB,GAClB,OAAO,CAAC,GAAG,CACP,uCAAuC,CAAC,iBAAiB,CAC5D,CAAC;QACN,MAAM,cAAc,GAChB,OAAO,CAAC,GAAG,CACP,uCAAuC,CAAC,eAAe,CAC1D,CAAC;AAEN,QAAA,OAAO,CAAC,gBAAgB,EAAE,cAAc,CAAC,CAAC;KAC7C;IAEM,OAAO,SAAS,CACnB,MAAc,EACd,WAAwB,EACxB,aAA6B,EAC7B,cAA8B,EAAA;QAE9B,MAAM,CAAC,gBAAgB,EAAE,cAAc,CAAC,GACpC,UAAU,CAAC,uBAAuB,EAAE,CAAC;;AAGzC,QAAA,IAAI,CAAC,gBAAgB,IAAI,CAAC,cAAc,EAAE;AACtC,YAAA,MAAM,CAAC,IAAI,CACP,CAAsB,mBAAA,EAAA,0BAA0B,CAAC,WAAW,CAAA,6DAAA,EAAgE,uCAAuC,CAAC,eAAe,CAAU,OAAA,EAAA,uCAAuC,CAAC,iBAAiB,CAAA,wCAAA,CAA0C,CACnS,CAAC;AACF,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;AAED,QAAA,MAAM,yBAAyB,GAC3B,UAAU,CAAC,gCAAgC,CACvC,uCAAuC,CAAC,iBAAiB,EACzD,gBAAgB,EAChB,0BAA0B,CAAC,WAAW,EACtC,MAAM,CACT,CAAC;AAEN,QAAA,MAAM,CAAC,IAAI,CACP,CAAA,+DAAA,EAAkE,0BAA0B,CAAC,WAAW,CAAoC,iCAAA,EAAA,yBAAyB,cAAc,0BAA0B,CAAC,WAAW,CAAA,kBAAA,CAAoB,CAChP,CAAC;AAEF,QAAA,OAAO,IAAI,UAAU,CACjB,MAAM,EACN,WAAW,EACX,aAAa,EACb,cAAc,EACd,gBAAgB,EAChB,cAAc,CACjB,CAAC;KACL;IAEM,aAAa,CAChB,QAAgB,EAChB,iBAAoC,EAAA;AAEpC,QAAA,MAAM,OAAO,GACT,IAAI,gCAAgC,CAChC,UAAU,CAAC,GAAG,EACd,IAAI,CAAC,gBAAgB,CACxB,CAAC;QAEN,OAAO,CAAC,OAAO,CAAC,8BAA8B,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC;AAEtE,QAAA,OAAO,CAAC,eAAe,CAAC,gCAAgC,CAAC;AACrD,YAAA,2BAA2B,CAAC;AAChC,QAAA,OAAO,CAAC,eAAe,CAAC,qCAAqC,CAAC;AAC1D,YAAA,QAAQ,CAAC;AAEb,QAAA,IACI,iBAAiB,CAAC,MAAM,KAAK,qBAAqB,CAAC,eAAe,EACpE;AACE,YAAA,OAAO,CAAC,eAAe,CACnB,IAAI,CAAC,iDAAiD,CAClD,iBAAiB,CAAC,MAAM,CAC3B,CACJ,GAAG,iBAAiB,CAAC,EAAE,CAAC;AAC5B,SAAA;;AAID,QAAA,OAAO,OAAO,CAAC;KAClB;AACJ;;;;"}
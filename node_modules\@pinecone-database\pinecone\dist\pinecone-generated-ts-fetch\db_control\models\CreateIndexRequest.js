"use strict";
/* tslint:disable */
/* eslint-disable */
/**
 * Pinecone Control Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateIndexRequestToJSON = exports.CreateIndexRequestFromJSONTyped = exports.CreateIndexRequestFromJSON = exports.instanceOfCreateIndexRequest = exports.CreateIndexRequestMetricEnum = void 0;
var runtime_1 = require("../runtime");
var DeletionProtection_1 = require("./DeletionProtection");
var IndexSpec_1 = require("./IndexSpec");
/**
 * @export
 */
exports.CreateIndexRequestMetricEnum = {
    Cosine: 'cosine',
    Euclidean: 'euclidean',
    Dotproduct: 'dotproduct'
};
/**
 * Check if a given object implements the CreateIndexRequest interface.
 */
function instanceOfCreateIndexRequest(value) {
    var isInstance = true;
    isInstance = isInstance && "name" in value;
    isInstance = isInstance && "dimension" in value;
    isInstance = isInstance && "spec" in value;
    return isInstance;
}
exports.instanceOfCreateIndexRequest = instanceOfCreateIndexRequest;
function CreateIndexRequestFromJSON(json) {
    return CreateIndexRequestFromJSONTyped(json, false);
}
exports.CreateIndexRequestFromJSON = CreateIndexRequestFromJSON;
function CreateIndexRequestFromJSONTyped(json, ignoreDiscriminator) {
    if ((json === undefined) || (json === null)) {
        return json;
    }
    return {
        'name': json['name'],
        'dimension': json['dimension'],
        'metric': !(0, runtime_1.exists)(json, 'metric') ? undefined : json['metric'],
        'deletionProtection': !(0, runtime_1.exists)(json, 'deletion_protection') ? undefined : (0, DeletionProtection_1.DeletionProtectionFromJSON)(json['deletion_protection']),
        'tags': !(0, runtime_1.exists)(json, 'tags') ? undefined : json['tags'],
        'spec': (0, IndexSpec_1.IndexSpecFromJSON)(json['spec']),
    };
}
exports.CreateIndexRequestFromJSONTyped = CreateIndexRequestFromJSONTyped;
function CreateIndexRequestToJSON(value) {
    if (value === undefined) {
        return undefined;
    }
    if (value === null) {
        return null;
    }
    return {
        'name': value.name,
        'dimension': value.dimension,
        'metric': value.metric,
        'deletion_protection': (0, DeletionProtection_1.DeletionProtectionToJSON)(value.deletionProtection),
        'tags': value.tags,
        'spec': (0, IndexSpec_1.IndexSpecToJSON)(value.spec),
    };
}
exports.CreateIndexRequestToJSON = CreateIndexRequestToJSON;
//# sourceMappingURL=CreateIndexRequest.js.map
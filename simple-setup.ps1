# Simple AI Agent Setup
Write-Host "Setting up your AI Agent Builder..." -ForegroundColor Cyan

$apiKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI0NDgyZmY2Yi0xNzcwLTRlZGQtOTY2Yy1hNTVjZTNkZDZmZGQiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzQ4NTAyNTMyfQ.Si6LRA-uLi8HIdWZIX6tKNXzrSJDd6gBqiRHwtf8K8M"
$headers = @{
    "Content-Type" = "application/json"
    "X-N8N-API-KEY" = $apiKey
}

# Create simple working AI agent
$agentJson = @'
{
  "name": "Universal AI Agent Builder",
  "nodes": [
    {
      "parameters": {
        "httpMethod": "POST",
        "path": "build-agent",
        "options": {}
      },
      "id": "webhook-start",
      "name": "Webhook Start",
      "type": "n8n-nodes-base.webhook",
      "typeVersion": 2,
      "position": [300, 400]
    },
    {
      "parameters": {
        "respondWith": "json",
        "responseBody": "{\n  \"success\": true,\n  \"message\": \"AI Agent Builder is working!\",\n  \"instruction\": \"{{ $json.body.instruction || 'No instruction provided' }}\",\n  \"webhook\": \"http://localhost:2410/webhook/build-agent\",\n  \"status\": \"Ready to build AI agents!\"\n}"
      },
      "id": "response-node",
      "name": "Response",
      "type": "n8n-nodes-base.respondToWebhook",
      "typeVersion": 1,
      "position": [520, 400]
    }
  ],
  "connections": {
    "Webhook Start": {
      "main": [
        [
          {
            "node": "Response",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  },
  "active": false,
  "settings": {
    "timezone": "UTC"
  },
  "tags": ["ai-agent"]
}
'@

try {
    Write-Host "Creating AI Agent Builder..." -ForegroundColor Yellow
    $result = Invoke-RestMethod -Uri "http://localhost:2410/api/v1/workflows" -Method POST -Headers $headers -Body $agentJson
    Write-Host "SUCCESS! AI Agent Builder created with ID: $($result.id)" -ForegroundColor Green
    
    # Activate it
    Write-Host "Activating the workflow..." -ForegroundColor Yellow
    Invoke-RestMethod -Uri "http://localhost:2410/api/v1/workflows/$($result.id)/activate" -Method POST -Headers $headers
    Write-Host "SUCCESS! AI Agent Builder is now ACTIVE!" -ForegroundColor Green
    
    Write-Host ""
    Write-Host "=== YOUR AI AGENT BUILDER IS READY! ===" -ForegroundColor Green
    Write-Host "Webhook URL: http://localhost:2410/webhook/build-agent" -ForegroundColor Cyan
    Write-Host "n8n Interface: http://localhost:2410" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Test it with:" -ForegroundColor Yellow
    Write-Host 'curl -X POST http://localhost:2410/webhook/build-agent -H "Content-Type: application/json" -d "{\"instruction\": \"Create a chatbot\"}"' -ForegroundColor Gray
    
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

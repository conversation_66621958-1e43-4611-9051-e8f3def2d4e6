/**
 * Pinecone Control Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
/**
 * The configuration needed to create a Pinecone collection.
 * @export
 * @interface CreateCollectionRequest
 */
export interface CreateCollectionRequest {
    /**
     * The name of the collection to be created. Resource name must be 1-45 characters long, start and end with an alphanumeric character, and consist only of lower case alphanumeric characters or '-'.
     * @type {string}
     * @memberof CreateCollectionRequest
     */
    name: string;
    /**
     * The name of the index to be used as the source for the collection.
     * @type {string}
     * @memberof CreateCollectionRequest
     */
    source: string;
}
/**
 * Check if a given object implements the CreateCollectionRequest interface.
 */
export declare function instanceOfCreateCollectionRequest(value: object): boolean;
export declare function CreateCollectionRequestFromJSON(json: any): CreateCollectionRequest;
export declare function CreateCollectionRequestFromJSONTyped(json: any, ignoreDiscriminator: boolean): CreateCollectionRequest;
export declare function CreateCollectionRequestToJSON(value?: CreateCollectionRequest | null): any;

/**
 * Pinecone Data Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import type { Vector } from './Vector';
/**
 * The request for the `upsert` operation.
 * @export
 * @interface UpsertRequest
 */
export interface UpsertRequest {
    /**
     * An array containing the vectors to upsert. Recommended batch limit is 100 vectors.
     * @type {Array<Vector>}
     * @memberof UpsertRequest
     */
    vectors: Array<Vector>;
    /**
     * The namespace where you upsert vectors.
     * @type {string}
     * @memberof UpsertRequest
     */
    namespace?: string;
}
/**
 * Check if a given object implements the UpsertRequest interface.
 */
export declare function instanceOfUpsertRequest(value: object): boolean;
export declare function UpsertRequestFromJSON(json: any): UpsertRequest;
export declare function UpsertRequestFromJSONTyped(json: any, ignoreDiscriminator: boolean): UpsertRequest;
export declare function UpsertRequestToJSON(value?: UpsertRequest | null): any;

{"name": "@azure/storage-blob", "sdk-type": "client", "version": "12.26.0", "description": "Microsoft Azure Storage SDK for JavaScript - Blob", "main": "./dist/index.js", "module": "./dist-esm/storage-blob/src/index.js", "browser": {"./dist-esm/storage-blob/src/index.js": "./dist-esm/storage-blob/src/index.browser.js", "./dist-esm/storage-blob/src/credentials/StorageSharedKeyCredential.js": "./dist-esm/storage-blob/src/credentials/StorageSharedKeyCredential.browser.js", "./dist-esm/storage-blob/src/credentials/UserDelegationKeyCredential.js": "./dist-esm/storage-blob/src/credentials/UserDelegationKeyCredential.browser.js", "./dist-esm/storage-blob/src/policies/StorageSharedKeyCredentialPolicyV2.js": "./dist-esm/storage-blob/src/policies/StorageSharedKeyCredentialPolicyV2.browser.js", "./dist-esm/storage-blob/src/policies/StorageCorrectContentLengthPolicy.js": "./dist-esm/storage-blob/src/policies/StorageCorrectContentLengthPolicy.browser.js", "./dist-esm/storage-blob/src/utils/utils.node.js": "./dist-esm/storage-blob/src/utils/utils.browser.js", "./dist-esm/storage-blob/test/utils/index.js": "./dist-esm/storage-blob/test/utils/index.browser.js", "./dist-esm/storage-blob/src/BatchUtils.js": "./dist-esm/storage-blob/src/BatchUtils.browser.js", "./dist-esm/storage-blob/src/BlobDownloadResponse.js": "./dist-esm/storage-blob/src/BlobDownloadResponse.browser.js", "./dist-esm/storage-blob/src/BlobQueryResponse.js": "./dist-esm/storage-blob/src/BlobQueryResponse.browser.js", "./dist-esm/storage-common/src/BufferScheduler.js": "./dist-esm/storage-common/src/BufferScheduler.browser.js", "./dist-esm/storage-common/src/index.js": "./dist-esm/storage-common/src/index.browser.js", "fs": false, "os": false, "process": false}, "react-native": {"./dist/index.js": "./dist-esm/storage-blob/src/index.js"}, "types": "./types/latest/storage-blob.d.ts", "engines": {"node": ">=18.0.0"}, "scripts": {"build": "npm run clean && tsc -p . && dev-tool run bundle && dev-tool run extract-api", "build:browser": "tsc -p . && dev-tool run bundle", "build:node": "tsc -p . && dev-tool run bundle", "build:samples": "echo Obsolete;", "build:test": "tsc -p . && dev-tool run bundle", "check-format": "dev-tool run vendored prettier --list-different --config ../../../.prettierrc.json --ignore-path ../../../.prettierignore \"src/**/*.ts\" \"test/**/*.ts\" \"samples-dev/**/*.ts\" \"*.{js,json}\"", "clean": "dev-tool run vendored rimraf --glob dist dist-* types temp statistics.html coverage coverage-browser .nyc_output *.tgz *.log test*.xml TEST*.xml", "clean:samples": "dev-tool run vendored rimraf samples/v12/javascript/node_modules samples/v12/typescript/node_modules samples/v12/typescript/dist samples/v12/typescript/package-lock.json samples/v12/javascript/package-lock.json", "emulator-tests": "dev-tool run vendored cross-env STORAGE_CONNECTION_STRING=UseDevelopmentStorage=true && npm run test:node", "execute:samples": "dev-tool samples run samples-dev", "extract-api": "tsc -p . && dev-tool run extract-api", "format": "dev-tool run vendored prettier --write --config ../../../.prettierrc.json --ignore-path ../../../.prettierignore \"src/**/*.ts\" \"test/**/*.ts\" \"samples-dev/**/*.ts\" \"*.{js,json}\"", "generate:client": "autorest --typescript ./swagger/README.md", "integration-test": "npm run integration-test:node && npm run integration-test:browser", "integration-test:browser": "dev-tool run test:browser", "integration-test:node": "dev-tool run test:node-js-input -- --timeout 5000000 'dist-esm/storage-blob/test/*.spec.js' 'dist-esm/storage-blob/test/node/*.spec.js'", "lint": "eslint package.json api-extractor.json README.md src test", "lint:fix": "eslint package.json api-extractor.json README.md src test --fix", "pack": "npm pack 2>&1", "test": "npm run clean && npm run build:test && npm run unit-test", "test:browser": "npm run clean && npm run build:test && npm run unit-test:browser", "test:node": "npm run clean && npm run build:test && npm run unit-test:node", "unit-test": "npm run unit-test:node && npm run unit-test:browser", "unit-test:browser": "dev-tool run test:browser", "unit-test:node": "dev-tool run test:node-ts-input -- --timeout 1200000 --exclude 'test/**/browser/*.spec.ts' 'test/**/*.spec.ts'", "update-snippets": "echo skipped"}, "files": ["BreakingChanges.md", "dist/", "dist-esm/storage-blob/src/", "dist-esm/storage-internal-avro/src/", "dist-esm/storage-common/src/", "types/latest/storage-blob.d.ts", "README.md", "LICENSE"], "repository": "github:Azure/azure-sdk-for-js", "keywords": ["azure", "storage", "blob", "cloud", "node.js", "typescript", "javascript", "browser"], "author": "Microsoft Corporation", "license": "MIT", "bugs": {"url": "https://github.com/Azure/azure-sdk-for-js/issues"}, "homepage": "https://github.com/Azure/azure-sdk-for-js/blob/main/sdk/storage/storage-blob/", "sideEffects": false, "//metadata": {"constantPaths": [{"path": "src/generated/src/storageClient.ts", "prefix": "packageVersion"}, {"path": "src/utils/constants.ts", "prefix": "SDK_VERSION"}, {"path": "swagger/README.md", "prefix": "package-version"}]}, "//sampleConfiguration": {"skip": ["advancedRequestOptions.js", "anonymousAuth.js", "azureAdAuth.js", "customPipeline.js", "customizedClientHeaders.js", "listBlobsByHierarchy.js", "listBlobs.js", "listContainers.js", "snapshots.js", "sharedKeyAuth.js"], "productName": "Azure Storage Blob", "productSlugs": ["azure", "azure-storage"], "requiredResources": {"Azure Storage Account": "https://docs.microsoft.com/azure/storage/common/storage-account-overview"}}, "dependencies": {"@azure/abort-controller": "^2.1.2", "@azure/core-auth": "^1.4.0", "@azure/core-client": "^1.6.2", "@azure/core-http-compat": "^2.0.0", "@azure/core-lro": "^2.2.0", "@azure/core-paging": "^1.1.1", "@azure/core-rest-pipeline": "^1.10.1", "@azure/core-tracing": "^1.1.2", "@azure/core-util": "^1.6.1", "@azure/core-xml": "^1.4.3", "@azure/logger": "^1.0.0", "events": "^3.0.0", "tslib": "^2.2.0"}, "devDependencies": {"@azure-tools/test-credential": "^1.0.0", "@azure-tools/test-perf": "^1.0.0", "@azure-tools/test-recorder": "^3.0.0", "@azure-tools/test-utils": "^1.0.1", "@azure/dev-tool": "^1.0.0", "@azure/eslint-plugin-azure-sdk": "^3.0.0", "@azure/identity": "^4.0.1", "@types/chai": "^4.1.6", "@types/mocha": "^10.0.0", "@types/node": "^18.0.0", "chai": "^4.2.0", "dotenv": "^16.0.0", "es6-promise": "^4.2.5", "eslint": "^9.9.0", "inherits": "^2.0.3", "karma": "^6.2.0", "karma-chrome-launcher": "^3.0.0", "karma-coverage": "^2.0.0", "karma-env-preprocessor": "^0.1.1", "karma-firefox-launcher": "^1.1.0", "karma-junit-reporter": "^2.0.1", "karma-mocha": "^2.0.1", "karma-mocha-reporter": "^2.2.5", "karma-sourcemap-loader": "^0.3.8", "mocha": "^10.0.0", "nyc": "^17.0.0", "puppeteer": "^23.0.2", "source-map-support": "^0.5.9", "ts-node": "^10.0.0", "typescript": "~5.6.2", "util": "^0.12.1"}}
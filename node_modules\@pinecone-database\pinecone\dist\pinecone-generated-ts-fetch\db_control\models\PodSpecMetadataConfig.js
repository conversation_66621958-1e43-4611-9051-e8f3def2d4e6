"use strict";
/* tslint:disable */
/* eslint-disable */
/**
 * Pinecone Control Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.PodSpecMetadataConfigToJSON = exports.PodSpecMetadataConfigFromJSONTyped = exports.PodSpecMetadataConfigFromJSON = exports.instanceOfPodSpecMetadataConfig = void 0;
var runtime_1 = require("../runtime");
/**
 * Check if a given object implements the PodSpecMetadataConfig interface.
 */
function instanceOfPodSpecMetadataConfig(value) {
    var isInstance = true;
    return isInstance;
}
exports.instanceOfPodSpecMetadataConfig = instanceOfPodSpecMetadataConfig;
function PodSpecMetadataConfigFromJSON(json) {
    return PodSpecMetadataConfigFromJSONTyped(json, false);
}
exports.PodSpecMetadataConfigFromJSON = PodSpecMetadataConfigFromJSON;
function PodSpecMetadataConfigFromJSONTyped(json, ignoreDiscriminator) {
    if ((json === undefined) || (json === null)) {
        return json;
    }
    return {
        'indexed': !(0, runtime_1.exists)(json, 'indexed') ? undefined : json['indexed'],
    };
}
exports.PodSpecMetadataConfigFromJSONTyped = PodSpecMetadataConfigFromJSONTyped;
function PodSpecMetadataConfigToJSON(value) {
    if (value === undefined) {
        return undefined;
    }
    if (value === null) {
        return null;
    }
    return {
        'indexed': value.indexed,
    };
}
exports.PodSpecMetadataConfigToJSON = PodSpecMetadataConfigToJSON;
//# sourceMappingURL=PodSpecMetadataConfig.js.map
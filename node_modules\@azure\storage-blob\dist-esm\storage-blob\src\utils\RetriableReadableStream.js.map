{"version": 3, "file": "RetriableReadableStream.js", "sourceRoot": "", "sources": ["../../../../src/utils/RetriableReadableStream.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,UAAU,EAAE,MAAM,yBAAyB,CAAC;AAErD,OAAO,EAAE,QAAQ,EAAE,MAAM,QAAQ,CAAC;AAgClC;;;;GAIG;AACH,MAAM,OAAO,uBAAwB,SAAQ,QAAQ;IAWnD;;;;;;;;;OASG;IACH,YACE,MAA6B,EAC7B,MAA4B,EAC5B,MAAc,EACd,KAAa,EACb,UAA0C,EAAE;QAE5C,KAAK,CAAC,EAAE,aAAa,EAAE,OAAO,CAAC,aAAa,EAAE,CAAC,CAAC;QAtB1C,YAAO,GAAW,CAAC,CAAC;QAuDpB,sBAAiB,GAAG,CAAC,IAAY,EAAE,EAAE;YAC3C,IAAI,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,CAAC;gBACnC,IAAI,CAAC,OAAO,CAAC,iBAAiB,GAAG,SAAS,CAAC;gBAC3C,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;gBACpB,IAAI,CAAC,uBAAuB,EAAE,CAAC;gBAC9B,IAAI,CAAC,MAAmB,CAAC,OAAO,EAAE,CAAC;gBACpC,OAAO;YACT,CAAC;YAED,eAAe;YACf,2EAA2E;YAC3E,KAAK;YACL,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC;YAC3B,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;gBACpB,IAAI,CAAC,UAAU,CAAC,EAAE,WAAW,EAAE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;YAC7D,CAAC;YACD,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;gBACrB,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;YACtB,CAAC;QACH,CAAC,CAAC;QAEM,yBAAoB,GAAG,GAAG,EAAE;YAClC,MAAM,UAAU,GAAG,IAAI,UAAU,CAAC,4BAA4B,CAAC,CAAC;YAChE,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QAC3B,CAAC,CAAC;QAEM,4BAAuB,GAAG,CAAC,GAAW,EAAE,EAAE;YAChD,IAAI,GAAG,IAAI,GAAG,CAAC,IAAI,KAAK,YAAY,EAAE,CAAC;gBACrC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;gBAClB,OAAO;YACT,CAAC;YAED,eAAe;YACf,kDAAkD;YAClD,kBAAkB;YAClB,+BAA+B;YAC/B,KAAK;YACL,IAAI,CAAC,yBAAyB,EAAE,CAAC;YACjC,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,KAAK,IAAI,CAAC,GAAG,EAAE,CAAC;gBACjC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClB,CAAC;iBAAM,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;gBACnC,eAAe;gBACf,gEAAgE;gBAChE,KAAK;gBACL,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;oBACzC,IAAI,CAAC,OAAO,IAAI,CAAC,CAAC;oBAClB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;yBACrB,IAAI,CAAC,CAAC,SAAS,EAAE,EAAE;wBAClB,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;wBACxB,IAAI,CAAC,sBAAsB,EAAE,CAAC;wBAC9B,OAAO;oBACT,CAAC,CAAC;yBACD,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;wBACf,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;oBACtB,CAAC,CAAC,CAAC;gBACP,CAAC;qBAAM,CAAC;oBACN,IAAI,CAAC,OAAO,CACV,IAAI,KAAK,CACP,sHACE,IAAI,CAAC,MAAM,GAAG,CAChB,yBAAyB,IAAI,CAAC,GAAG,cAAc,IAAI,CAAC,OAAO,kBACzD,IAAI,CAAC,gBACP,EAAE,CACH,CACF,CAAC;gBACJ,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,OAAO,CACV,IAAI,KAAK,CACP,4FACE,IAAI,CAAC,GACP,sBAAsB,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CACxC,CACF,CAAC;YACJ,CAAC;QACH,CAAC,CAAC;QA3GA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC;QACpB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,GAAG,GAAG,MAAM,GAAG,KAAK,GAAG,CAAC,CAAC;QAC9B,IAAI,CAAC,gBAAgB;YACnB,OAAO,CAAC,gBAAgB,IAAI,OAAO,CAAC,gBAAgB,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3F,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;QACrC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QAEvB,IAAI,CAAC,sBAAsB,EAAE,CAAC;IAChC,CAAC;IAEM,KAAK;QACV,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;IACvB,CAAC;IAEO,sBAAsB;QAC5B,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAC/C,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC,uBAAuB,CAAC,CAAC;QACpD,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,uBAAuB,CAAC,CAAC;QACtD,oBAAoB;QACpB,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;IACvD,CAAC;IAEO,yBAAyB;QAC/B,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAC3D,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,CAAC,uBAAuB,CAAC,CAAC;QAChE,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,IAAI,CAAC,uBAAuB,CAAC,CAAC;QAClE,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,SAAS,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;IACnE,CAAC;IA+ED,QAAQ,CAAC,KAAmB,EAAE,QAAiC;QAC7D,iDAAiD;QACjD,IAAI,CAAC,yBAAyB,EAAE,CAAC;QAChC,IAAI,CAAC,MAAmB,CAAC,OAAO,EAAE,CAAC;QAEpC,QAAQ,CAAC,KAAK,KAAK,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;IAC/C,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport { AbortError } from \"@azure/abort-controller\";\nimport type { TransferProgressEvent } from \"@azure/core-rest-pipeline\";\nimport { Readable } from \"stream\";\n\nexport type ReadableStreamGetter = (offset: number) => Promise<NodeJS.ReadableStream>;\n\nexport interface RetriableReadableStreamOptions {\n  /**\n   * Max retry count (greater than or equal to 0), undefined or invalid value means no retry\n   */\n  maxRetryRequests?: number;\n\n  /**\n   * Read progress event handler\n   */\n  onProgress?: (progress: TransferProgressEvent) => void;\n\n  /**\n   * Debug purpose only. Used to inject an unexpected end to existing internal stream,\n   * to test stream retry works well or not.\n   *\n   * When assign it to true, for next incoming \"data\" event of internal stream,\n   * RetriableReadableStream will try to emit an \"end\" event to existing internal\n   * stream to force it end and start retry from the breaking point.\n   * The value will then update to \"undefined\", once the injection works.\n   */\n  doInjectErrorOnce?: boolean;\n\n  /**\n   * A threshold, not a limit. Dictates the amount of data that a stream buffers before it stops asking for more data.\n   */\n  highWaterMark?: number;\n}\n\n/**\n * ONLY AVAILABLE IN NODE.JS RUNTIME.\n *\n * A Node.js ReadableStream will internally retry when internal ReadableStream unexpected ends.\n */\nexport class RetriableReadableStream extends Readable {\n  private start: number;\n  private offset: number;\n  private end: number;\n  private getter: ReadableStreamGetter;\n  private source: NodeJS.ReadableStream;\n  private retries: number = 0;\n  private maxRetryRequests: number;\n  private onProgress?: (progress: TransferProgressEvent) => void;\n  private options: RetriableReadableStreamOptions;\n\n  /**\n   * Creates an instance of RetriableReadableStream.\n   *\n   * @param source - The current ReadableStream returned from getter\n   * @param getter - A method calling downloading request returning\n   *                                      a new ReadableStream from specified offset\n   * @param offset - Offset position in original data source to read\n   * @param count - How much data in original data source to read\n   * @param options -\n   */\n  public constructor(\n    source: NodeJS.ReadableStream,\n    getter: ReadableStreamGetter,\n    offset: number,\n    count: number,\n    options: RetriableReadableStreamOptions = {},\n  ) {\n    super({ highWaterMark: options.highWaterMark });\n    this.getter = getter;\n    this.source = source;\n    this.start = offset;\n    this.offset = offset;\n    this.end = offset + count - 1;\n    this.maxRetryRequests =\n      options.maxRetryRequests && options.maxRetryRequests >= 0 ? options.maxRetryRequests : 0;\n    this.onProgress = options.onProgress;\n    this.options = options;\n\n    this.setSourceEventHandlers();\n  }\n\n  public _read(): void {\n    this.source.resume();\n  }\n\n  private setSourceEventHandlers() {\n    this.source.on(\"data\", this.sourceDataHandler);\n    this.source.on(\"end\", this.sourceErrorOrEndHandler);\n    this.source.on(\"error\", this.sourceErrorOrEndHandler);\n    // needed for Node14\n    this.source.on(\"aborted\", this.sourceAbortedHandler);\n  }\n\n  private removeSourceEventHandlers() {\n    this.source.removeListener(\"data\", this.sourceDataHandler);\n    this.source.removeListener(\"end\", this.sourceErrorOrEndHandler);\n    this.source.removeListener(\"error\", this.sourceErrorOrEndHandler);\n    this.source.removeListener(\"aborted\", this.sourceAbortedHandler);\n  }\n\n  private sourceDataHandler = (data: Buffer) => {\n    if (this.options.doInjectErrorOnce) {\n      this.options.doInjectErrorOnce = undefined;\n      this.source.pause();\n      this.sourceErrorOrEndHandler();\n      (this.source as Readable).destroy();\n      return;\n    }\n\n    // console.log(\n    //   `Offset: ${this.offset}, Received ${data.length} from internal stream`\n    // );\n    this.offset += data.length;\n    if (this.onProgress) {\n      this.onProgress({ loadedBytes: this.offset - this.start });\n    }\n    if (!this.push(data)) {\n      this.source.pause();\n    }\n  };\n\n  private sourceAbortedHandler = () => {\n    const abortError = new AbortError(\"The operation was aborted.\");\n    this.destroy(abortError);\n  };\n\n  private sourceErrorOrEndHandler = (err?: Error) => {\n    if (err && err.name === \"AbortError\") {\n      this.destroy(err);\n      return;\n    }\n\n    // console.log(\n    //   `Source stream emits end or error, offset: ${\n    //     this.offset\n    //   }, dest end : ${this.end}`\n    // );\n    this.removeSourceEventHandlers();\n    if (this.offset - 1 === this.end) {\n      this.push(null);\n    } else if (this.offset <= this.end) {\n      // console.log(\n      //   `retries: ${this.retries}, max retries: ${this.maxRetries}`\n      // );\n      if (this.retries < this.maxRetryRequests) {\n        this.retries += 1;\n        this.getter(this.offset)\n          .then((newSource) => {\n            this.source = newSource;\n            this.setSourceEventHandlers();\n            return;\n          })\n          .catch((error) => {\n            this.destroy(error);\n          });\n      } else {\n        this.destroy(\n          new Error(\n            `Data corruption failure: received less data than required and reached maxRetires limitation. Received data offset: ${\n              this.offset - 1\n            }, data needed offset: ${this.end}, retries: ${this.retries}, max retries: ${\n              this.maxRetryRequests\n            }`,\n          ),\n        );\n      }\n    } else {\n      this.destroy(\n        new Error(\n          `Data corruption failure: Received more data than original request, data needed offset is ${\n            this.end\n          }, received offset: ${this.offset - 1}`,\n        ),\n      );\n    }\n  };\n\n  _destroy(error: Error | null, callback: (error?: Error) => void): void {\n    // remove listener from source and release source\n    this.removeSourceEventHandlers();\n    (this.source as Readable).destroy();\n\n    callback(error === null ? undefined : error);\n  }\n}\n"]}
{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAIH,OAAO,EAIL,cAAc,GACf,MAAM,mBAAmB,CAAC;AAG3B,OAAO,EAAE,WAAW,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AACvD,OAAO,EAAE,oBAAoB,EAAE,kBAAkB,EAAE,MAAM,sBAAsB,CAAC;AAEhF,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AACrC,MAAM,CAAC,MAAM,IAAI,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport { Logger } from './types/Logger';\nexport { LoggerProvider } from './types/LoggerProvider';\nexport {\n  LogAttributes,\n  LogBody,\n  LogRecord,\n  SeverityNumber,\n} from './types/LogRecord';\nexport { LoggerOptions } from './types/LoggerOptions';\nexport { AnyValue, AnyValueMap } from './types/AnyValue';\nexport { NOOP_LOGGER, NoopLogger } from './NoopLogger';\nexport { NOOP_LOGGER_PROVIDER, NoopLoggerProvider } from './NoopLoggerProvider';\n\nimport { LogsAPI } from './api/logs';\nexport const logs = LogsAPI.getInstance();\n"]}
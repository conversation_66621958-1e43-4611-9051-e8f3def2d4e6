"use strict";
/* tslint:disable */
/* eslint-disable */
/**
 * Pinecone Data Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.NamespaceSummaryToJSON = exports.NamespaceSummaryFromJSONTyped = exports.NamespaceSummaryFromJSON = exports.instanceOfNamespaceSummary = void 0;
var runtime_1 = require("../runtime");
/**
 * Check if a given object implements the NamespaceSummary interface.
 */
function instanceOfNamespaceSummary(value) {
    var isInstance = true;
    return isInstance;
}
exports.instanceOfNamespaceSummary = instanceOfNamespaceSummary;
function NamespaceSummaryFromJSON(json) {
    return NamespaceSummaryFromJSONTyped(json, false);
}
exports.NamespaceSummaryFromJSON = NamespaceSummaryFromJSON;
function NamespaceSummaryFromJSONTyped(json, ignoreDiscriminator) {
    if ((json === undefined) || (json === null)) {
        return json;
    }
    return {
        'vectorCount': !(0, runtime_1.exists)(json, 'vectorCount') ? undefined : json['vectorCount'],
    };
}
exports.NamespaceSummaryFromJSONTyped = NamespaceSummaryFromJSONTyped;
function NamespaceSummaryToJSON(value) {
    if (value === undefined) {
        return undefined;
    }
    if (value === null) {
        return null;
    }
    return {
        'vectorCount': value.vectorCount,
    };
}
exports.NamespaceSummaryToJSON = NamespaceSummaryToJSON;
//# sourceMappingURL=NamespaceSummary.js.map
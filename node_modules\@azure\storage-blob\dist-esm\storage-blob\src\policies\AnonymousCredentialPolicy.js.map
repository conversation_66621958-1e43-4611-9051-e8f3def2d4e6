{"version": 3, "file": "AnonymousCredentialPolicy.js", "sourceRoot": "", "sources": ["../../../../src/policies/AnonymousCredentialPolicy.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAMlC,OAAO,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;AAEtD;;;GAGG;AACH,MAAM,OAAO,yBAA0B,SAAQ,gBAAgB;IAC7D;;;;OAIG;IACH,wGAAwG;IACxG,uEAAuE;IACvE,YAAY,UAAyB,EAAE,OAA6B;QAClE,KAAK,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;IAC7B,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type {\n  RequestPolicy,\n  RequestPolicyOptionsLike as RequestPolicyOptions,\n} from \"@azure/core-http-compat\";\nimport { CredentialPolicy } from \"./CredentialPolicy\";\n\n/**\n * AnonymousCredentialPolicy is used with HTTP(S) requests that read public resources\n * or for use with Shared Access Signatures (SAS).\n */\nexport class AnonymousCredentialPolicy extends CredentialPolicy {\n  /**\n   * Creates an instance of AnonymousCredentialPolicy.\n   * @param nextPolicy -\n   * @param options -\n   */\n  // The base class has a protected constructor. Adding a public one to enable constructing of this class.\n  /* eslint-disable-next-line @typescript-eslint/no-useless-constructor*/\n  constructor(nextPolicy: RequestPolicy, options: RequestPolicyOptions) {\n    super(nextPolicy, options);\n  }\n}\n"]}
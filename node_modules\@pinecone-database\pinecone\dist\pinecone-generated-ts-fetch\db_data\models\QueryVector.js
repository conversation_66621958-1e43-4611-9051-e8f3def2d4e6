"use strict";
/* tslint:disable */
/* eslint-disable */
/**
 * Pinecone Data Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.QueryVectorToJSON = exports.QueryVectorFromJSONTyped = exports.QueryVectorFromJSON = exports.instanceOfQueryVector = void 0;
var runtime_1 = require("../runtime");
var SparseValues_1 = require("./SparseValues");
/**
 * Check if a given object implements the QueryVector interface.
 */
function instanceOfQueryVector(value) {
    var isInstance = true;
    isInstance = isInstance && "values" in value;
    return isInstance;
}
exports.instanceOfQueryVector = instanceOfQueryVector;
function QueryVectorFromJSON(json) {
    return QueryVectorFromJSONTyped(json, false);
}
exports.QueryVectorFromJSON = QueryVectorFromJSON;
function QueryVectorFromJSONTyped(json, ignoreDiscriminator) {
    if ((json === undefined) || (json === null)) {
        return json;
    }
    return {
        'values': json['values'],
        'sparseValues': !(0, runtime_1.exists)(json, 'sparseValues') ? undefined : (0, SparseValues_1.SparseValuesFromJSON)(json['sparseValues']),
        'topK': !(0, runtime_1.exists)(json, 'topK') ? undefined : json['topK'],
        'namespace': !(0, runtime_1.exists)(json, 'namespace') ? undefined : json['namespace'],
        'filter': !(0, runtime_1.exists)(json, 'filter') ? undefined : json['filter'],
    };
}
exports.QueryVectorFromJSONTyped = QueryVectorFromJSONTyped;
function QueryVectorToJSON(value) {
    if (value === undefined) {
        return undefined;
    }
    if (value === null) {
        return null;
    }
    return {
        'values': value.values,
        'sparseValues': (0, SparseValues_1.SparseValuesToJSON)(value.sparseValues),
        'topK': value.topK,
        'namespace': value.namespace,
        'filter': value.filter,
    };
}
exports.QueryVectorToJSON = QueryVectorToJSON;
//# sourceMappingURL=QueryVector.js.map
{"version": 3, "file": "SasIPRange.js", "sourceRoot": "", "sources": ["../../../../src/sas/SasIPRange.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAkBlC;;;;;;GAMG;AACH,MAAM,UAAU,eAAe,CAAC,OAAmB;IACjD,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,KAAK,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC;AACzE,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\n/**\n * Allowed IP range for a SAS.\n */\nexport interface SasIPRange {\n  /**\n   * Starting IP address in the IP range.\n   * If end IP doesn't provide, start IP will the only IP allowed.\n   */\n  start: string;\n  /**\n   * Optional. IP address that ends the IP range.\n   * If not provided, start IP will the only IP allowed.\n   */\n  end?: string;\n}\n\n/**\n * Generate SasIPRange format string. For example:\n *\n * \"*******\" or \"*******-***************\"\n *\n * @param ipRange -\n */\nexport function ipRangeToString(ipRange: SasIPRange): string {\n  return ipRange.end ? `${ipRange.start}-${ipRange.end}` : ipRange.start;\n}\n"]}
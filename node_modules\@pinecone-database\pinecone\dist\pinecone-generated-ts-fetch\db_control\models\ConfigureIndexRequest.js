"use strict";
/* tslint:disable */
/* eslint-disable */
/**
 * Pinecone Control Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConfigureIndexRequestToJSON = exports.ConfigureIndexRequestFromJSONTyped = exports.ConfigureIndexRequestFromJSON = exports.instanceOfConfigureIndexRequest = void 0;
var runtime_1 = require("../runtime");
var ConfigureIndexRequestSpec_1 = require("./ConfigureIndexRequestSpec");
var DeletionProtection_1 = require("./DeletionProtection");
/**
 * Check if a given object implements the ConfigureIndexRequest interface.
 */
function instanceOfConfigureIndexRequest(value) {
    var isInstance = true;
    return isInstance;
}
exports.instanceOfConfigureIndexRequest = instanceOfConfigureIndexRequest;
function ConfigureIndexRequestFromJSON(json) {
    return ConfigureIndexRequestFromJSONTyped(json, false);
}
exports.ConfigureIndexRequestFromJSON = ConfigureIndexRequestFromJSON;
function ConfigureIndexRequestFromJSONTyped(json, ignoreDiscriminator) {
    if ((json === undefined) || (json === null)) {
        return json;
    }
    return {
        'spec': !(0, runtime_1.exists)(json, 'spec') ? undefined : (0, ConfigureIndexRequestSpec_1.ConfigureIndexRequestSpecFromJSON)(json['spec']),
        'deletionProtection': !(0, runtime_1.exists)(json, 'deletion_protection') ? undefined : (0, DeletionProtection_1.DeletionProtectionFromJSON)(json['deletion_protection']),
        'tags': !(0, runtime_1.exists)(json, 'tags') ? undefined : json['tags'],
    };
}
exports.ConfigureIndexRequestFromJSONTyped = ConfigureIndexRequestFromJSONTyped;
function ConfigureIndexRequestToJSON(value) {
    if (value === undefined) {
        return undefined;
    }
    if (value === null) {
        return null;
    }
    return {
        'spec': (0, ConfigureIndexRequestSpec_1.ConfigureIndexRequestSpecToJSON)(value.spec),
        'deletion_protection': (0, DeletionProtection_1.DeletionProtectionToJSON)(value.deletionProtection),
        'tags': value.tags,
    };
}
exports.ConfigureIndexRequestToJSON = ConfigureIndexRequestToJSON;
//# sourceMappingURL=ConfigureIndexRequest.js.map
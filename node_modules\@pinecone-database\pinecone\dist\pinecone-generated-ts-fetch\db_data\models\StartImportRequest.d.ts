/**
 * Pinecone Data Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import type { ImportErrorMode } from './ImportErrorMode';
/**
 * The request for the `start_import` operation.
 * @export
 * @interface StartImportRequest
 */
export interface StartImportRequest {
    /**
     * The id of the storage integration that should be used to access the data.
     * @type {string}
     * @memberof StartImportRequest
     */
    integrationId?: string;
    /**
     * The URI prefix under which the data to import is available. All data within this prefix will be listed then imported into the target index. Currently only `s3://` URIs are supported.
     * @type {string}
     * @memberof StartImportRequest
     */
    uri: string;
    /**
     *
     * @type {ImportErrorMode}
     * @memberof StartImportRequest
     */
    errorMode?: ImportErrorMode;
}
/**
 * Check if a given object implements the StartImportRequest interface.
 */
export declare function instanceOfStartImportRequest(value: object): boolean;
export declare function StartImportRequestFromJSON(json: any): StartImportRequest;
export declare function StartImportRequestFromJSONTyped(json: any, ignoreDiscriminator: boolean): StartImportRequest;
export declare function StartImportRequestToJSON(value?: StartImportRequest | null): any;

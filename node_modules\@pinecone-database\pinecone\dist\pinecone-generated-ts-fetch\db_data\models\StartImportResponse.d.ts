/**
 * Pinecone Data Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
/**
 * The response for the `start_import` operation.
 * @export
 * @interface StartImportResponse
 */
export interface StartImportResponse {
    /**
     * Unique identifier for the import operations.
     * @type {string}
     * @memberof StartImportResponse
     */
    id?: string;
}
/**
 * Check if a given object implements the StartImportResponse interface.
 */
export declare function instanceOfStartImportResponse(value: object): boolean;
export declare function StartImportResponseFromJSON(json: any): StartImportResponse;
export declare function StartImportResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): StartImportResponse;
export declare function StartImportResponseToJSON(value?: StartImportResponse | null): any;

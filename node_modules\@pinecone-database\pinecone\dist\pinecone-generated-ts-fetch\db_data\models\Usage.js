"use strict";
/* tslint:disable */
/* eslint-disable */
/**
 * Pinecone Data Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsageToJSON = exports.UsageFromJSONTyped = exports.UsageFromJSON = exports.instanceOfUsage = void 0;
var runtime_1 = require("../runtime");
/**
 * Check if a given object implements the Usage interface.
 */
function instanceOfUsage(value) {
    var isInstance = true;
    return isInstance;
}
exports.instanceOfUsage = instanceOfUsage;
function UsageFromJSON(json) {
    return UsageFromJSONTyped(json, false);
}
exports.UsageFromJSON = UsageFromJSON;
function UsageFromJSONTyped(json, ignoreDiscriminator) {
    if ((json === undefined) || (json === null)) {
        return json;
    }
    return {
        'readUnits': !(0, runtime_1.exists)(json, 'readUnits') ? undefined : json['readUnits'],
    };
}
exports.UsageFromJSONTyped = UsageFromJSONTyped;
function UsageToJSON(value) {
    if (value === undefined) {
        return undefined;
    }
    if (value === null) {
        return null;
    }
    return {
        'readUnits': value.readUnits,
    };
}
exports.UsageToJSON = UsageToJSON;
//# sourceMappingURL=Usage.js.map
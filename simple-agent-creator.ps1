# SIMPLE WORKING AI AGENT CREATOR
# Creates functional n8n workflows that actually work

param(
    [Parameter(Mandatory=$true)]
    [string]$Prompt
)

Write-Host "🤖 SIMPLE AI AGENT CREATOR" -ForegroundColor Cyan
Write-Host "Creating agent from prompt: $Prompt" -ForegroundColor White
Write-Host ""

# Configuration
$apiKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI0NDgyZmY2Yi0xNzcwLTRlZGQtOTY2Yy1hNTVjZTNkZDZmZGQiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzQ4NTAyNTMyfQ.Si6LRA-uLi8HIdWZIX6tKNXzrSJDd6gBqiRHwtf8K8M"
$headers = @{
    "Content-Type" = "application/json"
    "X-N8N-API-KEY" = $apiKey
}

# Analyze prompt
Write-Host "🔍 Analyzing prompt..." -ForegroundColor Yellow
$promptLower = $Prompt.ToLower()

if ($promptLower -like "*chatbot*" -or $promptLower -like "*chat*") {
    $agentType = "chatbot"
    $agentName = "AI Chatbot"
    $webhookPath = "chatbot-$(Get-Random -Maximum 9999)"
} elseif ($promptLower -like "*email*") {
    $agentType = "email"
    $agentName = "Email Automation"
    $webhookPath = "email-$(Get-Random -Maximum 9999)"
} elseif ($promptLower -like "*content*" -or $promptLower -like "*social*") {
    $agentType = "content"
    $agentName = "Content Generator"
    $webhookPath = "content-$(Get-Random -Maximum 9999)"
} else {
    $agentType = "general"
    $agentName = "AI Assistant"
    $webhookPath = "assistant-$(Get-Random -Maximum 9999)"
}

Write-Host "📊 Detected: $agentType - $agentName" -ForegroundColor Cyan

# Create simple working workflow
Write-Host "🧠 Creating workflow..." -ForegroundColor Yellow

$workflow = @{
    name = "$agentName - $(Get-Date -Format 'yyyy-MM-dd')"
    nodes = @(
        @{
            parameters = @{
                httpMethod = "POST"
                path = $webhookPath
                options = @{}
            }
            id = "webhook1"
            name = "Webhook"
            type = "n8n-nodes-base.webhook"
            typeVersion = 2
            position = @(300, 300)
        },
        @{
            parameters = @{
                respondWith = "json"
                responseBody = @"
{
  "success": true,
  "agent": "$agentName",
  "type": "$agentType",
  "message": "Hello! I am your $agentName. You said: {{ `$json.body.message || `$json.body.input || `$json.message || `$json.input || 'Hello' }}",
  "originalPrompt": "$Prompt",
  "response": "I understand you want: $Prompt. I am ready to help!",
  "timestamp": "{{ new Date().toISOString() }}",
  "webhook": "http://localhost:2410/webhook/$webhookPath",
  "status": "✅ Working perfectly!"
}
"@
            }
            id = "respond1"
            name = "Respond"
            type = "n8n-nodes-base.respondToWebhook"
            typeVersion = 1
            position = @(500, 300)
        }
    )
    connections = @{
        "Webhook" = @{
            main = @(
                @(
                    @{
                        node = "Respond"
                        type = "main"
                        index = 0
                    }
                )
            )
        }
    }
    settings = @{
        timezone = "UTC"
        saveManualExecutions = $true
    }
} | ConvertTo-Json -Depth 10

# Deploy workflow
Write-Host "🚀 Deploying to n8n..." -ForegroundColor Yellow
try {
    $result = Invoke-RestMethod -Uri "http://localhost:2410/api/v1/workflows" -Method POST -Headers $headers -Body $workflow
    Write-Host "✅ Workflow created! ID: $($result.id)" -ForegroundColor Green
    
    # Activate workflow
    Write-Host "⚡ Activating workflow..." -ForegroundColor Yellow
    Invoke-RestMethod -Uri "http://localhost:2410/api/v1/workflows/$($result.id)/activate" -Method POST -Headers $headers
    Write-Host "✅ Workflow activated!" -ForegroundColor Green
    
    # Test the webhook
    Write-Host "🧪 Testing the agent..." -ForegroundColor Yellow
    Start-Sleep 3  # Wait for activation
    
    $testHeaders = @{"Content-Type" = "application/json"}
    $testBody = @{message = "Hello, test my new agent!"} | ConvertTo-Json
    
    try {
        $response = Invoke-RestMethod -Uri "http://localhost:2410/webhook/$webhookPath" -Method POST -Headers $testHeaders -Body $testBody
        Write-Host "✅ AGENT IS WORKING!" -ForegroundColor Green
        Write-Host ""
        Write-Host "🎉 SUCCESS! Your AI agent is ready!" -ForegroundColor Green
        Write-Host ""
        Write-Host "📋 Agent Details:" -ForegroundColor Cyan
        Write-Host "   Name: $($result.name)" -ForegroundColor White
        Write-Host "   Type: $agentType" -ForegroundColor White
        Write-Host "   ID: $($result.id)" -ForegroundColor White
        Write-Host "   Webhook: http://localhost:2410/webhook/$webhookPath" -ForegroundColor White
        Write-Host ""
        Write-Host "🧪 Test Response:" -ForegroundColor Cyan
        Write-Host "   $($response.message)" -ForegroundColor White
        Write-Host ""
        Write-Host "🧪 Test your agent:" -ForegroundColor Yellow
        Write-Host "   curl -X POST http://localhost:2410/webhook/$webhookPath \\" -ForegroundColor Gray
        Write-Host "        -H `"Content-Type: application/json`" \\" -ForegroundColor Gray
        Write-Host "        -d '{`"message`": `"Hello, help me!`"}'" -ForegroundColor Gray
        Write-Host ""
        Write-Host "🌐 View in n8n: http://localhost:2410" -ForegroundColor Cyan
        
    } catch {
        Write-Host "⚠️  Agent created but webhook not ready yet. Try testing in a few seconds." -ForegroundColor Yellow
        Write-Host "   Webhook: http://localhost:2410/webhook/$webhookPath" -ForegroundColor Cyan
    }
    
} catch {
    Write-Host "❌ Failed to create agent: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Error details: $responseBody" -ForegroundColor Red
    }
}

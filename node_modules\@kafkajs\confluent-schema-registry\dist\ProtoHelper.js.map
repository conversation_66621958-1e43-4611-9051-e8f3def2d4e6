{"version": 3, "file": "ProtoHelper.js", "sourceRoot": "", "sources": ["../src/ProtoHelper.ts"], "names": [], "mappings": ";;AAAA,qCAQiB;AACjB,qCAAuD;AAEvD,MAAqB,WAAW;IACvB,QAAQ,CAAC,OAAe;QAC7B,OAAM;IACR,CAAC;IAEM,UAAU,CACf,gBAAsC,EACtC,OAAe,EACf,UAAkB;QAElB,MAAM,IAAI,qCAA4B,CAAC,qBAAqB,CAAC,CAAA;IAC/D,CAAC;IAEM,iBAAiB,CAAC,IAAoB;QAC3C,OAAO,EAAE,IAAI,EAAE,mBAAU,CAAC,QAAQ,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,UAAU,EAAE,IAAI,CAAC,UAAU,EAAE,CAAA;IACxF,CAAC;IAED,iCAAiC,CAC/B,iBAAyC,EACzC,UAA2B,EAAE;QAE7B,OAAO;YACL,GAAG,OAAO;YACV,CAAC,mBAAU,CAAC,QAAQ,CAAC,EAAE,EAAE,GAAG,OAAO,CAAC,mBAAU,CAAC,QAAQ,CAAC,EAAE,iBAAiB,EAAE;SAC9E,CAAA;IACH,CAAC;CACF;AA1BD,8BA0BC"}
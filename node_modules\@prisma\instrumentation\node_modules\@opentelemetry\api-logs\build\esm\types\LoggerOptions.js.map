{"version": 3, "file": "LoggerOptions.js", "sourceRoot": "", "sources": ["../../../src/types/LoggerOptions.ts"], "names": [], "mappings": "", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { Attributes } from '@opentelemetry/api';\nexport interface LoggerOptions {\n  /**\n   * The schemaUrl of the tracer or instrumentation library\n   * @default ''\n   */\n  schemaUrl?: string;\n\n  /**\n   * The instrumentation scope attributes to associate with emitted telemetry\n   */\n  scopeAttributes?: Attributes;\n\n  /**\n   * Specifies whether the Trace Context should automatically be passed on to the LogRecords emitted by the Logger.\n   * @default true\n   */\n  includeTraceContext?: boolean;\n}\n"]}
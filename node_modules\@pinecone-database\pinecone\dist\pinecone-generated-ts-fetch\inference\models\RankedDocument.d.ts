/**
 * Pinecone Inference API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
/**
 * A ranked document with a relevance score and an index position.
 * @export
 * @interface RankedDocument
 */
export interface RankedDocument {
    /**
     * The index of the document
     * @type {number}
     * @memberof RankedDocument
     */
    index: number;
    /**
     * The relevance score of the document normalized between 0 and 1.
     * @type {number}
     * @memberof RankedDocument
     */
    score: number;
    /**
     * Document for reranking
     * @type {{ [key: string]: string; }}
     * @memberof RankedDocument
     */
    document?: {
        [key: string]: string;
    };
}
/**
 * Check if a given object implements the RankedDocument interface.
 */
export declare function instanceOfRankedDocument(value: object): boolean;
export declare function RankedDocumentFromJSON(json: any): RankedDocument;
export declare function RankedDocumentFromJSONTyped(json: any, ignoreDiscriminator: boolean): RankedDocument;
export declare function RankedDocumentToJSON(value?: RankedDocument | null): any;

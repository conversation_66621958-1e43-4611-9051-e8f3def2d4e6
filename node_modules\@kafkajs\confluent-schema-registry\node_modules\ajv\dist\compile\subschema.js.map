{"version": 3, "file": "subschema.js", "sourceRoot": "", "sources": ["../../lib/compile/subschema.ts"], "names": [], "mappings": ";;;AAEA,yCAAwC;AACxC,iCAAwD;AACxD,uCAAyD;AAyBzD,IAAY,IAGX;AAHD,WAAY,IAAI;IACd,6BAAG,CAAA;IACH,6BAAG,CAAA;AACL,CAAC,EAHW,IAAI,GAAJ,YAAI,KAAJ,YAAI,QAGf;AAsBD,SAAgB,cAAc,CAAC,EAAgB,EAAE,IAAmB,EAAE,KAAW;IAC/E,MAAM,SAAS,GAAG,YAAY,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;IACxC,mBAAmB,CAAC,SAAS,EAAE,EAAE,EAAE,IAAI,CAAC,CAAA;IACxC,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC,CAAA;IACpC,MAAM,WAAW,GAAG,EAAC,GAAG,EAAE,EAAE,GAAG,SAAS,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,SAAS,EAAC,CAAA;IAC7E,wBAAa,CAAC,WAAW,EAAE,KAAK,CAAC,CAAA;IACjC,OAAO,WAAW,CAAA;AACpB,CAAC;AAPD,wCAOC;AAED,SAAS,YAAY,CACnB,EAAgB,EAChB,EAAC,OAAO,EAAE,UAAU,EAAE,MAAM,EAAE,UAAU,EAAE,aAAa,EAAE,YAAY,EAAgB;IAErF,IAAI,OAAO,KAAK,SAAS,IAAI,MAAM,KAAK,SAAS,EAAE;QACjD,MAAM,IAAI,KAAK,CAAC,sDAAsD,CAAC,CAAA;KACxE;IAED,IAAI,OAAO,KAAK,SAAS,EAAE;QACzB,MAAM,GAAG,GAAG,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;QAC9B,OAAO,UAAU,KAAK,SAAS;YAC7B,CAAC,CAAC;gBACE,MAAM,EAAE,GAAG;gBACX,UAAU,EAAE,WAAC,CAAA,GAAG,EAAE,CAAC,UAAU,GAAG,qBAAW,CAAC,OAAO,CAAC,EAAE;gBACtD,aAAa,EAAE,GAAG,EAAE,CAAC,aAAa,IAAI,OAAO,EAAE;aAChD;YACH,CAAC,CAAC;gBACE,MAAM,EAAE,GAAG,CAAC,UAAU,CAAC;gBACvB,UAAU,EAAE,WAAC,CAAA,GAAG,EAAE,CAAC,UAAU,GAAG,qBAAW,CAAC,OAAO,CAAC,GAAG,qBAAW,CAAC,UAAU,CAAC,EAAE;gBAChF,aAAa,EAAE,GAAG,EAAE,CAAC,aAAa,IAAI,OAAO,IAAI,qBAAc,CAAC,UAAU,CAAC,EAAE;aAC9E,CAAA;KACN;IAED,IAAI,MAAM,KAAK,SAAS,EAAE;QACxB,IAAI,UAAU,KAAK,SAAS,IAAI,aAAa,KAAK,SAAS,IAAI,YAAY,KAAK,SAAS,EAAE;YACzF,MAAM,IAAI,KAAK,CAAC,6EAA6E,CAAC,CAAA;SAC/F;QACD,OAAO;YACL,MAAM;YACN,UAAU;YACV,YAAY;YACZ,aAAa;SACd,CAAA;KACF;IAED,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAA;AAChE,CAAC;AAED,SAAS,mBAAmB,CAC1B,SAA2B,EAC3B,EAAgB,EAChB,EAAC,QAAQ,EAAE,YAAY,EAAE,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,YAAY,EAAgB;IAE9E,IAAI,IAAI,KAAK,SAAS,IAAI,QAAQ,KAAK,SAAS,EAAE;QAChD,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAA;KACvE;IAED,MAAM,EAAC,GAAG,EAAC,GAAG,EAAE,CAAA;IAEhB,IAAI,QAAQ,KAAK,SAAS,EAAE;QAC1B,MAAM,EAAC,SAAS,EAAE,WAAW,EAAE,IAAI,EAAC,GAAG,EAAE,CAAA;QACzC,MAAM,QAAQ,GAAG,GAAG,CAAC,GAAG,CAAC,MAAM,EAAE,WAAC,CAAA,GAAG,EAAE,CAAC,IAAI,GAAG,qBAAW,CAAC,QAAQ,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;QAC7E,gBAAgB,CAAC,QAAQ,CAAC,CAAA;QAC1B,SAAS,CAAC,SAAS,GAAG,aAAG,CAAA,GAAG,SAAS,GAAG,YAAY,CAAC,QAAQ,EAAE,MAAM,EAAE,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAA;QAC/F,SAAS,CAAC,kBAAkB,GAAG,WAAC,CAAA,GAAG,QAAQ,EAAE,CAAA;QAC7C,SAAS,CAAC,WAAW,GAAG,CAAC,GAAG,WAAW,EAAE,SAAS,CAAC,kBAAkB,CAAC,CAAA;KACvE;IAED,IAAI,IAAI,KAAK,SAAS,EAAE;QACtB,MAAM,QAAQ,GAAG,IAAI,YAAY,cAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA,CAAC,4BAA4B;QACvG,gBAAgB,CAAC,QAAQ,CAAC,CAAA;QAC1B,IAAI,YAAY,KAAK,SAAS;YAAE,SAAS,CAAC,YAAY,GAAG,YAAY,CAAA;QACrE,2GAA2G;KAC5G;IAED,IAAI,SAAS;QAAE,SAAS,CAAC,SAAS,GAAG,SAAS,CAAA;IAE9C,SAAS,gBAAgB,CAAC,SAAe;QACvC,SAAS,CAAC,IAAI,GAAG,SAAS,CAAA;QAC1B,SAAS,CAAC,SAAS,GAAG,EAAE,CAAC,SAAS,GAAG,CAAC,CAAA;QACtC,SAAS,CAAC,SAAS,GAAG,EAAE,CAAA;QACxB,EAAE,CAAC,iBAAiB,GAAG,IAAI,GAAG,EAAU,CAAA;QACxC,SAAS,CAAC,UAAU,GAAG,EAAE,CAAC,IAAI,CAAA;QAC9B,SAAS,CAAC,SAAS,GAAG,CAAC,GAAG,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC,CAAA;IACpD,CAAC;AACH,CAAC;AAED,SAAS,mBAAmB,CAC1B,SAA2B,EAC3B,EAAC,gBAAgB,EAAE,WAAW,EAAE,aAAa,EAAE,YAAY,EAAE,SAAS,EAAgB;IAEtF,IAAI,aAAa,KAAK,SAAS;QAAE,SAAS,CAAC,aAAa,GAAG,aAAa,CAAA;IACxE,IAAI,YAAY,KAAK,SAAS;QAAE,SAAS,CAAC,YAAY,GAAG,YAAY,CAAA;IACrE,IAAI,SAAS,KAAK,SAAS;QAAE,SAAS,CAAC,SAAS,GAAG,SAAS,CAAA;IAC5D,SAAS,CAAC,gBAAgB,GAAG,gBAAgB,CAAA,CAAC,gBAAgB;IAC9D,SAAS,CAAC,WAAW,GAAG,WAAW,CAAA,CAAC,gBAAgB;AACtD,CAAC;AAED,SAAS,YAAY,CACnB,QAAgC,EAChC,YAAmB,EACnB,gBAA0B;IAE1B,WAAW;IACX,IAAI,QAAQ,YAAY,cAAI,EAAE;QAC5B,MAAM,QAAQ,GAAG,YAAY,KAAK,IAAI,CAAC,GAAG,CAAA;QAC1C,OAAO,gBAAgB;YACrB,CAAC,CAAC,QAAQ;gBACR,CAAC,CAAC,WAAC,CAAA,SAAS,QAAQ,QAAQ;gBAC5B,CAAC,CAAC,WAAC,CAAA,UAAU,QAAQ,SAAS;YAChC,CAAC,CAAC,QAAQ;gBACV,CAAC,CAAC,WAAC,CAAA,SAAS,QAAQ,EAAE;gBACtB,CAAC,CAAC,WAAC,CAAA,SAAS,QAAQ,4CAA4C,CAAA,CAAC,sCAAsC;KAC1G;IACD,OAAO,gBAAgB,CAAC,CAAC,CAAC,qBAAW,CAAC,QAAQ,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG,wBAAiB,CAAC,QAAQ,CAAC,CAAA;AAChG,CAAC"}
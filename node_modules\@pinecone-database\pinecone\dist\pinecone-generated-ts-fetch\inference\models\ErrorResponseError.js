"use strict";
/* tslint:disable */
/* eslint-disable */
/**
 * Pinecone Inference API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ErrorResponseErrorToJSON = exports.ErrorResponseErrorFromJSONTyped = exports.ErrorResponseErrorFromJSON = exports.instanceOfErrorResponseError = exports.ErrorResponseErrorCodeEnum = void 0;
var runtime_1 = require("../runtime");
/**
 * @export
 */
exports.ErrorResponseErrorCodeEnum = {
    Ok: 'OK',
    Unknown: 'UNKNOWN',
    InvalidArgument: 'INVALID_ARGUMENT',
    DeadlineExceeded: 'DEADLINE_EXCEEDED',
    QuotaExceeded: 'QUOTA_EXCEEDED',
    NotFound: 'NOT_FOUND',
    AlreadyExists: 'ALREADY_EXISTS',
    PermissionDenied: 'PERMISSION_DENIED',
    Unauthenticated: 'UNAUTHENTICATED',
    ResourceExhausted: 'RESOURCE_EXHAUSTED',
    FailedPrecondition: 'FAILED_PRECONDITION',
    Aborted: 'ABORTED',
    OutOfRange: 'OUT_OF_RANGE',
    Unimplemented: 'UNIMPLEMENTED',
    Internal: 'INTERNAL',
    Unavailable: 'UNAVAILABLE',
    DataLoss: 'DATA_LOSS',
    Forbidden: 'FORBIDDEN'
};
/**
 * Check if a given object implements the ErrorResponseError interface.
 */
function instanceOfErrorResponseError(value) {
    var isInstance = true;
    isInstance = isInstance && "code" in value;
    isInstance = isInstance && "message" in value;
    return isInstance;
}
exports.instanceOfErrorResponseError = instanceOfErrorResponseError;
function ErrorResponseErrorFromJSON(json) {
    return ErrorResponseErrorFromJSONTyped(json, false);
}
exports.ErrorResponseErrorFromJSON = ErrorResponseErrorFromJSON;
function ErrorResponseErrorFromJSONTyped(json, ignoreDiscriminator) {
    if ((json === undefined) || (json === null)) {
        return json;
    }
    return {
        'code': json['code'],
        'message': json['message'],
        'details': !(0, runtime_1.exists)(json, 'details') ? undefined : json['details'],
    };
}
exports.ErrorResponseErrorFromJSONTyped = ErrorResponseErrorFromJSONTyped;
function ErrorResponseErrorToJSON(value) {
    if (value === undefined) {
        return undefined;
    }
    if (value === null) {
        return null;
    }
    return {
        'code': value.code,
        'message': value.message,
        'details': value.details,
    };
}
exports.ErrorResponseErrorToJSON = ErrorResponseErrorToJSON;
//# sourceMappingURL=ErrorResponseError.js.map
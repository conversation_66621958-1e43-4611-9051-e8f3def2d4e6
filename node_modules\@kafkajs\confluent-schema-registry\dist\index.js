"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.COMPATIBILITY = exports.SchemaType = exports.SchemaRegistry = void 0;
const SchemaRegistry_1 = __importDefault(require("./SchemaRegistry"));
Object.defineProperty(exports, "SchemaRegistry", { enumerable: true, get: function () { return SchemaRegistry_1.default; } });
__exportStar(require("./utils"), exports);
var _types_1 = require("./@types");
Object.defineProperty(exports, "SchemaType", { enumerable: true, get: function () { return _types_1.SchemaType; } });
var constants_1 = require("./constants");
Object.defineProperty(exports, "COMPATIBILITY", { enumerable: true, get: function () { return constants_1.COMPATIBILITY; } });
//# sourceMappingURL=index.js.map
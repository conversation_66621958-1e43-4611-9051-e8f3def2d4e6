{"version": 3, "file": "avdlToAVSC.js", "sourceRoot": "", "sources": ["../../src/utils/avdlToAVSC.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoFA,gCAKC;AAED,0CAcC;AAzGD,uCAAwB;AACxB,+BAAqD;AAErD,sCAAwD;AAkBxD,IAAI,KAAU,CAAA;AACd,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAA;AAC3B,MAAM,QAAQ,GAAG,CAAC,GAAY,EAAc,EAAE,CAAC,CAAC,CAAC,GAAG,IAAI,OAAO,GAAG,KAAK,QAAQ,CAAA;AAC/E,MAAM,UAAU,GAAG,CAAC,GAAY,EAAmB,EAAE,CACnD,QAAQ,CAAC,GAAG,CAAC,IAAI,OAAO,GAAG,CAAC,GAAG,KAAK,WAAW,CAAA;AACjD,MAAM,YAAY,GAAG,CAAC,KAAc,EAAkB,EAAE,CACtD,QAAQ,CAAC,KAAK,CAAC,IAAI,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,KAAK,OAAO,CAAA;AAExE,MAAM,OAAO,GAAG,CAAC,QAAa,EAAE,KAAU,EAAE,EAAE;IAC5C,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;QACrB,OAAO,QAAQ,CAAA;IACjB,CAAC;IAED,MAAM,IAAI,GAAG,CAAC,IAAS,EAAE,EAAE;QACzB,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;YAC7B,IAAI,GAAG,IAAI,CAAC,WAAW,EAAE,CAAA;QAC3B,CAAC;QAED,MAAM,aAAa,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAM,EAAE,EAAE;YAC1C,MAAM,KAAK,GAAG,EAAE,CAAA;YAChB,IAAI,CAAC,CAAC,SAAS,EAAE,CAAC;gBAChB,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,SAAS,GAAG,CAAC,CAAA;YAC/B,CAAC;YACD,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAA;YAEhC,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,IAAI,CAAA;QAChC,CAAC,CAAC,CAAA;QAEF,IAAI,CAAC,aAAa,IAAI,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC;YAChD,OAAO,IAAI,CAAA;QACb,CAAC;QAED,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QAE7B,OAAO,OAAO,CAAC,aAAa,EAAE,KAAK,CAAC,CAAA;IACtC,CAAC,CAAA;IAED,MAAM,cAAc,GAAG,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,KAAU,EAAE,EAAE;QACxD,IAAI,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC;YACxB,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;YAC5C,OAAO,aAAa;gBAClB,CAAC,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,EAAE,KAAK,EAAE,aAAa,EAAE,CAAC,EAAE,CAAC;gBACrE,CAAC,CAAC,KAAK,CAAA;QACX,CAAC;aAAM,IAAI,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;YAClC,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,SAAc,EAAE,EAAE;gBAC7C,IAAI,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;oBACxB,MAAM,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAA;oBAC3C,OAAO,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,aAAa,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS,CAAA;gBAC/E,CAAC;qBAAM,CAAC;oBACN,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,SAAS,CAAA;gBACrC,CAAC;YACH,CAAC,CAAC,CAAA;YAEF,OAAO,KAAK,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,CAAC,CAAA;QAC/B,CAAC;QAED,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;QACtC,OAAO,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,aAAa,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,CAAA;IACtE,CAAC,CAAC,CAAA;IAEF,OAAO,KAAK,CAAC,QAAQ,EAAE,EAAE,MAAM,EAAE,cAAc,EAAE,CAAC,CAAA;AACpD,CAAC,CAAA;AAED,SAAgB,UAAU,CAAC,IAAS;IAClC,KAAK,GAAG,EAAE,CAAA;IACV,MAAM,QAAQ,GAAG,IAAA,mBAAY,EAAC,EAAE,CAAC,YAAY,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAA;IAE5D,OAAO,KAAK,CAAC,EAAE,SAAS,EAAE,QAAQ,CAAC,SAAS,EAAE,EAAE,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,EAAE,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAA;AAChG,CAAC;AAEM,KAAK,UAAU,eAAe,CAAC,IAAY;IAChD,KAAK,GAAG,EAAE,CAAA;IAEV,MAAM,QAAQ,GAAwB,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QAC1E,IAAA,uBAAgB,EAAC,IAAI,EAAE,CAAC,GAA0B,EAAE,MAAM,EAAE,EAAE;YAC5D,IAAI,GAAG,EAAE,CAAC;gBACR,MAAM,CAAC,IAAI,qCAA4B,CAAC,GAAG,GAAG,CAAC,OAAO,gBAAgB,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,CAAA;YACpF,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,MAA6B,CAAC,CAAA;YACxC,CAAC;QACH,CAAC,CAAC,CAAA;IACJ,CAAC,CAAC,CAAA;IAEF,OAAO,KAAK,CAAC,EAAE,SAAS,EAAE,QAAQ,CAAC,SAAS,EAAE,EAAE,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,EAAE,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAA;AAChG,CAAC"}
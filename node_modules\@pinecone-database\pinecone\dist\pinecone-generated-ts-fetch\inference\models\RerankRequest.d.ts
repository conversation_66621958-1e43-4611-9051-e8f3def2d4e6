/**
 * Pinecone Inference API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
/**
 *
 * @export
 * @interface RerankRequest
 */
export interface RerankRequest {
    /**
     * The [model](https://docs.pinecone.io/guides/inference/understanding-inference#models) to use for reranking.
     * @type {string}
     * @memberof RerankRequest
     */
    model: string;
    /**
     * The query to rerank documents against.
     * @type {string}
     * @memberof RerankRequest
     */
    query: string;
    /**
     * The number of results to return sorted by relevance. Defaults to the number of inputs.
     * @type {number}
     * @memberof RerankRequest
     */
    topN?: number;
    /**
     * Whether to return the documents in the response.
     * @type {boolean}
     * @memberof RerankRequest
     */
    returnDocuments?: boolean;
    /**
     * The fields to rank the documents by. If not provided, the default is `"text"`.
     * @type {Array<string>}
     * @memberof RerankRequest
     */
    rankFields?: Array<string>;
    /**
     * The documents to rerank.
     * @type {Array<{ [key: string]: string; }>}
     * @memberof RerankRequest
     */
    documents: Array<{
        [key: string]: string;
    }>;
    /**
     * Additional model-specific parameters for the reranker.
     * @type {{ [key: string]: string; }}
     * @memberof RerankRequest
     */
    parameters?: {
        [key: string]: string;
    };
}
/**
 * Check if a given object implements the RerankRequest interface.
 */
export declare function instanceOfRerankRequest(value: object): boolean;
export declare function RerankRequestFromJSON(json: any): RerankRequest;
export declare function RerankRequestFromJSONTyped(json: any, ignoreDiscriminator: boolean): RerankRequest;
export declare function RerankRequestToJSON(value?: RerankRequest | null): any;

/*
 * Copyright (c) Microsoft Corporation.
 * Licensed under the MIT License.
 *
 * Code generated by Microsoft (R) AutoRest Code Generator.
 * Changes may cause incorrect behavior and will be lost if the code is regenerated.
 */
import * as coreClient from "@azure/core-client";
import * as Mappers from "../models/mappers";
import * as Parameters from "../models/parameters";
/** Class containing Service operations. */
export class ServiceImpl {
    /**
     * Initialize a new instance of the class Service class.
     * @param client Reference to the service client
     */
    constructor(client) {
        this.client = client;
    }
    /**
     * Sets properties for a storage account's Blob service endpoint, including properties for Storage
     * Analytics and CORS (Cross-Origin Resource Sharing) rules
     * @param blobServiceProperties The StorageService properties.
     * @param options The options parameters.
     */
    setProperties(blobServiceProperties, options) {
        return this.client.sendOperationRequest({ blobServiceProperties, options }, setPropertiesOperationSpec);
    }
    /**
     * gets the properties of a storage account's Blob service, including properties for Storage Analytics
     * and CORS (Cross-Origin Resource Sharing) rules.
     * @param options The options parameters.
     */
    getProperties(options) {
        return this.client.sendOperationRequest({ options }, getPropertiesOperationSpec);
    }
    /**
     * Retrieves statistics related to replication for the Blob service. It is only available on the
     * secondary location endpoint when read-access geo-redundant replication is enabled for the storage
     * account.
     * @param options The options parameters.
     */
    getStatistics(options) {
        return this.client.sendOperationRequest({ options }, getStatisticsOperationSpec);
    }
    /**
     * The List Containers Segment operation returns a list of the containers under the specified account
     * @param options The options parameters.
     */
    listContainersSegment(options) {
        return this.client.sendOperationRequest({ options }, listContainersSegmentOperationSpec);
    }
    /**
     * Retrieves a user delegation key for the Blob service. This is only a valid operation when using
     * bearer token authentication.
     * @param keyInfo Key information
     * @param options The options parameters.
     */
    getUserDelegationKey(keyInfo, options) {
        return this.client.sendOperationRequest({ keyInfo, options }, getUserDelegationKeyOperationSpec);
    }
    /**
     * Returns the sku name and account kind
     * @param options The options parameters.
     */
    getAccountInfo(options) {
        return this.client.sendOperationRequest({ options }, getAccountInfoOperationSpec);
    }
    /**
     * The Batch operation allows multiple API calls to be embedded into a single HTTP request.
     * @param contentLength The length of the request.
     * @param multipartContentType Required. The value of this header must be multipart/mixed with a batch
     *                             boundary. Example header value: multipart/mixed; boundary=batch_<GUID>
     * @param body Initial data
     * @param options The options parameters.
     */
    submitBatch(contentLength, multipartContentType, body, options) {
        return this.client.sendOperationRequest({ contentLength, multipartContentType, body, options }, submitBatchOperationSpec);
    }
    /**
     * The Filter Blobs operation enables callers to list blobs across all containers whose tags match a
     * given search expression.  Filter blobs searches across all containers within a storage account but
     * can be scoped within the expression to a single container.
     * @param options The options parameters.
     */
    filterBlobs(options) {
        return this.client.sendOperationRequest({ options }, filterBlobsOperationSpec);
    }
}
// Operation Specifications
const xmlSerializer = coreClient.createSerializer(Mappers, /* isXml */ true);
const setPropertiesOperationSpec = {
    path: "/",
    httpMethod: "PUT",
    responses: {
        202: {
            headersMapper: Mappers.ServiceSetPropertiesHeaders,
        },
        default: {
            bodyMapper: Mappers.StorageError,
            headersMapper: Mappers.ServiceSetPropertiesExceptionHeaders,
        },
    },
    requestBody: Parameters.blobServiceProperties,
    queryParameters: [
        Parameters.restype,
        Parameters.comp,
        Parameters.timeoutInSeconds,
    ],
    urlParameters: [Parameters.url],
    headerParameters: [
        Parameters.contentType,
        Parameters.accept,
        Parameters.version,
        Parameters.requestId,
    ],
    isXML: true,
    contentType: "application/xml; charset=utf-8",
    mediaType: "xml",
    serializer: xmlSerializer,
};
const getPropertiesOperationSpec = {
    path: "/",
    httpMethod: "GET",
    responses: {
        200: {
            bodyMapper: Mappers.BlobServiceProperties,
            headersMapper: Mappers.ServiceGetPropertiesHeaders,
        },
        default: {
            bodyMapper: Mappers.StorageError,
            headersMapper: Mappers.ServiceGetPropertiesExceptionHeaders,
        },
    },
    queryParameters: [
        Parameters.restype,
        Parameters.comp,
        Parameters.timeoutInSeconds,
    ],
    urlParameters: [Parameters.url],
    headerParameters: [
        Parameters.version,
        Parameters.requestId,
        Parameters.accept1,
    ],
    isXML: true,
    serializer: xmlSerializer,
};
const getStatisticsOperationSpec = {
    path: "/",
    httpMethod: "GET",
    responses: {
        200: {
            bodyMapper: Mappers.BlobServiceStatistics,
            headersMapper: Mappers.ServiceGetStatisticsHeaders,
        },
        default: {
            bodyMapper: Mappers.StorageError,
            headersMapper: Mappers.ServiceGetStatisticsExceptionHeaders,
        },
    },
    queryParameters: [
        Parameters.restype,
        Parameters.timeoutInSeconds,
        Parameters.comp1,
    ],
    urlParameters: [Parameters.url],
    headerParameters: [
        Parameters.version,
        Parameters.requestId,
        Parameters.accept1,
    ],
    isXML: true,
    serializer: xmlSerializer,
};
const listContainersSegmentOperationSpec = {
    path: "/",
    httpMethod: "GET",
    responses: {
        200: {
            bodyMapper: Mappers.ListContainersSegmentResponse,
            headersMapper: Mappers.ServiceListContainersSegmentHeaders,
        },
        default: {
            bodyMapper: Mappers.StorageError,
            headersMapper: Mappers.ServiceListContainersSegmentExceptionHeaders,
        },
    },
    queryParameters: [
        Parameters.timeoutInSeconds,
        Parameters.comp2,
        Parameters.prefix,
        Parameters.marker,
        Parameters.maxPageSize,
        Parameters.include,
    ],
    urlParameters: [Parameters.url],
    headerParameters: [
        Parameters.version,
        Parameters.requestId,
        Parameters.accept1,
    ],
    isXML: true,
    serializer: xmlSerializer,
};
const getUserDelegationKeyOperationSpec = {
    path: "/",
    httpMethod: "POST",
    responses: {
        200: {
            bodyMapper: Mappers.UserDelegationKey,
            headersMapper: Mappers.ServiceGetUserDelegationKeyHeaders,
        },
        default: {
            bodyMapper: Mappers.StorageError,
            headersMapper: Mappers.ServiceGetUserDelegationKeyExceptionHeaders,
        },
    },
    requestBody: Parameters.keyInfo,
    queryParameters: [
        Parameters.restype,
        Parameters.timeoutInSeconds,
        Parameters.comp3,
    ],
    urlParameters: [Parameters.url],
    headerParameters: [
        Parameters.contentType,
        Parameters.accept,
        Parameters.version,
        Parameters.requestId,
    ],
    isXML: true,
    contentType: "application/xml; charset=utf-8",
    mediaType: "xml",
    serializer: xmlSerializer,
};
const getAccountInfoOperationSpec = {
    path: "/",
    httpMethod: "GET",
    responses: {
        200: {
            headersMapper: Mappers.ServiceGetAccountInfoHeaders,
        },
        default: {
            bodyMapper: Mappers.StorageError,
            headersMapper: Mappers.ServiceGetAccountInfoExceptionHeaders,
        },
    },
    queryParameters: [
        Parameters.comp,
        Parameters.timeoutInSeconds,
        Parameters.restype1,
    ],
    urlParameters: [Parameters.url],
    headerParameters: [
        Parameters.version,
        Parameters.requestId,
        Parameters.accept1,
    ],
    isXML: true,
    serializer: xmlSerializer,
};
const submitBatchOperationSpec = {
    path: "/",
    httpMethod: "POST",
    responses: {
        202: {
            bodyMapper: {
                type: { name: "Stream" },
                serializedName: "parsedResponse",
            },
            headersMapper: Mappers.ServiceSubmitBatchHeaders,
        },
        default: {
            bodyMapper: Mappers.StorageError,
            headersMapper: Mappers.ServiceSubmitBatchExceptionHeaders,
        },
    },
    requestBody: Parameters.body,
    queryParameters: [Parameters.timeoutInSeconds, Parameters.comp4],
    urlParameters: [Parameters.url],
    headerParameters: [
        Parameters.accept,
        Parameters.version,
        Parameters.requestId,
        Parameters.contentLength,
        Parameters.multipartContentType,
    ],
    isXML: true,
    contentType: "application/xml; charset=utf-8",
    mediaType: "xml",
    serializer: xmlSerializer,
};
const filterBlobsOperationSpec = {
    path: "/",
    httpMethod: "GET",
    responses: {
        200: {
            bodyMapper: Mappers.FilterBlobSegment,
            headersMapper: Mappers.ServiceFilterBlobsHeaders,
        },
        default: {
            bodyMapper: Mappers.StorageError,
            headersMapper: Mappers.ServiceFilterBlobsExceptionHeaders,
        },
    },
    queryParameters: [
        Parameters.timeoutInSeconds,
        Parameters.marker,
        Parameters.maxPageSize,
        Parameters.comp5,
        Parameters.where,
    ],
    urlParameters: [Parameters.url],
    headerParameters: [
        Parameters.version,
        Parameters.requestId,
        Parameters.accept1,
    ],
    isXML: true,
    serializer: xmlSerializer,
};
//# sourceMappingURL=service.js.map
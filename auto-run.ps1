# FULLY AUTOMATED META AI AGENT - NO MANUAL WORK
Write-Host "🤖 FULLY AUTOMATED META AI AGENT" -ForegroundColor Cyan
Write-Host "=================================" -ForegroundColor Cyan
Write-Host ""

# Step 1: Install dependencies automatically
Write-Host "📦 Installing dependencies..." -ForegroundColor Yellow
npm install axios 2>$null
Write-Host "✅ Dependencies ready!" -ForegroundColor Green

# Step 2: Create instant-use batch files
Write-Host "⚡ Creating instant-use commands..." -ForegroundColor Yellow

# Create simple batch files
'@echo off
echo 🤖 Creating AI Agent...
node meta-ai-agent.js %*
pause' | Out-File -FilePath "create-agent.bat" -Encoding ASCII

'@echo off
echo 🤖 Creating Chatbot...
node meta-ai-agent.js "Create a chatbot for customer support"
pause' | Out-File -FilePath "create-chatbot.bat" -Encoding ASCII

'@echo off
echo 📧 Creating Email Automation...
node meta-ai-agent.js "Build an email automation for new subscribers"
pause' | Out-File -FilePath "create-email.bat" -Encoding ASCII

Write-Host "✅ Instant commands created!" -ForegroundColor Green

# Step 3: Auto-create first demo agent
Write-Host "🚀 Auto-creating your first AI agent..." -ForegroundColor Yellow
try {
    node meta-ai-agent.js "Create a chatbot for customer support" 2>$null
    Write-Host "✅ First agent created!" -ForegroundColor Green
} catch {
    Write-Host "⚠️  Agent will be created when you run the commands" -ForegroundColor Yellow
}

# Step 4: Auto-open n8n
Write-Host "🌐 Opening n8n interface..." -ForegroundColor Yellow
Start-Process "http://localhost:2410"

Write-Host ""
Write-Host "🎉 SETUP COMPLETE - ZERO MANUAL WORK!" -ForegroundColor Green
Write-Host "====================================" -ForegroundColor Green
Write-Host ""
Write-Host "🚀 READY TO USE - Just double-click:" -ForegroundColor Cyan
Write-Host "   📁 create-agent.bat     - Create any agent" -ForegroundColor White
Write-Host "   🤖 create-chatbot.bat   - Create chatbot" -ForegroundColor White
Write-Host "   📧 create-email.bat     - Create email automation" -ForegroundColor White
Write-Host ""
Write-Host "💻 Or use command line:" -ForegroundColor Cyan
Write-Host '   node meta-ai-agent.js "Create whatever you want"' -ForegroundColor Gray
Write-Host ""
Write-Host "✨ NO MANUAL WORK REQUIRED! ✨" -ForegroundColor Magenta

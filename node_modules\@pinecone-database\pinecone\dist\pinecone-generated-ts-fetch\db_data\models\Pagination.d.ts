/**
 * Pinecone Data Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
/**
 *
 * @export
 * @interface Pagination
 */
export interface Pagination {
    /**
     *
     * @type {string}
     * @memberof Pagination
     */
    next?: string;
}
/**
 * Check if a given object implements the Pagination interface.
 */
export declare function instanceOfPagination(value: object): boolean;
export declare function PaginationFromJSON(json: any): Pagination;
export declare function PaginationFromJSONTyped(json: any, ignoreDiscriminator: boolean): Pagination;
export declare function PaginationToJSON(value?: Pagination | null): any;

/**
 * Pinecone Data Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import type { NamespaceSummary } from './NamespaceSummary';
/**
 * The response for the `describe_index_stats` operation.
 * @export
 * @interface IndexDescription
 */
export interface IndexDescription {
    /**
     * A mapping for each namespace in the index from the namespace name to a summary of its contents. If a metadata filter expression is present, the summary will reflect only vectors matching that expression.
     * @type {{ [key: string]: NamespaceSummary; }}
     * @memberof IndexDescription
     */
    namespaces?: {
        [key: string]: NamespaceSummary;
    };
    /**
     * The dimension of the indexed vectors.
     * @type {number}
     * @memberof IndexDescription
     */
    dimension?: number;
    /**
     * The fullness of the index, regardless of whether a metadata filter expression was passed. The granularity of this metric is 10%.
     *
     * Serverless indexes scale automatically as needed, so index fullness  is relevant only for pod-based indexes.
     *
     * The index fullness result may be inaccurate during pod resizing; to get the status of a pod resizing process, use [`describe_index`](https://docs.pinecone.io/reference/api/control-plane/describe_index).
     * @type {number}
     * @memberof IndexDescription
     */
    indexFullness?: number;
    /**
     * The total number of vectors in the index, regardless of whether a metadata filter expression was passed
     * @type {number}
     * @memberof IndexDescription
     */
    totalVectorCount?: number;
}
/**
 * Check if a given object implements the IndexDescription interface.
 */
export declare function instanceOfIndexDescription(value: object): boolean;
export declare function IndexDescriptionFromJSON(json: any): IndexDescription;
export declare function IndexDescriptionFromJSONTyped(json: any, ignoreDiscriminator: boolean): IndexDescription;
export declare function IndexDescriptionToJSON(value?: IndexDescription | null): any;

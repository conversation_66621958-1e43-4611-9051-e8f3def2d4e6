{"version": 3, "file": "schemaTypeResolver.js", "sourceRoot": "", "sources": ["../src/schemaTypeResolver.ts"], "names": [], "mappings": ";;;;;;AAAA,8DAAqC;AACrC,8DAAqC;AACrC,8DAAqC;AACrC,gEAAuC;AACvC,gEAAuC;AACvC,qCAYiB;AACjB,qCAA+D;AAE/D,MAAM,2BAA2B,GAAiC,EAAE,CAAA;AAE7D,MAAM,oBAAoB,GAAG,CAAC,gBAAwB,EAAE,EAAE;IAC/D,QAAQ,gBAAgB,EAAE,CAAC;QACzB,KAAK,MAAM,CAAC;QACZ,KAAK,SAAS;YACZ,OAAO,mBAAU,CAAC,IAAI,CAAA;QACxB,KAAK,MAAM;YACT,OAAO,mBAAU,CAAC,IAAI,CAAA;QACxB,KAAK,UAAU;YACb,OAAO,mBAAU,CAAC,QAAQ,CAAA;QAC5B;YACE,OAAO,mBAAU,CAAC,OAAO,CAAA;IAC7B,CAAC;AACH,CAAC,CAAA;AAZY,QAAA,oBAAoB,wBAYhC;AAEM,MAAM,wBAAwB,GAAG,CACtC,aAAyB,mBAAU,CAAC,IAAI,EAC1B,EAAE;IAChB,MAAM,aAAa,GAAG,UAAU,CAAC,QAAQ,EAAE,CAAA;IAE3C,IAAI,CAAC,2BAA2B,CAAC,aAAa,CAAC,EAAE,CAAC;QAChD,IAAI,MAAM,CAAA;QACV,QAAQ,UAAU,EAAE,CAAC;YACnB,KAAK,mBAAU,CAAC,IAAI,CAAC,CAAC,CAAC;gBACrB,MAAM,GAAG,IAAI,oBAAU,EAAE,CAAA;gBACzB,MAAK;YACP,CAAC;YACD,KAAK,mBAAU,CAAC,IAAI,CAAC,CAAC,CAAC;gBACrB,MAAM,GAAG,IAAI,oBAAU,EAAE,CAAA;gBACzB,MAAK;YACP,CAAC;YACD,KAAK,mBAAU,CAAC,QAAQ,CAAC,CAAC,CAAC;gBACzB,MAAM,GAAG,IAAI,qBAAW,EAAE,CAAA;gBAC1B,MAAK;YACP,CAAC;YACD;gBACE,MAAM,IAAI,6CAAoC,CAAC,oBAAoB,CAAC,CAAA;QACxE,CAAC;QACD,2BAA2B,CAAC,aAAa,CAAC,GAAG,MAAM,CAAA;IACrD,CAAC;IACD,OAAO,2BAA2B,CAAC,aAAa,CAAC,CAAA;AACnD,CAAC,CAAA;AA1BY,QAAA,wBAAwB,4BA0BpC;AAEM,MAAM,yBAAyB,GAAG,CACvC,eAAgC,EAChC,OAAwC,EACnB,EAAE;IACvB,IAAI,CAAC;QACH,IAAI,MAAc,CAAA;QAElB,QAAQ,eAAe,CAAC,IAAI,EAAE,CAAC;YAC7B,KAAK,mBAAU,CAAC,IAAI,CAAC,CAAC,CAAC;gBACrB,MAAM,IAAI,GACR,CAAC,OAAyB,aAAzB,OAAO,uBAAP,OAAO,CAAoB,gBAAgB;qBAC3C,OAA2B,aAA3B,OAAO,uBAAP,OAAO,CAAuB,mBAAU,CAAC,IAAI,CAAC,CAAA,CAAA;gBACjD,MAAM,GAAI,IAAA,gCAAwB,EAAC,eAAe,CAAC,IAAI,CAAgB,CAAC,aAAa,CACnF,eAAe,EACf,IAAI,CACL,CAAA;gBACD,MAAK;YACP,CAAC;YACD,KAAK,mBAAU,CAAC,IAAI,CAAC,CAAC,CAAC;gBACrB,MAAM,IAAI,GAA6B,OAA2B,aAA3B,OAAO,uBAAP,OAAO,CAAuB,mBAAU,CAAC,IAAI,CAAC,CAAA;gBACrF,MAAM,GAAG,IAAI,oBAAU,CAAC,eAAe,EAAE,IAAI,CAAC,CAAA;gBAC9C,MAAK;YACP,CAAC;YACD,KAAK,mBAAU,CAAC,QAAQ,CAAC,CAAC,CAAC;gBACzB,MAAM,IAAI,GAA8B,OAA2B,aAA3B,OAAO,uBAAP,OAAO,CAAuB,mBAAU,CAAC,QAAQ,CAAC,CAAA;gBAC1F,MAAM,GAAG,IAAI,qBAAW,CAAC,eAAe,EAAE,IAAI,CAAC,CAAA;gBAC/C,MAAK;YACP,CAAC;YACD;gBACE,MAAM,IAAI,6CAAoC,CAAC,oBAAoB,CAAC,CAAA;QACxE,CAAC;QAED,OAAO,MAAM,CAAA;IACf,CAAC;IAAC,OAAO,GAAG,EAAE,CAAC;QACb,IAAI,GAAG,YAAY,KAAK;YAAE,MAAM,IAAI,6CAAoC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAA;QACrF,MAAM,GAAG,CAAA;IACX,CAAC;AACH,CAAC,CAAA;AArCY,QAAA,yBAAyB,6BAqCrC"}
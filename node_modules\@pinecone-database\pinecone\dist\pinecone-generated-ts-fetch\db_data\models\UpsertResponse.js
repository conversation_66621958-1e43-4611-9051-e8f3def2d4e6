"use strict";
/* tslint:disable */
/* eslint-disable */
/**
 * Pinecone Data Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpsertResponseToJSON = exports.UpsertResponseFromJSONTyped = exports.UpsertResponseFromJSON = exports.instanceOfUpsertResponse = void 0;
var runtime_1 = require("../runtime");
/**
 * Check if a given object implements the UpsertResponse interface.
 */
function instanceOfUpsertResponse(value) {
    var isInstance = true;
    return isInstance;
}
exports.instanceOfUpsertResponse = instanceOfUpsertResponse;
function UpsertResponseFromJSON(json) {
    return UpsertResponseFromJSONTyped(json, false);
}
exports.UpsertResponseFromJSON = UpsertResponseFromJSON;
function UpsertResponseFromJSONTyped(json, ignoreDiscriminator) {
    if ((json === undefined) || (json === null)) {
        return json;
    }
    return {
        'upsertedCount': !(0, runtime_1.exists)(json, 'upsertedCount') ? undefined : json['upsertedCount'],
    };
}
exports.UpsertResponseFromJSONTyped = UpsertResponseFromJSONTyped;
function UpsertResponseToJSON(value) {
    if (value === undefined) {
        return undefined;
    }
    if (value === null) {
        return null;
    }
    return {
        'upsertedCount': value.upsertedCount,
    };
}
exports.UpsertResponseToJSON = UpsertResponseToJSON;
//# sourceMappingURL=UpsertResponse.js.map
/**
 * Pinecone Inference API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
/**
 * Usage statistics for the model inference.
 * @export
 * @interface RerankResultUsage
 */
export interface RerankResultUsage {
    /**
     *
     * @type {number}
     * @memberof RerankResultUsage
     */
    rerankUnits?: number;
}
/**
 * Check if a given object implements the RerankResultUsage interface.
 */
export declare function instanceOfRerankResultUsage(value: object): boolean;
export declare function RerankResultUsageFromJSON(json: any): RerankResultUsage;
export declare function RerankResultUsageFromJSONTyped(json: any, ignoreDiscriminator: boolean): RerankResultUsage;
export declare function RerankResultUsageToJSON(value?: RerankResultUsage | null): any;

"use strict";
/* tslint:disable */
/* eslint-disable */
/**
 * Pinecone Inference API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmbeddingToJSON = exports.EmbeddingFromJSONTyped = exports.EmbeddingFromJSON = exports.instanceOfEmbedding = void 0;
var runtime_1 = require("../runtime");
/**
 * Check if a given object implements the Embedding interface.
 */
function instanceOfEmbedding(value) {
    var isInstance = true;
    return isInstance;
}
exports.instanceOfEmbedding = instanceOfEmbedding;
function EmbeddingFromJSON(json) {
    return EmbeddingFromJSONTyped(json, false);
}
exports.EmbeddingFromJSON = EmbeddingFromJSON;
function EmbeddingFromJSONTyped(json, ignoreDiscriminator) {
    if ((json === undefined) || (json === null)) {
        return json;
    }
    return {
        'values': !(0, runtime_1.exists)(json, 'values') ? undefined : json['values'],
    };
}
exports.EmbeddingFromJSONTyped = EmbeddingFromJSONTyped;
function EmbeddingToJSON(value) {
    if (value === undefined) {
        return undefined;
    }
    if (value === null) {
        return null;
    }
    return {
        'values': value.values,
    };
}
exports.EmbeddingToJSON = EmbeddingToJSON;
//# sourceMappingURL=Embedding.js.map
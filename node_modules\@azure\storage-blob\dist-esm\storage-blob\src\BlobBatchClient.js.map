{"version": 3, "file": "BlobBatchClient.js", "sourceRoot": "", "sources": ["../../../src/BlobBatchClient.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AASlC,OAAO,EAAE,mBAAmB,EAAE,MAAM,uBAAuB,CAAC;AAC5D,OAAO,EAAE,cAAc,EAAE,MAAM,cAAc,CAAC;AAC9C,OAAO,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;AACxC,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAIhD,OAAO,EAAE,mBAAmB,EAAE,MAAM,mCAAmC,CAAC;AAExE,OAAO,EAAE,oBAAoB,EAAE,MAAM,wBAAwB,CAAC;AAE9D,OAAO,EAAE,WAAW,EAAE,cAAc,EAAE,oBAAoB,EAAE,MAAM,YAAY,CAAC;AAE/E,OAAO,EAAE,cAAc,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAyBlE;;;;GAIG;AACH,MAAM,OAAO,eAAe;IA8B1B,YACE,GAAW,EACX,oBAIgB;IAChB,mFAAmF;IACnF,gEAAgE;IAChE,OAAgC;QAEhC,IAAI,QAAsB,CAAC;QAC3B,IAAI,cAAc,CAAC,oBAAoB,CAAC,EAAE,CAAC;YACzC,QAAQ,GAAG,oBAAoB,CAAC;QAClC,CAAC;aAAM,IAAI,CAAC,oBAAoB,EAAE,CAAC;YACjC,yBAAyB;YACzB,QAAQ,GAAG,WAAW,CAAC,IAAI,mBAAmB,EAAE,EAAE,OAAO,CAAC,CAAC;QAC7D,CAAC;aAAM,CAAC;YACN,QAAQ,GAAG,WAAW,CAAC,oBAAoB,EAAE,OAAO,CAAC,CAAC;QACxD,CAAC;QAED,MAAM,oBAAoB,GAAG,IAAI,oBAAoB,CAAC,GAAG,EAAE,oBAAoB,CAAC,QAAQ,CAAC,CAAC,CAAC;QAE3F,MAAM,IAAI,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;QAC7B,IAAI,IAAI,IAAI,IAAI,KAAK,GAAG,EAAE,CAAC;YACzB,oBAAoB;YACpB,IAAI,CAAC,yBAAyB,GAAG,oBAAoB,CAAC,SAAS,CAAC;QAClE,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,yBAAyB,GAAG,oBAAoB,CAAC,OAAO,CAAC;QAChE,CAAC;IACH,CAAC;IAED;;;OAGG;IACI,WAAW;QAChB,OAAO,IAAI,SAAS,EAAE,CAAC;IACzB,CAAC;IAsCM,KAAK,CAAC,WAAW,CACtB,iBAA0C,EAC1C,mBAKa;IACb,mFAAmF;IACnF,gEAAgE;IAChE,OAA2B;QAE3B,MAAM,KAAK,GAAG,IAAI,SAAS,EAAE,CAAC;QAC9B,KAAK,MAAM,eAAe,IAAI,iBAAiB,EAAE,CAAC;YAChD,IAAI,OAAO,eAAe,KAAK,QAAQ,EAAE,CAAC;gBACxC,MAAM,KAAK,CAAC,UAAU,CAAC,eAAe,EAAE,mBAAsC,EAAE,OAAO,CAAC,CAAC;YAC3F,CAAC;iBAAM,CAAC;gBACN,MAAM,KAAK,CAAC,UAAU,CAAC,eAAe,EAAE,mBAAwC,CAAC,CAAC;YACpF,CAAC;QACH,CAAC;QACD,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;IACjC,CAAC;IAkDM,KAAK,CAAC,kBAAkB,CAC7B,iBAA0C,EAC1C,gBAIc,EACd,aAA+C;IAC/C,mFAAmF;IACnF,gEAAgE;IAChE,OAA4B;QAE5B,MAAM,KAAK,GAAG,IAAI,SAAS,EAAE,CAAC;QAC9B,KAAK,MAAM,eAAe,IAAI,iBAAiB,EAAE,CAAC;YAChD,IAAI,OAAO,eAAe,KAAK,QAAQ,EAAE,CAAC;gBACxC,MAAM,KAAK,CAAC,iBAAiB,CAC3B,eAAe,EACf,gBAAmC,EACnC,aAA2B,EAC3B,OAAO,CACR,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,MAAM,KAAK,CAAC,iBAAiB,CAC3B,eAAe,EACf,gBAA8B,EAC9B,aAAmC,CACpC,CAAC;YACJ,CAAC;QACH,CAAC;QACD,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;IACjC,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAkCG;IACI,KAAK,CAAC,WAAW,CACtB,YAAuB,EACvB,UAA8C,EAAE;QAEhD,IAAI,CAAC,YAAY,IAAI,YAAY,CAAC,cAAc,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;YAC9D,MAAM,IAAI,UAAU,CAAC,wDAAwD,CAAC,CAAC;QACjF,CAAC;QAED,OAAO,aAAa,CAAC,QAAQ,CAC3B,6BAA6B,EAC7B,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,MAAM,gBAAgB,GAAG,YAAY,CAAC,kBAAkB,EAAE,CAAC;YAE3D,2FAA2F;YAC3F,MAAM,gBAAgB,GAAoC,cAAc,CACtE,MAAM,IAAI,CAAC,yBAAyB,CAAC,WAAW,CAC9C,cAAc,CAAC,gBAAgB,CAAC,EAChC,YAAY,CAAC,uBAAuB,EAAE,EACtC,gBAAgB,oBAEX,cAAc,EAEpB,CACF,CAAC;YAEF,gHAAgH;YAChH,MAAM,mBAAmB,GAAG,IAAI,mBAAmB,CACjD,gBAAgB,EAChB,YAAY,CAAC,cAAc,EAAE,CAC9B,CAAC;YACF,MAAM,eAAe,GAAG,MAAM,mBAAmB,CAAC,kBAAkB,EAAE,CAAC;YAEvE,MAAM,GAAG,GAAiC;gBACxC,SAAS,EAAE,gBAAgB,CAAC,SAAS;gBACrC,WAAW,EAAE,gBAAgB,CAAC,WAAW;gBACzC,SAAS,EAAE,gBAAgB,CAAC,SAAS;gBACrC,SAAS,EAAE,gBAAgB,CAAC,SAAS;gBACrC,eAAe,EAAE,gBAAgB,CAAC,eAAe;gBACjD,OAAO,EAAE,gBAAgB,CAAC,OAAO;gBACjC,YAAY,EAAE,eAAe,CAAC,YAAY;gBAC1C,0BAA0B,EAAE,eAAe,CAAC,0BAA0B;gBACtE,uBAAuB,EAAE,eAAe,CAAC,uBAAuB;aACjE,CAAC;YAEF,OAAO,GAAG,CAAC;QACb,CAAC,CACF,CAAC;IACJ,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type {\n  AccessTier,\n  ServiceSubmitBatchHeaders,\n  ServiceSubmitBatchOptionalParamsModel,\n  ServiceSubmitBatchResponseModel,\n} from \"./generatedModels\";\nimport type { ParsedBatchResponse } from \"./BatchResponse\";\nimport { BatchResponseParser } from \"./BatchResponseParser\";\nimport { utf8ByteLength } from \"./BatchUtils\";\nimport { BlobBatch } from \"./BlobBatch\";\nimport { tracingClient } from \"./utils/tracing\";\nimport type { TokenCredential } from \"@azure/core-auth\";\nimport type { Service, Container } from \"./generated/src/operationsInterfaces\";\nimport type { StorageSharedKeyCredential } from \"./credentials/StorageSharedKeyCredential\";\nimport { AnonymousCredential } from \"./credentials/AnonymousCredential\";\nimport type { BlobDeleteOptions, BlobClient, BlobSetTierOptions } from \"./Clients\";\nimport { StorageContextClient } from \"./StorageContextClient\";\nimport type { PipelineLike, StoragePipelineOptions } from \"./Pipeline\";\nimport { newPipeline, isPipelineLike, getCoreClientOptions } from \"./Pipeline\";\nimport type { WithResponse } from \"./utils/utils.common\";\nimport { assertResponse, getURLPath } from \"./utils/utils.common\";\n\n/**\n * Options to configure the Service - Submit Batch Optional Params.\n */\nexport interface BlobBatchSubmitBatchOptionalParams extends ServiceSubmitBatchOptionalParamsModel {}\n\n/**\n * Contains response data for blob batch operations.\n */\nexport declare type BlobBatchSubmitBatchResponse = WithResponse<\n  ParsedBatchResponse & ServiceSubmitBatchHeaders,\n  ServiceSubmitBatchHeaders\n>;\n\n/**\n * Contains response data for the {@link deleteBlobs} operation.\n */\nexport declare type BlobBatchDeleteBlobsResponse = BlobBatchSubmitBatchResponse;\n\n/**\n * Contains response data for the {@link setBlobsAccessTier} operation.\n */\nexport declare type BlobBatchSetBlobsAccessTierResponse = BlobBatchSubmitBatchResponse;\n\n/**\n * A BlobBatchClient allows you to make batched requests to the Azure Storage Blob service.\n *\n * @see https://docs.microsoft.com/en-us/rest/api/storageservices/blob-batch\n */\nexport class BlobBatchClient {\n  private serviceOrContainerContext: Service | Container;\n\n  /**\n   * Creates an instance of BlobBatchClient.\n   *\n   * @param url - A url pointing to Azure Storage blob service, such as\n   *                     \"https://myaccount.blob.core.windows.net\". You can append a SAS\n   *                     if using AnonymousCredential, such as \"https://myaccount.blob.core.windows.net?sasString\".\n   * @param credential -  Such as AnonymousCredential, StorageSharedKeyCredential or any credential from the `@azure/identity` package to authenticate requests to the service. You can also provide an object that implements the TokenCredential interface. If not specified, AnonymousCredential is used.\n   * @param options - Options to configure the HTTP pipeline.\n   */\n  constructor(\n    url: string,\n    credential?: StorageSharedKeyCredential | AnonymousCredential | TokenCredential,\n    // Legacy, no fix for eslint error without breaking. Disable it for this interface.\n    /* eslint-disable-next-line @azure/azure-sdk/ts-naming-options*/\n    options?: StoragePipelineOptions,\n  );\n\n  /**\n   * Creates an instance of BlobBatchClient.\n   *\n   * @param url - A url pointing to Azure Storage blob service, such as\n   *                     \"https://myaccount.blob.core.windows.net\". You can append a SAS\n   *                     if using AnonymousCredential, such as \"https://myaccount.blob.core.windows.net?sasString\".\n   * @param pipeline - Call newPipeline() to create a default\n   *                            pipeline, or provide a customized pipeline.\n   */\n  constructor(url: string, pipeline: PipelineLike);\n  constructor(\n    url: string,\n    credentialOrPipeline?:\n      | StorageSharedKeyCredential\n      | AnonymousCredential\n      | TokenCredential\n      | PipelineLike,\n    // Legacy, no fix for eslint error without breaking. Disable it for this interface.\n    /* eslint-disable-next-line @azure/azure-sdk/ts-naming-options*/\n    options?: StoragePipelineOptions,\n  ) {\n    let pipeline: PipelineLike;\n    if (isPipelineLike(credentialOrPipeline)) {\n      pipeline = credentialOrPipeline;\n    } else if (!credentialOrPipeline) {\n      // no credential provided\n      pipeline = newPipeline(new AnonymousCredential(), options);\n    } else {\n      pipeline = newPipeline(credentialOrPipeline, options);\n    }\n\n    const storageClientContext = new StorageContextClient(url, getCoreClientOptions(pipeline));\n\n    const path = getURLPath(url);\n    if (path && path !== \"/\") {\n      // Container scoped.\n      this.serviceOrContainerContext = storageClientContext.container;\n    } else {\n      this.serviceOrContainerContext = storageClientContext.service;\n    }\n  }\n\n  /**\n   * Creates a {@link BlobBatch}.\n   * A BlobBatch represents an aggregated set of operations on blobs.\n   */\n  public createBatch(): BlobBatch {\n    return new BlobBatch();\n  }\n\n  /**\n   * Create multiple delete operations to mark the specified blobs or snapshots for deletion.\n   * Note that in order to delete a blob, you must delete all of its snapshots.\n   * You can delete both at the same time. See [delete operation details](https://docs.microsoft.com/en-us/rest/api/storageservices/delete-blob).\n   * The operations will be authenticated and authorized with specified credential.\n   * See [blob batch authorization details](https://docs.microsoft.com/en-us/rest/api/storageservices/blob-batch#authorization).\n   *\n   * @param urls - The urls of the blob resources to delete.\n   * @param credential -  Such as AnonymousCredential, StorageSharedKeyCredential or any credential from the `@azure/identity` package to authenticate requests to the service. You can also provide an object that implements the TokenCredential interface. If not specified, AnonymousCredential is used.\n   * @param options -\n   */\n  public async deleteBlobs(\n    urls: string[],\n    credential: StorageSharedKeyCredential | AnonymousCredential | TokenCredential,\n    // Legacy, no fix for eslint error without breaking. Disable it for this interface.\n    /* eslint-disable-next-line @azure/azure-sdk/ts-naming-options*/\n    options?: BlobDeleteOptions,\n  ): Promise<BlobBatchDeleteBlobsResponse>;\n\n  /**\n   * Create multiple delete operations to mark the specified blobs or snapshots for deletion.\n   * Note that in order to delete a blob, you must delete all of its snapshots.\n   * You can delete both at the same time. See [delete operation details](https://docs.microsoft.com/en-us/rest/api/storageservices/delete-blob).\n   * The operation(subrequest) will be authenticated and authorized with specified credential.\n   * See [blob batch authorization details](https://docs.microsoft.com/en-us/rest/api/storageservices/blob-batch#authorization).\n   *\n   * @param blobClients - The BlobClients for the blobs to delete.\n   * @param options -\n   */\n  public async deleteBlobs(\n    blobClients: BlobClient[],\n    // Legacy, no fix for eslint error without breaking. Disable it for this interface.\n    /* eslint-disable-next-line @azure/azure-sdk/ts-naming-options*/\n    options?: BlobDeleteOptions,\n  ): Promise<BlobBatchDeleteBlobsResponse>;\n\n  public async deleteBlobs(\n    urlsOrBlobClients: string[] | BlobClient[],\n    credentialOrOptions:\n      | StorageSharedKeyCredential\n      | AnonymousCredential\n      | TokenCredential\n      | BlobDeleteOptions\n      | undefined,\n    // Legacy, no fix for eslint error without breaking. Disable it for this interface.\n    /* eslint-disable-next-line @azure/azure-sdk/ts-naming-options*/\n    options?: BlobDeleteOptions,\n  ): Promise<BlobBatchDeleteBlobsResponse> {\n    const batch = new BlobBatch();\n    for (const urlOrBlobClient of urlsOrBlobClients) {\n      if (typeof urlOrBlobClient === \"string\") {\n        await batch.deleteBlob(urlOrBlobClient, credentialOrOptions as TokenCredential, options);\n      } else {\n        await batch.deleteBlob(urlOrBlobClient, credentialOrOptions as BlobDeleteOptions);\n      }\n    }\n    return this.submitBatch(batch);\n  }\n\n  /**\n   * Create multiple set tier operations to set the tier on a blob.\n   * The operation is allowed on a page blob in a premium\n   * storage account and on a block blob in a blob storage account (locally redundant\n   * storage only). A premium page blob's tier determines the allowed size, IOPS,\n   * and bandwidth of the blob. A block blob's tier determines Hot/Cool/Archive\n   * storage type. This operation does not update the blob's ETag.\n   * See [set blob tier details](https://docs.microsoft.com/en-us/rest/api/storageservices/set-blob-tier).\n   * The operation(subrequest) will be authenticated and authorized\n   * with specified credential.See [blob batch authorization details](https://docs.microsoft.com/en-us/rest/api/storageservices/blob-batch#authorization).\n   *\n   * @param urls - The urls of the blob resource to delete.\n   * @param credential -  Such as AnonymousCredential, StorageSharedKeyCredential or any credential from the `@azure/identity` package to authenticate requests to the service. You can also provide an object that implements the TokenCredential interface. If not specified, AnonymousCredential is used.\n   * @param tier -\n   * @param options -\n   */\n  public async setBlobsAccessTier(\n    urls: string[],\n    credential: StorageSharedKeyCredential | AnonymousCredential | TokenCredential,\n    tier: AccessTier,\n    // Legacy, no fix for eslint error without breaking. Disable it for this interface.\n    /* eslint-disable-next-line @azure/azure-sdk/ts-naming-options*/\n    options?: BlobSetTierOptions,\n  ): Promise<BlobBatchSetBlobsAccessTierResponse>;\n\n  /**\n   * Create multiple set tier operations to set the tier on a blob.\n   * The operation is allowed on a page blob in a premium\n   * storage account and on a block blob in a blob storage account (locally redundant\n   * storage only). A premium page blob's tier determines the allowed size, IOPS,\n   * and bandwidth of the blob. A block blob's tier determines Hot/Cool/Archive\n   * storage type. This operation does not update the blob's ETag.\n   * See [set blob tier details](https://docs.microsoft.com/en-us/rest/api/storageservices/set-blob-tier).\n   * The operation(subrequest) will be authenticated and authorized\n   * with specified credential.See [blob batch authorization details](https://docs.microsoft.com/en-us/rest/api/storageservices/blob-batch#authorization).\n   *\n   * @param blobClients - The BlobClients for the blobs which should have a new tier set.\n   * @param tier -\n   * @param options -\n   */\n  public async setBlobsAccessTier(\n    blobClients: BlobClient[],\n    tier: AccessTier,\n    // Legacy, no fix for eslint error without breaking. Disable it for this interface.\n    /* eslint-disable-next-line @azure/azure-sdk/ts-naming-options*/\n    options?: BlobSetTierOptions,\n  ): Promise<BlobBatchSetBlobsAccessTierResponse>;\n\n  public async setBlobsAccessTier(\n    urlsOrBlobClients: string[] | BlobClient[],\n    credentialOrTier:\n      | StorageSharedKeyCredential\n      | AnonymousCredential\n      | TokenCredential\n      | AccessTier,\n    tierOrOptions?: AccessTier | BlobSetTierOptions,\n    // Legacy, no fix for eslint error without breaking. Disable it for this interface.\n    /* eslint-disable-next-line @azure/azure-sdk/ts-naming-options*/\n    options?: BlobSetTierOptions,\n  ): Promise<BlobBatchSetBlobsAccessTierResponse> {\n    const batch = new BlobBatch();\n    for (const urlOrBlobClient of urlsOrBlobClients) {\n      if (typeof urlOrBlobClient === \"string\") {\n        await batch.setBlobAccessTier(\n          urlOrBlobClient,\n          credentialOrTier as TokenCredential,\n          tierOrOptions as AccessTier,\n          options,\n        );\n      } else {\n        await batch.setBlobAccessTier(\n          urlOrBlobClient,\n          credentialOrTier as AccessTier,\n          tierOrOptions as BlobSetTierOptions,\n        );\n      }\n    }\n    return this.submitBatch(batch);\n  }\n\n  /**\n   * Submit batch request which consists of multiple subrequests.\n   *\n   * Get `blobBatchClient` and other details before running the snippets.\n   * `blobServiceClient.getBlobBatchClient()` gives the `blobBatchClient`\n   *\n   * Example usage:\n   *\n   * ```js\n   * let batchRequest = new BlobBatch();\n   * await batchRequest.deleteBlob(urlInString0, credential0);\n   * await batchRequest.deleteBlob(urlInString1, credential1, {\n   *  deleteSnapshots: \"include\"\n   * });\n   * const batchResp = await blobBatchClient.submitBatch(batchRequest);\n   * console.log(batchResp.subResponsesSucceededCount);\n   * ```\n   *\n   * Example using a lease:\n   *\n   * ```js\n   * let batchRequest = new BlobBatch();\n   * await batchRequest.setBlobAccessTier(blockBlobClient0, \"Cool\");\n   * await batchRequest.setBlobAccessTier(blockBlobClient1, \"Cool\", {\n   *  conditions: { leaseId: leaseId }\n   * });\n   * const batchResp = await blobBatchClient.submitBatch(batchRequest);\n   * console.log(batchResp.subResponsesSucceededCount);\n   * ```\n   *\n   * @see https://docs.microsoft.com/en-us/rest/api/storageservices/blob-batch\n   *\n   * @param batchRequest - A set of Delete or SetTier operations.\n   * @param options -\n   */\n  public async submitBatch(\n    batchRequest: BlobBatch,\n    options: BlobBatchSubmitBatchOptionalParams = {},\n  ): Promise<BlobBatchSubmitBatchResponse> {\n    if (!batchRequest || batchRequest.getSubRequests().size === 0) {\n      throw new RangeError(\"Batch request should contain one or more sub requests.\");\n    }\n\n    return tracingClient.withSpan(\n      \"BlobBatchClient-submitBatch\",\n      options,\n      async (updatedOptions) => {\n        const batchRequestBody = batchRequest.getHttpRequestBody();\n\n        // ServiceSubmitBatchResponseModel and ContainerSubmitBatchResponse are compatible for now.\n        const rawBatchResponse: ServiceSubmitBatchResponseModel = assertResponse(\n          await this.serviceOrContainerContext.submitBatch(\n            utf8ByteLength(batchRequestBody),\n            batchRequest.getMultiPartContentType(),\n            batchRequestBody,\n            {\n              ...updatedOptions,\n            },\n          ),\n        );\n\n        // Parse the sub responses result, if logic reaches here(i.e. the batch request succeeded with status code 202).\n        const batchResponseParser = new BatchResponseParser(\n          rawBatchResponse,\n          batchRequest.getSubRequests(),\n        );\n        const responseSummary = await batchResponseParser.parseBatchResponse();\n\n        const res: BlobBatchSubmitBatchResponse = {\n          _response: rawBatchResponse._response,\n          contentType: rawBatchResponse.contentType,\n          errorCode: rawBatchResponse.errorCode,\n          requestId: rawBatchResponse.requestId,\n          clientRequestId: rawBatchResponse.clientRequestId,\n          version: rawBatchResponse.version,\n          subResponses: responseSummary.subResponses,\n          subResponsesSucceededCount: responseSummary.subResponsesSucceededCount,\n          subResponsesFailedCount: responseSummary.subResponsesFailedCount,\n        };\n\n        return res;\n      },\n    );\n  }\n}\n"]}
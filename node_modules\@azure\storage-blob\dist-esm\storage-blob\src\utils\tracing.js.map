{"version": 3, "file": "tracing.js", "sourceRoot": "", "sources": ["../../../../src/utils/tracing.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,mBAAmB,EAAE,MAAM,qBAAqB,CAAC;AAC1D,OAAO,EAAE,WAAW,EAAE,MAAM,aAAa,CAAC;AAE1C;;;GAGG;AACH,MAAM,CAAC,MAAM,aAAa,GAAG,mBAAmB,CAAC;IAC/C,WAAW,EAAE,qBAAqB;IAClC,cAAc,EAAE,WAAW;IAC3B,SAAS,EAAE,mBAAmB;CAC/B,CAAC,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport { createTracingClient } from \"@azure/core-tracing\";\nimport { SDK_VERSION } from \"./constants\";\n\n/**\n * Creates a span using the global tracer.\n * @internal\n */\nexport const tracingClient = createTracingClient({\n  packageName: \"@azure/storage-blob\",\n  packageVersion: SDK_VERSION,\n  namespace: \"Microsoft.Storage\",\n});\n"]}
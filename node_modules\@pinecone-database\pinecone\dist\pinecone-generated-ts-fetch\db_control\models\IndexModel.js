"use strict";
/* tslint:disable */
/* eslint-disable */
/**
 * Pinecone Control Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.IndexModelToJSON = exports.IndexModelFromJSONTyped = exports.IndexModelFromJSON = exports.instanceOfIndexModel = exports.IndexModelMetricEnum = void 0;
var runtime_1 = require("../runtime");
var DeletionProtection_1 = require("./DeletionProtection");
var IndexModelSpec_1 = require("./IndexModelSpec");
var IndexModelStatus_1 = require("./IndexModelStatus");
/**
 * @export
 */
exports.IndexModelMetricEnum = {
    Cosine: 'cosine',
    Euclidean: 'euclidean',
    Dotproduct: 'dotproduct'
};
/**
 * Check if a given object implements the IndexModel interface.
 */
function instanceOfIndexModel(value) {
    var isInstance = true;
    isInstance = isInstance && "name" in value;
    isInstance = isInstance && "dimension" in value;
    isInstance = isInstance && "metric" in value;
    isInstance = isInstance && "host" in value;
    isInstance = isInstance && "spec" in value;
    isInstance = isInstance && "status" in value;
    return isInstance;
}
exports.instanceOfIndexModel = instanceOfIndexModel;
function IndexModelFromJSON(json) {
    return IndexModelFromJSONTyped(json, false);
}
exports.IndexModelFromJSON = IndexModelFromJSON;
function IndexModelFromJSONTyped(json, ignoreDiscriminator) {
    if ((json === undefined) || (json === null)) {
        return json;
    }
    return {
        'name': json['name'],
        'dimension': json['dimension'],
        'metric': json['metric'],
        'host': json['host'],
        'deletionProtection': !(0, runtime_1.exists)(json, 'deletion_protection') ? undefined : (0, DeletionProtection_1.DeletionProtectionFromJSON)(json['deletion_protection']),
        'tags': !(0, runtime_1.exists)(json, 'tags') ? undefined : json['tags'],
        'spec': (0, IndexModelSpec_1.IndexModelSpecFromJSON)(json['spec']),
        'status': (0, IndexModelStatus_1.IndexModelStatusFromJSON)(json['status']),
    };
}
exports.IndexModelFromJSONTyped = IndexModelFromJSONTyped;
function IndexModelToJSON(value) {
    if (value === undefined) {
        return undefined;
    }
    if (value === null) {
        return null;
    }
    return {
        'name': value.name,
        'dimension': value.dimension,
        'metric': value.metric,
        'host': value.host,
        'deletion_protection': (0, DeletionProtection_1.DeletionProtectionToJSON)(value.deletionProtection),
        'tags': value.tags,
        'spec': (0, IndexModelSpec_1.IndexModelSpecToJSON)(value.spec),
        'status': (0, IndexModelStatus_1.IndexModelStatusToJSON)(value.status),
    };
}
exports.IndexModelToJSON = IndexModelToJSON;
//# sourceMappingURL=IndexModel.js.map
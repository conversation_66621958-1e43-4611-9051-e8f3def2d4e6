{"version": 3, "file": "createIndex.test.js", "sourceRoot": "", "sources": ["../../../src/integration/control/createIndex.test.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,qCAAuC;AACvC,gDAAkD;AAElD,IAAI,QAAkB,CAAC;AAEvB,SAAS,CAAC;;QACR,QAAQ,GAAG,IAAI,gBAAQ,EAAE,CAAC;;;KAC3B,CAAC,CAAC;AAEH,QAAQ,CAAC,cAAc,EAAE;IACvB,QAAQ,CAAC,YAAY,EAAE;QACrB,IAAI,CAAC,eAAe,EAAE;;;;;wBACd,SAAS,GAAG,IAAA,8BAAe,EAAC,mBAAmB,CAAC,CAAC;wBACvD,qBAAM,QAAQ,CAAC,WAAW,CAAC;gCACzB,IAAI,EAAE,SAAS;gCACf,SAAS,EAAE,CAAC;gCACZ,IAAI,EAAE;oCACJ,UAAU,EAAE;wCACV,KAAK,EAAE,KAAK;wCACZ,MAAM,EAAE,WAAW;qCACpB;iCACF;gCACD,cAAc,EAAE,IAAI;6BACrB,CAAC,EAAA;;wBAVF,SAUE,CAAC;wBACiB,qBAAM,QAAQ,CAAC,aAAa,CAAC,SAAS,CAAC,EAAA;;wBAArD,WAAW,GAAG,SAAuC;wBAC3D,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;wBAC5C,MAAM,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;wBACzC,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;wBAC7C,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;wBAEvC,qBAAM,QAAQ,CAAC,WAAW,CAAC,SAAS,CAAC,EAAA;;wBAArC,SAAqC,CAAC;;;;aACvC,CAAC,CAAC;QAEH,IAAI,CAAC,oBAAoB,EAAE;;;;;wBACnB,SAAS,GAAG,IAAA,8BAAe,EAAC,mBAAmB,CAAC,CAAC;wBAEvD,qBAAM,QAAQ,CAAC,WAAW,CAAC;gCACzB,IAAI,EAAE,SAAS;gCACf,SAAS,EAAE,CAAC;gCACZ,MAAM,EAAE,WAAW;gCACnB,IAAI,EAAE;oCACJ,UAAU,EAAE;wCACV,KAAK,EAAE,KAAK;wCACZ,MAAM,EAAE,WAAW;qCACpB;iCACF;gCACD,cAAc,EAAE,IAAI;6BACrB,CAAC,EAAA;;wBAXF,SAWE,CAAC;wBAEiB,qBAAM,QAAQ,CAAC,aAAa,CAAC,SAAS,CAAC,EAAA;;wBAArD,WAAW,GAAG,SAAuC;wBAC3D,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;wBAC5C,MAAM,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;wBACzC,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;wBAChD,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;wBAEvC,qBAAM,QAAQ,CAAC,WAAW,CAAC,SAAS,CAAC,EAAA;;wBAArC,SAAqC,CAAC;;;;aACvC,CAAC,CAAC;QAEH,uFAAuF;QACvF,IAAI,CAAC,IAAI,CAAC,0DAA0D,EAAE;;;gBAC9D,SAAS,GAAG,IAAA,8BAAe,EAAC,mBAAmB,CAAC,CAAC;gBAEvD,IAAI,CAAC,0CAA0C,EAAE;;;;;oCAC/C,qBAAM,QAAQ,CAAC,WAAW,CAAC;oCACzB,IAAI,EAAE,SAAS;oCACf,SAAS,EAAE,CAAC;oCACZ,MAAM,EAAE,QAAQ;oCAChB,IAAI,EAAE;wCACJ,UAAU,EAAE;4CACV,KAAK,EAAE,KAAK;4CACZ,MAAM,EAAE,WAAW;yCACpB;qCACF;oCACD,cAAc,EAAE,IAAI;iCACrB,CAAC,EAAA;;gCAXF,SAWE,CAAC;gCAEiB,qBAAM,QAAQ,CAAC,aAAa,CAAC,SAAS,CAAC,EAAA;;gCAArD,WAAW,GAAG,SAAuC;gCAC3D,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;gCAC5C,MAAM,CAAC,MAAA,WAAW,CAAC,MAAM,0CAAE,KAAK,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;;;;qBACpD,CAAC,CAAC;;;aACJ,CAAC,CAAC;QAEH,sEAAsE;QACtE,IAAI,CAAC,IAAI,CAAC,sFAAsF,EAAE;;gBAChG,IAAI,CAAC,6CAA6C,EAAE;;;;;gCAC5C,SAAS,GAAG,IAAA,8BAAe,EAAC,mBAAmB,CAAC,CAAC;gCAEvD,qBAAM,QAAQ,CAAC,WAAW,CAAC;wCACzB,IAAI,EAAE,SAAS;wCACf,SAAS,EAAE,CAAC;wCACZ,MAAM,EAAE,QAAQ;wCAChB,IAAI,EAAE;4CACJ,UAAU,EAAE;gDACV,KAAK,EAAE,KAAK;gDACZ,MAAM,EAAE,WAAW;6CACpB;yCACF;wCACD,iBAAiB,EAAE,IAAI;wCACvB,cAAc,EAAE,IAAI;qCACrB,CAAC,EAAA;;gCAZF,SAYE,CAAC;gCAEiB,qBAAM,QAAQ,CAAC,aAAa,CAAC,SAAS,CAAC,EAAA;;gCAArD,WAAW,GAAG,SAAuC;gCAC3D,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;;;;qBAC7C,CAAC,CAAC;;;aACJ,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,aAAa,EAAE;QACtB,IAAI,CAAC,sCAAsC,EAAE;;;;;;wBAEnC,SAAS,GAAG,IAAA,8BAAe,EAAC,mBAAmB,CAAC,CAAC;wBAEvD,qBAAM,QAAQ,CAAC,WAAW,CAAC;gCACzB,IAAI,EAAE,SAAS,GAAG,GAAG;gCACrB,SAAS,EAAE,CAAC;gCACZ,MAAM,EAAE,QAAQ;gCAChB,IAAI,EAAE;oCACJ,UAAU,EAAE;wCACV,KAAK,EAAE,KAAK;wCACZ,MAAM,EAAE,WAAW;qCACpB;iCACF;6BACF,CAAC,EAAA;;wBAVF,SAUE,CAAC;;;;wBAEG,GAAG,GAAG,GAA0B,CAAC;wBACvC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,yBAAyB,CAAC,CAAC;wBACpD,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC;;;;;aAE3D,CAAC,CAAC;QAEH,+CAA+C;QAC/C,IAAI,CAAC,IAAI,CAAC,+DAA+D,EAAE;;gBACzE,IAAI,CAAC,oBAAoB,EAAE;;;;;gCACnB,SAAS,GAAG,IAAA,8BAAe,EAAC,mBAAmB,CAAC,CAAC;;;;gCAGrD,qBAAM,QAAQ,CAAC,WAAW,CAAC;wCACzB,IAAI,EAAE,SAAS;wCACf,SAAS,EAAE,CAAC;wCACZ,MAAM,EAAE,QAAQ;wCAChB,IAAI,EAAE;4CACJ,UAAU,EAAE;gDACV,KAAK,EAAE,KAAK;gDACZ,MAAM,EAAE,WAAW;6CACpB;yCACF;qCACF,CAAC,EAAA;;gCAVF,SAUE,CAAC;;;;gCAEG,GAAG,GAAG,GAA0B,CAAC;gCACvC,iGAAiG;gCACjG,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,6BAA6B,CAAC,CAAC;gCACxD,oGAAoG;gCACpG,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,+BAA+B,CAAC,CAAC;;;;;qBAElE,CAAC,CAAC;;;aACJ,CAAC,CAAC;QAEH,IAAI,CAAC,qCAAqC,EAAE;;;;;wBACpC,SAAS,GAAG,IAAA,8BAAe,EAAC,mBAAmB,CAAC,CAAC;;;;wBAGrD,qBAAM,QAAQ,CAAC,WAAW,CAAC;gCACzB,IAAI,EAAE,SAAS;gCACf,SAAS,EAAE,CAAC;gCACZ,MAAM,EAAE,QAAQ;gCAChB,IAAI,EAAE;oCACJ,GAAG,EAAE;wCACH,WAAW,EAAE,eAAe;wCAC5B,OAAO,EAAE,OAAO;wCAChB,IAAI,EAAE,CAAC;wCACP,gBAAgB,EAAE,yBAAyB;qCAC5C;iCACF;6BACF,CAAC,EAAA;;wBAZF,SAYE,CAAC;;;;wBAEG,GAAG,GAAG,GAA0B,CAAC;wBACvC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,yBAAyB,CAAC,CAAC;wBACpD,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,SAAS,CAC3B,4CAA4C,CAC7C,CAAC;;;;;aAEL,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}
🤖 WORKING META AI AGENT - Creating functional workflow...
📝 Prompt: "Create"

🔍 Analyzing prompt...
📊 Detected: general - AI Assistant
🧠 Generating working n8n workflow...
🚀 Deploying workflow to n8n...
✅ Workflow created! ID: E2QP8hvz5v0okWdT
⚡ Activating workflow...
✅ Workflow activated!
🧪 Testing the created agent...

🎉 SUCCESS! Your working AI agent is ready!

📋 Agent Details:
   Name: AI Assistant - 2025-05-29
   Type: general
   ID: E2QP8hvz5v0okWdT
   Webhook: http://localhost:2410/webhook/ai-assistant

🧪 Test Result: ⚠️ Activating

🧪 Test your agent:
   curl -X POST http://localhost:2410/webhook/ai-assistant \
        -H "Content-Type: application/json" \
        -d '{"message": "Hello, test my agent!"}'

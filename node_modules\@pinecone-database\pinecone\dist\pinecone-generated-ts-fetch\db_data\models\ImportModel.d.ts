/**
 * Pinecone Data Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
/**
 * The model for an import operation.
 * @export
 * @interface ImportModel
 */
export interface ImportModel {
    /**
     * Unique identifier for the import operation.
     * @type {string}
     * @memberof ImportModel
     */
    id?: string;
    /**
     * The URI from where the data is imported.
     * @type {string}
     * @memberof ImportModel
     */
    uri?: string;
    /**
     * The status of the operation.
     * @type {string}
     * @memberof ImportModel
     */
    status?: ImportModelStatusEnum;
    /**
     * The start time of the import operation.
     * @type {Date}
     * @memberof ImportModel
     */
    createdAt?: Date;
    /**
     * The end time of the import operation.
     * @type {Date}
     * @memberof ImportModel
     */
    finishedAt?: Date;
    /**
     * The progress made by the operation out of 100
     * @type {number}
     * @memberof ImportModel
     */
    percentComplete?: number;
    /**
     * The number of records successfully imported.
     * @type {number}
     * @memberof ImportModel
     */
    recordsImported?: number;
    /**
     * The error message if the import process failed.
     * @type {string}
     * @memberof ImportModel
     */
    error?: string;
}
/**
 * @export
 */
export declare const ImportModelStatusEnum: {
    readonly Pending: "Pending";
    readonly InProgress: "InProgress";
    readonly Failed: "Failed";
    readonly Completed: "Completed";
    readonly Cancelled: "Cancelled";
};
export type ImportModelStatusEnum = typeof ImportModelStatusEnum[keyof typeof ImportModelStatusEnum];
/**
 * Check if a given object implements the ImportModel interface.
 */
export declare function instanceOfImportModel(value: object): boolean;
export declare function ImportModelFromJSON(json: any): ImportModel;
export declare function ImportModelFromJSONTyped(json: any, ignoreDiscriminator: boolean): ImportModel;
export declare function ImportModelToJSON(value?: ImportModel | null): any;

"use strict";
/* tslint:disable */
/* eslint-disable */
/**
 * Pinecone Control Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeletionProtectionToJSON = exports.DeletionProtectionFromJSONTyped = exports.DeletionProtectionFromJSON = exports.DeletionProtection = void 0;
/**
 * Whether [deletion protection](http://docs.pinecone.io/guides/indexes/prevent-index-deletion) is enabled/disabled for the index.
 * @export
 */
exports.DeletionProtection = {
    Disabled: 'disabled',
    Enabled: 'enabled'
};
function DeletionProtectionFromJSON(json) {
    return DeletionProtectionFromJSONTyped(json, false);
}
exports.DeletionProtectionFromJSON = DeletionProtectionFromJSON;
function DeletionProtectionFromJSONTyped(json, ignoreDiscriminator) {
    return json;
}
exports.DeletionProtectionFromJSONTyped = DeletionProtectionFromJSONTyped;
function DeletionProtectionToJSON(value) {
    return value;
}
exports.DeletionProtectionToJSON = DeletionProtectionToJSON;
//# sourceMappingURL=DeletionProtection.js.map
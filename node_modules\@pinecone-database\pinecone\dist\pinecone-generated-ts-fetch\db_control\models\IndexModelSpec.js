"use strict";
/* tslint:disable */
/* eslint-disable */
/**
 * Pinecone Control Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.IndexModelSpecToJSON = exports.IndexModelSpecFromJSONTyped = exports.IndexModelSpecFromJSON = exports.instanceOfIndexModelSpec = void 0;
var runtime_1 = require("../runtime");
var PodSpec_1 = require("./PodSpec");
var ServerlessSpec_1 = require("./ServerlessSpec");
/**
 * Check if a given object implements the IndexModelSpec interface.
 */
function instanceOfIndexModelSpec(value) {
    var isInstance = true;
    return isInstance;
}
exports.instanceOfIndexModelSpec = instanceOfIndexModelSpec;
function IndexModelSpecFromJSON(json) {
    return IndexModelSpecFromJSONTyped(json, false);
}
exports.IndexModelSpecFromJSON = IndexModelSpecFromJSON;
function IndexModelSpecFromJSONTyped(json, ignoreDiscriminator) {
    if ((json === undefined) || (json === null)) {
        return json;
    }
    return {
        'pod': !(0, runtime_1.exists)(json, 'pod') ? undefined : (0, PodSpec_1.PodSpecFromJSON)(json['pod']),
        'serverless': !(0, runtime_1.exists)(json, 'serverless') ? undefined : (0, ServerlessSpec_1.ServerlessSpecFromJSON)(json['serverless']),
    };
}
exports.IndexModelSpecFromJSONTyped = IndexModelSpecFromJSONTyped;
function IndexModelSpecToJSON(value) {
    if (value === undefined) {
        return undefined;
    }
    if (value === null) {
        return null;
    }
    return {
        'pod': (0, PodSpec_1.PodSpecToJSON)(value.pod),
        'serverless': (0, ServerlessSpec_1.ServerlessSpecToJSON)(value.serverless),
    };
}
exports.IndexModelSpecToJSON = IndexModelSpecToJSON;
//# sourceMappingURL=IndexModelSpec.js.map
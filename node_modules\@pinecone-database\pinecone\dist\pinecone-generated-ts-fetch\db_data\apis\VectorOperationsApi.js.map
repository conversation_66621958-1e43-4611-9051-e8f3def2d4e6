{"version": 3, "file": "VectorOperationsApi.js", "sourceRoot": "", "sources": ["../../../../src/pinecone-generated-ts-fetch/db_data/apis/VectorOperationsApi.ts"], "names": [], "mappings": ";AAAA,oBAAoB;AACpB,oBAAoB;AACpB;;;;;;;;;;GAUG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGH,kDAAsC;AActC,yCAuByB;AAkCzB;;GAEG;AACH;IAAyC,uCAAe;IAAxD;;IAiSA,CAAC;IA/RG;;;OAGG;IACG,8CAAgB,GAAtB,UAAuB,iBAAuC,EAAE,aAA0D;;;;;;wBACtH,IAAI,iBAAiB,CAAC,aAAa,KAAK,IAAI,IAAI,iBAAiB,CAAC,aAAa,KAAK,SAAS,EAAE;4BAC3F,MAAM,IAAI,OAAO,CAAC,aAAa,CAAC,eAAe,EAAC,sGAAsG,CAAC,CAAC;yBAC3J;wBAEK,eAAe,GAAQ,EAAE,CAAC;wBAE1B,gBAAgB,GAAwB,EAAE,CAAC;wBAEjD,gBAAgB,CAAC,cAAc,CAAC,GAAG,kBAAkB,CAAC;wBAEtD,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE;4BACjD,gBAAgB,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,4BAA4B;yBACnG;wBAEgB,qBAAM,IAAI,CAAC,OAAO,CAAC;gCAChC,IAAI,EAAE,iBAAiB;gCACvB,MAAM,EAAE,MAAM;gCACd,OAAO,EAAE,gBAAgB;gCACzB,KAAK,EAAE,eAAe;gCACtB,IAAI,EAAE,IAAA,2BAAmB,EAAC,iBAAiB,CAAC,aAAa,CAAC;6BAC7D,EAAE,aAAa,CAAC,EAAA;;wBANX,QAAQ,GAAG,SAMA;wBAEjB,sBAAO,IAAI,OAAO,CAAC,eAAe,CAAM,QAAQ,CAAC,EAAC;;;;KACrD;IAED;;;OAGG;IACG,2CAAa,GAAnB,UAAoB,iBAAuC,EAAE,aAA0D;;;;;4BAClG,qBAAM,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,EAAE,aAAa,CAAC,EAAA;;wBAAxE,QAAQ,GAAG,SAA6D;wBACvE,qBAAM,QAAQ,CAAC,KAAK,EAAE,EAAA;4BAA7B,sBAAO,SAAsB,EAAC;;;;KACjC;IAED;;;OAGG;IACG,mDAAqB,GAA3B,UAA4B,iBAAqD,EAAE,aAA0D;;;;;;wBACzI,IAAI,iBAAiB,CAAC,yBAAyB,KAAK,IAAI,IAAI,iBAAiB,CAAC,yBAAyB,KAAK,SAAS,EAAE;4BACnH,MAAM,IAAI,OAAO,CAAC,aAAa,CAAC,2BAA2B,EAAC,uHAAuH,CAAC,CAAC;yBACxL;wBAEK,eAAe,GAAQ,EAAE,CAAC;wBAE1B,gBAAgB,GAAwB,EAAE,CAAC;wBAEjD,gBAAgB,CAAC,cAAc,CAAC,GAAG,kBAAkB,CAAC;wBAEtD,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE;4BACjD,gBAAgB,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,4BAA4B;yBACnG;wBAEgB,qBAAM,IAAI,CAAC,OAAO,CAAC;gCAChC,IAAI,EAAE,uBAAuB;gCAC7B,MAAM,EAAE,MAAM;gCACd,OAAO,EAAE,gBAAgB;gCACzB,KAAK,EAAE,eAAe;gCACtB,IAAI,EAAE,IAAA,uCAA+B,EAAC,iBAAiB,CAAC,yBAAyB,CAAC;6BACrF,EAAE,aAAa,CAAC,EAAA;;wBANX,QAAQ,GAAG,SAMA;wBAEjB,sBAAO,IAAI,OAAO,CAAC,eAAe,CAAC,QAAQ,EAAE,UAAC,SAAS,IAAK,OAAA,IAAA,gCAAwB,EAAC,SAAS,CAAC,EAAnC,CAAmC,CAAC,EAAC;;;;KACpG;IAED;;;OAGG;IACG,gDAAkB,GAAxB,UAAyB,iBAAqD,EAAE,aAA0D;;;;;4BACrH,qBAAM,IAAI,CAAC,qBAAqB,CAAC,iBAAiB,EAAE,aAAa,CAAC,EAAA;;wBAA7E,QAAQ,GAAG,SAAkE;wBAC5E,qBAAM,QAAQ,CAAC,KAAK,EAAE,EAAA;4BAA7B,sBAAO,SAAsB,EAAC;;;;KACjC;IAED;;;OAGG;IACG,6CAAe,GAArB,UAAsB,iBAAsC,EAAE,aAA0D;;;;;;wBACpH,IAAI,iBAAiB,CAAC,GAAG,KAAK,IAAI,IAAI,iBAAiB,CAAC,GAAG,KAAK,SAAS,EAAE;4BACvE,MAAM,IAAI,OAAO,CAAC,aAAa,CAAC,KAAK,EAAC,2FAA2F,CAAC,CAAC;yBACtI;wBAEK,eAAe,GAAQ,EAAE,CAAC;wBAEhC,IAAI,iBAAiB,CAAC,GAAG,EAAE;4BACvB,eAAe,CAAC,KAAK,CAAC,GAAG,iBAAiB,CAAC,GAAG,CAAC;yBAClD;wBAED,IAAI,iBAAiB,CAAC,SAAS,KAAK,SAAS,EAAE;4BAC3C,eAAe,CAAC,WAAW,CAAC,GAAG,iBAAiB,CAAC,SAAS,CAAC;yBAC9D;wBAEK,gBAAgB,GAAwB,EAAE,CAAC;wBAEjD,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE;4BACjD,gBAAgB,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,4BAA4B;yBACnG;wBAEgB,qBAAM,IAAI,CAAC,OAAO,CAAC;gCAChC,IAAI,EAAE,gBAAgB;gCACtB,MAAM,EAAE,KAAK;gCACb,OAAO,EAAE,gBAAgB;gCACzB,KAAK,EAAE,eAAe;6BACzB,EAAE,aAAa,CAAC,EAAA;;wBALX,QAAQ,GAAG,SAKA;wBAEjB,sBAAO,IAAI,OAAO,CAAC,eAAe,CAAC,QAAQ,EAAE,UAAC,SAAS,IAAK,OAAA,IAAA,6BAAqB,EAAC,SAAS,CAAC,EAAhC,CAAgC,CAAC,EAAC;;;;KACjG;IAED;;;OAGG;IACG,0CAAY,GAAlB,UAAmB,iBAAsC,EAAE,aAA0D;;;;;4BAChG,qBAAM,IAAI,CAAC,eAAe,CAAC,iBAAiB,EAAE,aAAa,CAAC,EAAA;;wBAAvE,QAAQ,GAAG,SAA4D;wBACtE,qBAAM,QAAQ,CAAC,KAAK,EAAE,EAAA;4BAA7B,sBAAO,SAAsB,EAAC;;;;KACjC;IAED;;;OAGG;IACG,4CAAc,GAApB,UAAqB,iBAAqC,EAAE,aAA0D;;;;;;wBAC5G,eAAe,GAAQ,EAAE,CAAC;wBAEhC,IAAI,iBAAiB,CAAC,MAAM,KAAK,SAAS,EAAE;4BACxC,eAAe,CAAC,QAAQ,CAAC,GAAG,iBAAiB,CAAC,MAAM,CAAC;yBACxD;wBAED,IAAI,iBAAiB,CAAC,KAAK,KAAK,SAAS,EAAE;4BACvC,eAAe,CAAC,OAAO,CAAC,GAAG,iBAAiB,CAAC,KAAK,CAAC;yBACtD;wBAED,IAAI,iBAAiB,CAAC,eAAe,KAAK,SAAS,EAAE;4BACjD,eAAe,CAAC,iBAAiB,CAAC,GAAG,iBAAiB,CAAC,eAAe,CAAC;yBAC1E;wBAED,IAAI,iBAAiB,CAAC,SAAS,KAAK,SAAS,EAAE;4BAC3C,eAAe,CAAC,WAAW,CAAC,GAAG,iBAAiB,CAAC,SAAS,CAAC;yBAC9D;wBAEK,gBAAgB,GAAwB,EAAE,CAAC;wBAEjD,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE;4BACjD,gBAAgB,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,4BAA4B;yBACnG;wBAEgB,qBAAM,IAAI,CAAC,OAAO,CAAC;gCAChC,IAAI,EAAE,eAAe;gCACrB,MAAM,EAAE,KAAK;gCACb,OAAO,EAAE,gBAAgB;gCACzB,KAAK,EAAE,eAAe;6BACzB,EAAE,aAAa,CAAC,EAAA;;wBALX,QAAQ,GAAG,SAKA;wBAEjB,sBAAO,IAAI,OAAO,CAAC,eAAe,CAAC,QAAQ,EAAE,UAAC,SAAS,IAAK,OAAA,IAAA,4BAAoB,EAAC,SAAS,CAAC,EAA/B,CAA+B,CAAC,EAAC;;;;KAChG;IAED;;;OAGG;IACG,yCAAW,GAAjB,UAAkB,iBAA0C,EAAE,aAA0D;QAAtG,kCAAA,EAAA,sBAA0C;;;;;4BACvC,qBAAM,IAAI,CAAC,cAAc,CAAC,iBAAiB,EAAE,aAAa,CAAC,EAAA;;wBAAtE,QAAQ,GAAG,SAA2D;wBACrE,qBAAM,QAAQ,CAAC,KAAK,EAAE,EAAA;4BAA7B,sBAAO,SAAsB,EAAC;;;;KACjC;IAED;;;OAGG;IACG,6CAAe,GAArB,UAAsB,iBAAsC,EAAE,aAA0D;;;;;;wBACpH,IAAI,iBAAiB,CAAC,YAAY,KAAK,IAAI,IAAI,iBAAiB,CAAC,YAAY,KAAK,SAAS,EAAE;4BACzF,MAAM,IAAI,OAAO,CAAC,aAAa,CAAC,cAAc,EAAC,oGAAoG,CAAC,CAAC;yBACxJ;wBAEK,eAAe,GAAQ,EAAE,CAAC;wBAE1B,gBAAgB,GAAwB,EAAE,CAAC;wBAEjD,gBAAgB,CAAC,cAAc,CAAC,GAAG,kBAAkB,CAAC;wBAEtD,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE;4BACjD,gBAAgB,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,4BAA4B;yBACnG;wBAEgB,qBAAM,IAAI,CAAC,OAAO,CAAC;gCAChC,IAAI,EAAE,QAAQ;gCACd,MAAM,EAAE,MAAM;gCACd,OAAO,EAAE,gBAAgB;gCACzB,KAAK,EAAE,eAAe;gCACtB,IAAI,EAAE,IAAA,0BAAkB,EAAC,iBAAiB,CAAC,YAAY,CAAC;6BAC3D,EAAE,aAAa,CAAC,EAAA;;wBANX,QAAQ,GAAG,SAMA;wBAEjB,sBAAO,IAAI,OAAO,CAAC,eAAe,CAAC,QAAQ,EAAE,UAAC,SAAS,IAAK,OAAA,IAAA,6BAAqB,EAAC,SAAS,CAAC,EAAhC,CAAgC,CAAC,EAAC;;;;KACjG;IAED;;;OAGG;IACG,0CAAY,GAAlB,UAAmB,iBAAsC,EAAE,aAA0D;;;;;4BAChG,qBAAM,IAAI,CAAC,eAAe,CAAC,iBAAiB,EAAE,aAAa,CAAC,EAAA;;wBAAvE,QAAQ,GAAG,SAA4D;wBACtE,qBAAM,QAAQ,CAAC,KAAK,EAAE,EAAA;4BAA7B,sBAAO,SAAsB,EAAC;;;;KACjC;IAED;;;OAGG;IACG,6CAAe,GAArB,UAAsB,iBAAsC,EAAE,aAA0D;;;;;;wBACpH,IAAI,iBAAiB,CAAC,aAAa,KAAK,IAAI,IAAI,iBAAiB,CAAC,aAAa,KAAK,SAAS,EAAE;4BAC3F,MAAM,IAAI,OAAO,CAAC,aAAa,CAAC,eAAe,EAAC,qGAAqG,CAAC,CAAC;yBAC1J;wBAEK,eAAe,GAAQ,EAAE,CAAC;wBAE1B,gBAAgB,GAAwB,EAAE,CAAC;wBAEjD,gBAAgB,CAAC,cAAc,CAAC,GAAG,kBAAkB,CAAC;wBAEtD,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE;4BACjD,gBAAgB,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,4BAA4B;yBACnG;wBAEgB,qBAAM,IAAI,CAAC,OAAO,CAAC;gCAChC,IAAI,EAAE,iBAAiB;gCACvB,MAAM,EAAE,MAAM;gCACd,OAAO,EAAE,gBAAgB;gCACzB,KAAK,EAAE,eAAe;gCACtB,IAAI,EAAE,IAAA,2BAAmB,EAAC,iBAAiB,CAAC,aAAa,CAAC;6BAC7D,EAAE,aAAa,CAAC,EAAA;;wBANX,QAAQ,GAAG,SAMA;wBAEjB,sBAAO,IAAI,OAAO,CAAC,eAAe,CAAM,QAAQ,CAAC,EAAC;;;;KACrD;IAED;;;OAGG;IACG,0CAAY,GAAlB,UAAmB,iBAAsC,EAAE,aAA0D;;;;;4BAChG,qBAAM,IAAI,CAAC,eAAe,CAAC,iBAAiB,EAAE,aAAa,CAAC,EAAA;;wBAAvE,QAAQ,GAAG,SAA4D;wBACtE,qBAAM,QAAQ,CAAC,KAAK,EAAE,EAAA;4BAA7B,sBAAO,SAAsB,EAAC;;;;KACjC;IAED;;;OAGG;IACG,8CAAgB,GAAtB,UAAuB,iBAAuC,EAAE,aAA0D;;;;;;wBACtH,IAAI,iBAAiB,CAAC,aAAa,KAAK,IAAI,IAAI,iBAAiB,CAAC,aAAa,KAAK,SAAS,EAAE;4BAC3F,MAAM,IAAI,OAAO,CAAC,aAAa,CAAC,eAAe,EAAC,sGAAsG,CAAC,CAAC;yBAC3J;wBAEK,eAAe,GAAQ,EAAE,CAAC;wBAE1B,gBAAgB,GAAwB,EAAE,CAAC;wBAEjD,gBAAgB,CAAC,cAAc,CAAC,GAAG,kBAAkB,CAAC;wBAEtD,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE;4BACjD,gBAAgB,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,4BAA4B;yBACnG;wBAEgB,qBAAM,IAAI,CAAC,OAAO,CAAC;gCAChC,IAAI,EAAE,iBAAiB;gCACvB,MAAM,EAAE,MAAM;gCACd,OAAO,EAAE,gBAAgB;gCACzB,KAAK,EAAE,eAAe;gCACtB,IAAI,EAAE,IAAA,2BAAmB,EAAC,iBAAiB,CAAC,aAAa,CAAC;6BAC7D,EAAE,aAAa,CAAC,EAAA;;wBANX,QAAQ,GAAG,SAMA;wBAEjB,sBAAO,IAAI,OAAO,CAAC,eAAe,CAAC,QAAQ,EAAE,UAAC,SAAS,IAAK,OAAA,IAAA,8BAAsB,EAAC,SAAS,CAAC,EAAjC,CAAiC,CAAC,EAAC;;;;KAClG;IAED;;;OAGG;IACG,2CAAa,GAAnB,UAAoB,iBAAuC,EAAE,aAA0D;;;;;4BAClG,qBAAM,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,EAAE,aAAa,CAAC,EAAA;;wBAAxE,QAAQ,GAAG,SAA6D;wBACvE,qBAAM,QAAQ,CAAC,KAAK,EAAE,EAAA;4BAA7B,sBAAO,SAAsB,EAAC;;;;KACjC;IAEL,0BAAC;AAAD,CAAC,AAjSD,CAAyC,OAAO,CAAC,OAAO,GAiSvD;AAjSY,kDAAmB"}
{"name": "Meta AI Agent Builder", "nodes": [{"parameters": {"httpMethod": "POST", "path": "build-agent", "options": {}}, "id": "webhook-trigger", "name": "Webhook - Receive Instructions", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "build-agent"}, {"parameters": {"model": "gpt-4", "messages": {"messageType": "multipleMessages", "messages": [{"role": "system", "message": "You are an expert n8n workflow builder AI. Your job is to create complete, functional n8n workflows based on user instructions. Always respond with valid JSON workflow format that can be imported into n8n."}, {"role": "user", "message": "Create an n8n workflow for: {{ $json.instruction }}\n\nRequirements:\n1. Include all necessary nodes\n2. Proper connections between nodes\n3. Realistic node configurations\n4. Include error handling\n5. Make it production-ready\n\nReturn ONLY the n8n workflow JSON, no explanations."}]}}, "id": "ai-workflow-generator", "name": "AI Workflow Generator", "type": "n8n-nodes-base.openAi", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"jsCode": "// Parse the AI response and prepare workflow data\nconst aiResponse = $input.first().json.message.content;\n\ntry {\n  // Clean the response (remove markdown formatting if present)\n  let cleanJson = aiResponse.replace(/```json\\n?/g, '').replace(/```\\n?/g, '').trim();\n  \n  // Parse the workflow JSON\n  const workflowData = JSON.parse(cleanJson);\n  \n  // Add metadata\n  workflowData.active = false; // Start inactive for safety\n  workflowData.tags = ['ai-generated', 'meta-agent'];\n  workflowData.settings = {\n    timezone: 'UTC',\n    saveManualExecutions: true\n  };\n  \n  return {\n    json: {\n      workflow: workflowData,\n      originalInstruction: $('Webhook - Receive Instructions').first().json.instruction,\n      status: 'prepared'\n    }\n  };\n} catch (error) {\n  return {\n    json: {\n      error: 'Failed to parse AI response',\n      details: error.message,\n      aiResponse: aiResponse\n    }\n  };\n}"}, "id": "workflow-parser", "name": "Parse & Prepare Workflow", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [680, 300]}, {"parameters": {"url": "http://localhost:2410/api/v1/workflows", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "name", "value": "={{ $json.workflow.name || 'AI Generated Workflow' }}"}, {"name": "nodes", "value": "={{ JSON.stringify($json.workflow.nodes) }}"}, {"name": "connections", "value": "={{ JSON.stringify($json.workflow.connections) }}"}, {"name": "active", "value": "false"}, {"name": "tags", "value": "={{ JSON.stringify($json.workflow.tags) }}"}]}}, "id": "create-workflow", "name": "Create Workflow in n8n", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [900, 300]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "success-condition", "leftValue": "={{ $json.id }}", "rightValue": "", "operator": {"type": "exists"}}], "combinator": "and"}}, "id": "check-creation-success", "name": "Check Creation Success", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [1120, 300]}, {"parameters": {"respondWith": "json", "responseBody": "={{ {\n  \"success\": true,\n  \"message\": \"AI Agent workflow created successfully!\",\n  \"workflowId\": $json.id,\n  \"workflowName\": $json.name,\n  \"instruction\": $('Webhook - Receive Instructions').first().json.instruction,\n  \"webhookUrl\": \"http://localhost:2410/webhook/\" + $json.id,\n  \"nextSteps\": [\n    \"Go to your n8n workflows tab\",\n    \"Find the new workflow: \" + $json.name,\n    \"Configure any required credentials\",\n    \"Activate the workflow when ready\"\n  ]\n} }}"}, "id": "success-response", "name": "Success Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1340, 200]}, {"parameters": {"respondWith": "json", "responseBody": "={{ {\n  \"success\": false,\n  \"message\": \"Failed to create AI Agent workflow\",\n  \"error\": $json.error || \"Unknown error occurred\",\n  \"instruction\": $('Webhook - Receive Instructions').first().json.instruction,\n  \"suggestion\": \"Please try rephrasing your instruction or check the logs for more details\"\n} }}"}, "id": "error-response", "name": "Error Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1340, 400]}], "connections": {"Webhook - Receive Instructions": {"main": [[{"node": "AI Workflow Generator", "type": "main", "index": 0}]]}, "AI Workflow Generator": {"main": [[{"node": "Parse & Prepare Workflow", "type": "main", "index": 0}]]}, "Parse & Prepare Workflow": {"main": [[{"node": "Create Workflow in n8n", "type": "main", "index": 0}]]}, "Create Workflow in n8n": {"main": [[{"node": "Check Creation Success", "type": "main", "index": 0}]]}, "Check Creation Success": {"main": [[{"node": "Success Response", "type": "main", "index": 0}], [{"node": "Error Response", "type": "main", "index": 0}]]}}, "active": false, "settings": {"timezone": "UTC", "saveManualExecutions": true}, "tags": ["meta-agent", "ai-builder"]}
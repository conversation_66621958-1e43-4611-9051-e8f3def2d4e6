/**
 * Pinecone Control Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import type { PodSpec } from './PodSpec';
import type { ServerlessSpec } from './ServerlessSpec';
/**
 *
 * @export
 * @interface IndexModelSpec
 */
export interface IndexModelSpec {
    /**
     *
     * @type {PodSpec}
     * @memberof IndexModelSpec
     */
    pod?: PodSpec;
    /**
     *
     * @type {ServerlessSpec}
     * @memberof IndexModelSpec
     */
    serverless?: ServerlessSpec;
}
/**
 * Check if a given object implements the IndexModelSpec interface.
 */
export declare function instanceOfIndexModelSpec(value: object): boolean;
export declare function IndexModelSpecFromJSON(json: any): IndexModelSpec;
export declare function IndexModelSpecFromJSONTyped(json: any, ignoreDiscriminator: boolean): IndexModelSpec;
export declare function IndexModelSpecToJSON(value?: IndexModelSpec | null): any;

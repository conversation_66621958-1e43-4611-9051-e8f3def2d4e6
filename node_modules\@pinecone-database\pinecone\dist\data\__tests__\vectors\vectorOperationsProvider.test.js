"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var vectorOperationsProvider_1 = require("../../vectors/vectorOperationsProvider");
var indexHostSingleton_1 = require("../../indexHostSingleton");
var db_data_1 = require("../../../pinecone-generated-ts-fetch/db_data");
jest.mock('../../../pinecone-generated-ts-fetch/db_data', function () { return (__assign(__assign({}, jest.requireActual('../../../pinecone-generated-ts-fetch/db_data')), { Configuration: jest.fn() })); });
describe('DataOperationsProvider', function () {
    var real;
    var config = {
        apiKey: 'test-api-key',
    };
    beforeAll(function () {
        real = indexHostSingleton_1.IndexHostSingleton.getHostUrl;
    });
    afterAll(function () {
        indexHostSingleton_1.IndexHostSingleton.getHostUrl = real;
    });
    beforeEach(function () {
        indexHostSingleton_1.IndexHostSingleton.getHostUrl = jest.fn();
    });
    afterEach(function () {
        indexHostSingleton_1.IndexHostSingleton._reset();
    });
    test('makes no API calls on instantiation', function () { return __awaiter(void 0, void 0, void 0, function () {
        var config;
        return __generator(this, function (_a) {
            config = {
                apiKey: 'test-api-key',
            };
            new vectorOperationsProvider_1.VectorOperationsProvider(config, 'index-name');
            expect(indexHostSingleton_1.IndexHostSingleton.getHostUrl).not.toHaveBeenCalled();
            return [2 /*return*/];
        });
    }); });
    test('api calls occur only the first time the provide method is called', function () { return __awaiter(void 0, void 0, void 0, function () {
        var config, provider, api, api2;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    config = {
                        apiKey: 'test-api-key',
                    };
                    provider = new vectorOperationsProvider_1.VectorOperationsProvider(config, 'index-name');
                    expect(indexHostSingleton_1.IndexHostSingleton.getHostUrl).not.toHaveBeenCalled();
                    return [4 /*yield*/, provider.provide()];
                case 1:
                    api = _a.sent();
                    expect(indexHostSingleton_1.IndexHostSingleton.getHostUrl).toHaveBeenCalled();
                    return [4 /*yield*/, provider.provide()];
                case 2:
                    api2 = _a.sent();
                    expect(indexHostSingleton_1.IndexHostSingleton.getHostUrl).toHaveBeenCalledTimes(1);
                    expect(api).toEqual(api2);
                    return [2 /*return*/];
            }
        });
    }); });
    test('passing indexHostUrl skips hostUrl resolution', function () { return __awaiter(void 0, void 0, void 0, function () {
        var indexHostUrl, provider;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    indexHostUrl = 'http://index-host-url';
                    provider = new vectorOperationsProvider_1.VectorOperationsProvider(config, 'index-name', indexHostUrl);
                    jest.spyOn(provider, 'buildDataOperationsConfig');
                    return [4 /*yield*/, provider.provide()];
                case 1:
                    _a.sent();
                    expect(indexHostSingleton_1.IndexHostSingleton.getHostUrl).not.toHaveBeenCalled();
                    expect(provider.buildDataOperationsConfig).toHaveBeenCalled();
                    return [2 /*return*/];
            }
        });
    }); });
    test('passing additionalHeaders applies them to the API Configuration', function () { return __awaiter(void 0, void 0, void 0, function () {
        var additionalHeaders, provider;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    additionalHeaders = { 'x-custom-header': 'custom-value' };
                    provider = new vectorOperationsProvider_1.VectorOperationsProvider(config, 'index-name', undefined, additionalHeaders);
                    return [4 /*yield*/, provider.provide()];
                case 1:
                    _a.sent();
                    expect(db_data_1.Configuration).toHaveBeenCalledWith(expect.objectContaining({
                        headers: expect.objectContaining(additionalHeaders),
                    }));
                    return [2 /*return*/];
            }
        });
    }); });
});
//# sourceMappingURL=vectorOperationsProvider.test.js.map
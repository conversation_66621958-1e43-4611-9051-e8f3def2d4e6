/**
 * Pinecone Data Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import type { ScoredVector } from './ScoredVector';
import type { SingleQueryResults } from './SingleQueryResults';
import type { Usage } from './Usage';
/**
 * The response for the `query` operation. These are the matches found for a particular query vector. The matches are ordered from most similar to least similar.
 * @export
 * @interface QueryResponse
 */
export interface QueryResponse {
    /**
     * DEPRECATED. The results of each query. The order is the same as `QueryRequest.queries`.
     * @type {Array<SingleQueryResults>}
     * @memberof QueryResponse
     * @deprecated
     */
    results?: Array<SingleQueryResults>;
    /**
     * The matches for the vectors.
     * @type {Array<ScoredVector>}
     * @memberof QueryResponse
     */
    matches?: Array<ScoredVector>;
    /**
     * The namespace for the vectors.
     * @type {string}
     * @memberof QueryResponse
     */
    namespace?: string;
    /**
     *
     * @type {Usage}
     * @memberof QueryResponse
     */
    usage?: Usage;
}
/**
 * Check if a given object implements the QueryResponse interface.
 */
export declare function instanceOfQueryResponse(value: object): boolean;
export declare function QueryResponseFromJSON(json: any): QueryResponse;
export declare function QueryResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): QueryResponse;
export declare function QueryResponseToJSON(value?: QueryResponse | null): any;

// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
/** Known values of {@link EncryptionAlgorithmType} that the service accepts. */
export var KnownEncryptionAlgorithmType;
(function (KnownEncryptionAlgorithmType) {
    KnownEncryptionAlgorithmType["AES256"] = "AES256";
})(KnownEncryptionAlgorithmType || (KnownEncryptionAlgorithmType = {}));
//# sourceMappingURL=generatedModels.js.map
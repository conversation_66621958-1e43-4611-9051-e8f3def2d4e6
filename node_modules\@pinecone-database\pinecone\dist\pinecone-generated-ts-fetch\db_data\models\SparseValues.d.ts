/**
 * Pinecone Data Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
/**
 * Vector sparse data. Represented as a list of indices and a list of  corresponded values, which must be with the same length.
 * @export
 * @interface SparseValues
 */
export interface SparseValues {
    /**
     * The indices of the sparse data.
     * @type {Array<number>}
     * @memberof SparseValues
     */
    indices: Array<number>;
    /**
     * The corresponding values of the sparse data, which must be with the same length as the indices.
     * @type {Array<number>}
     * @memberof SparseValues
     */
    values: Array<number>;
}
/**
 * Check if a given object implements the SparseValues interface.
 */
export declare function instanceOfSparseValues(value: object): boolean;
export declare function SparseValuesFromJSON(json: any): SparseValues;
export declare function SparseValuesFromJSONTyped(json: any, ignoreDiscriminator: boolean): SparseValues;
export declare function SparseValuesToJSON(value?: SparseValues | null): any;

{"version": 3, "file": "SASQueryParameters.js", "sourceRoot": "", "sources": ["../../../../src/sas/SASQueryParameters.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EAAE,eAAe,EAAE,MAAM,cAAc,CAAC;AAC/C,OAAO,EAAE,oBAAoB,EAAE,MAAM,uBAAuB,CAAC;AAG7D;;GAEG;AACH,MAAM,CAAN,IAAY,WAUX;AAVD,WAAY,WAAW;IACrB;;OAEG;IACH,8BAAe,CAAA;IAEf;;OAEG;IACH,0CAA2B,CAAA;AAC7B,CAAC,EAVW,WAAW,KAAX,WAAW,QAUtB;AA4FD;;;;;;;;GAQG;AACH,MAAM,OAAO,kBAAkB;IA+I7B;;;;OAIG;IACH,IAAW,OAAO;QAChB,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACtB,OAAO;gBACL,GAAG,EAAE,IAAI,CAAC,YAAY,CAAC,GAAG;gBAC1B,KAAK,EAAE,IAAI,CAAC,YAAY,CAAC,KAAK;aAC/B,CAAC;QACJ,CAAC;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;IA0DD,YACE,OAAe,EACf,SAAiB,EACjB,oBAAyD,EACzD,QAAiB,EACjB,aAAsB,EACtB,QAAsB,EACtB,QAAe,EACf,SAAgB,EAChB,OAAoB,EACpB,UAAmB,EACnB,QAAiB,EACjB,YAAqB,EACrB,kBAA2B,EAC3B,eAAwB,EACxB,eAAwB,EACxB,WAAoB,EACpB,iBAAqC,EACrC,0BAAmC,EACnC,aAAsB,EACtB,eAAwB;QAExB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAE3B,IAAI,oBAAoB,KAAK,SAAS,IAAI,OAAO,oBAAoB,KAAK,QAAQ,EAAE,CAAC;YACnF,4BAA4B;YAC5B,IAAI,CAAC,WAAW,GAAG,oBAAoB,CAAC,WAAW,CAAC;YACpD,IAAI,CAAC,QAAQ,GAAG,oBAAoB,CAAC,QAAQ,CAAC;YAC9C,IAAI,CAAC,aAAa,GAAG,oBAAoB,CAAC,aAAa,CAAC;YACxD,IAAI,CAAC,QAAQ,GAAG,oBAAoB,CAAC,QAAQ,CAAC;YAC9C,IAAI,CAAC,QAAQ,GAAG,oBAAoB,CAAC,QAAQ,CAAC;YAC9C,IAAI,CAAC,SAAS,GAAG,oBAAoB,CAAC,SAAS,CAAC;YAChD,IAAI,CAAC,YAAY,GAAG,oBAAoB,CAAC,OAAO,CAAC;YACjD,IAAI,CAAC,UAAU,GAAG,oBAAoB,CAAC,UAAU,CAAC;YAClD,IAAI,CAAC,eAAe,GAAG,oBAAoB,CAAC,eAAe,CAAC;YAC5D,IAAI,CAAC,QAAQ,GAAG,oBAAoB,CAAC,QAAQ,CAAC;YAC9C,IAAI,CAAC,YAAY,GAAG,oBAAoB,CAAC,YAAY,CAAC;YACtD,IAAI,CAAC,kBAAkB,GAAG,oBAAoB,CAAC,kBAAkB,CAAC;YAClE,IAAI,CAAC,eAAe,GAAG,oBAAoB,CAAC,eAAe,CAAC;YAC5D,IAAI,CAAC,eAAe,GAAG,oBAAoB,CAAC,eAAe,CAAC;YAC5D,IAAI,CAAC,WAAW,GAAG,oBAAoB,CAAC,WAAW,CAAC;YAEpD,IAAI,oBAAoB,CAAC,iBAAiB,EAAE,CAAC;gBAC3C,IAAI,CAAC,SAAS,GAAG,oBAAoB,CAAC,iBAAiB,CAAC,cAAc,CAAC;gBACvE,IAAI,CAAC,cAAc,GAAG,oBAAoB,CAAC,iBAAiB,CAAC,cAAc,CAAC;gBAC5E,IAAI,CAAC,cAAc,GAAG,oBAAoB,CAAC,iBAAiB,CAAC,cAAc,CAAC;gBAC5E,IAAI,CAAC,eAAe,GAAG,oBAAoB,CAAC,iBAAiB,CAAC,eAAe,CAAC;gBAC9E,IAAI,CAAC,aAAa,GAAG,oBAAoB,CAAC,iBAAiB,CAAC,aAAa,CAAC;gBAC1E,IAAI,CAAC,aAAa,GAAG,oBAAoB,CAAC,iBAAiB,CAAC,aAAa,CAAC;gBAE1E,IAAI,CAAC,0BAA0B,GAAG,oBAAoB,CAAC,0BAA0B,CAAC;gBAClF,IAAI,CAAC,aAAa,GAAG,oBAAoB,CAAC,aAAa,CAAC;YAC1D,CAAC;QACH,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;YACzB,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;YACnC,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;YAC3B,IAAI,CAAC,WAAW,GAAG,oBAAoB,CAAC;YACxC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;YACzB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;YACzB,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC;YAC5B,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;YACvC,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;YAC7B,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;YACzB,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;YACjC,IAAI,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;YAC7C,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;YACvC,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;YACvC,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;YAE/B,IAAI,iBAAiB,EAAE,CAAC;gBACtB,IAAI,CAAC,SAAS,GAAG,iBAAiB,CAAC,cAAc,CAAC;gBAClD,IAAI,CAAC,cAAc,GAAG,iBAAiB,CAAC,cAAc,CAAC;gBACvD,IAAI,CAAC,cAAc,GAAG,iBAAiB,CAAC,cAAc,CAAC;gBACvD,IAAI,CAAC,eAAe,GAAG,iBAAiB,CAAC,eAAe,CAAC;gBACzD,IAAI,CAAC,aAAa,GAAG,iBAAiB,CAAC,aAAa,CAAC;gBACrD,IAAI,CAAC,aAAa,GAAG,iBAAiB,CAAC,aAAa,CAAC;gBAErD,IAAI,CAAC,0BAA0B,GAAG,0BAA0B,CAAC;gBAC7D,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;YACrC,CAAC;QACH,CAAC;IACH,CAAC;IAED;;;OAGG;IACI,QAAQ;QACb,MAAM,MAAM,GAAa;YACvB,IAAI;YACJ,IAAI;YACJ,KAAK;YACL,KAAK;YACL,IAAI;YACJ,IAAI;YACJ,KAAK;YACL,IAAI;YACJ,KAAK;YACL,OAAO,EAAE,mBAAmB;YAC5B,OAAO,EAAE,mBAAmB;YAC5B,KAAK,EAAE,wBAAwB;YAC/B,KAAK,EAAE,yBAAyB;YAChC,KAAK,EAAE,qBAAqB;YAC5B,KAAK,EAAE,qBAAqB;YAC5B,IAAI;YACJ,IAAI;YACJ,KAAK;YACL,MAAM;YACN,MAAM;YACN,MAAM;YACN,MAAM;YACN,MAAM;YACN,OAAO;YACP,MAAM;SACP,CAAC;QACF,MAAM,OAAO,GAAa,EAAE,CAAC;QAE7B,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;YAC3B,QAAQ,KAAK,EAAE,CAAC;gBACd,KAAK,IAAI;oBACP,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;oBAC3D,MAAM;gBACR,KAAK,IAAI;oBACP,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;oBAC5D,MAAM;gBACR,KAAK,KAAK;oBACR,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;oBACjE,MAAM;gBACR,KAAK,KAAK;oBACR,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;oBAC5D,MAAM;gBACR,KAAK,IAAI;oBACP,IAAI,CAAC,uBAAuB,CAC1B,OAAO,EACP,KAAK,EACL,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,oBAAoB,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CACvE,CAAC;oBACF,MAAM;gBACR,KAAK,IAAI;oBACP,IAAI,CAAC,uBAAuB,CAC1B,OAAO,EACP,KAAK,EACL,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,oBAAoB,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CACzE,CAAC;oBACF,MAAM;gBACR,KAAK,KAAK;oBACR,IAAI,CAAC,uBAAuB,CAC1B,OAAO,EACP,KAAK,EACL,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS,CACzD,CAAC;oBACF,MAAM;gBACR,KAAK,IAAI;oBACP,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;oBAC9D,MAAM;gBACR,KAAK,KAAK;oBACR,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;oBACnE,MAAM;gBACR,KAAK,OAAO,EAAE,mBAAmB;oBAC/B,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;oBAC7D,MAAM;gBACR,KAAK,OAAO,EAAE,mBAAmB;oBAC/B,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;oBAClE,MAAM;gBACR,KAAK,KAAK,EAAE,wBAAwB;oBAClC,IAAI,CAAC,uBAAuB,CAC1B,OAAO,EACP,KAAK,EACL,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,oBAAoB,CAAC,IAAI,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CACnF,CAAC;oBACF,MAAM;gBACR,KAAK,KAAK,EAAE,yBAAyB;oBACnC,IAAI,CAAC,uBAAuB,CAC1B,OAAO,EACP,KAAK,EACL,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,oBAAoB,CAAC,IAAI,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CACrF,CAAC;oBACF,MAAM;gBACR,KAAK,KAAK,EAAE,qBAAqB;oBAC/B,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;oBACjE,MAAM;gBACR,KAAK,KAAK,EAAE,qBAAqB;oBAC/B,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;oBACjE,MAAM;gBACR,KAAK,IAAI;oBACP,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;oBAC5D,MAAM;gBACR,KAAK,IAAI;oBACP,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;oBAC/D,MAAM;gBACR,KAAK,KAAK;oBACR,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;oBAC7D,MAAM;gBACR,KAAK,MAAM;oBACT,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;oBAChE,MAAM;gBACR,KAAK,MAAM;oBACT,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;oBACtE,MAAM;gBACR,KAAK,MAAM;oBACT,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;oBACnE,MAAM;gBACR,KAAK,MAAM;oBACT,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;oBACnE,MAAM;gBACR,KAAK,MAAM;oBACT,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;oBAC/D,MAAM;gBACR,KAAK,OAAO;oBACV,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,0BAA0B,CAAC,CAAC;oBAC9E,MAAM;gBACR,KAAK,MAAM;oBACT,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;oBACjE,MAAM;YACV,CAAC;QACH,CAAC;QACD,OAAO,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC3B,CAAC;IAED;;;;;;OAMG;IACK,uBAAuB,CAAC,OAAiB,EAAE,GAAW,EAAE,KAAc;QAC5E,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO;QACT,CAAC;QAED,GAAG,GAAG,kBAAkB,CAAC,GAAG,CAAC,CAAC;QAC9B,KAAK,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC;QAClC,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACvC,OAAO,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,KAAK,EAAE,CAAC,CAAC;QAClC,CAAC;IACH,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { SasIPRange } from \"./SasIPRange\";\nimport { ipRangeToString } from \"./SasIPRange\";\nimport { truncatedISO8061Date } from \"../utils/utils.common\";\nimport type { UserDelegationKey } from \"../BlobServiceClient\";\n\n/**\n * Protocols for generated SAS.\n */\nexport enum SASProtocol {\n  /**\n   * Protocol that allows HTTPS only\n   */\n  Https = \"https\",\n\n  /**\n   * Protocol that allows both HTTPS and HTTP\n   */\n  HttpsAndHttp = \"https,http\",\n}\n\n/**\n * Options to construct {@link SASQueryParameters}.\n */\nexport interface SASQueryParametersOptions {\n  /**\n   * Optional only when identifier is provided.\n   * Please refer to {@link AccountSASPermissions}, {@link BlobSASPermissions}, or {@link ContainerSASPermissions} for\n   * more details.\n   */\n  permissions?: string;\n  /**\n   * Optional. The storage services being accessed (only for Account SAS). Please refer to {@link AccountSASServices}\n   * for more details.\n   */\n  services?: string;\n  /**\n   * Optional. The storage resource types being accessed (only for Account SAS). Please refer to\n   * {@link AccountSASResourceTypes} for more details.\n   */\n  resourceTypes?: string;\n  /**\n   * Optional. The allowed HTTP protocol(s).\n   */\n  protocol?: SASProtocol;\n  /**\n   * Optional. The start time for this SAS token.\n   */\n  startsOn?: Date;\n  /**\n   * Optional only when identifier is provided. The expiry time for this SAS token.\n   */\n  expiresOn?: Date;\n  /**\n   * Optional. IP ranges allowed in this SAS.\n   */\n  ipRange?: SasIPRange;\n  /**\n   * Optional. The signed identifier (only for {@link BlobSASSignatureValues}).\n   *\n   * @see https://docs.microsoft.com/en-us/rest/api/storageservices/establishing-a-stored-access-policy\n   */\n  identifier?: string;\n  /**\n   * Optional. Encryption scope to use when sending requests authorized with this SAS URI.\n   */\n  encryptionScope?: string;\n  /**\n   * Optional. Specifies which resources are accessible via the SAS (only for {@link BlobSASSignatureValues}).\n   * @see https://docs.microsoft.com/rest/api/storageservices/create-service-sas#specifying-the-signed-resource-blob-service-only\n   */\n  resource?: string;\n  /**\n   * Value for cache-control header in Blob/File Service SAS.\n   */\n  cacheControl?: string;\n  /**\n   * Value for content-disposition header in Blob/File Service SAS.\n   */\n  contentDisposition?: string;\n  /**\n   * Value for content-encoding header in Blob/File Service SAS.\n   */\n  contentEncoding?: string;\n  /**\n   * Value for content-length header in Blob/File Service SAS.\n   */\n  contentLanguage?: string;\n  /**\n   * Value for content-type header in Blob/File Service SAS.\n   */\n  contentType?: string;\n  /**\n   * User delegation key properties.\n   */\n  userDelegationKey?: UserDelegationKey;\n  /**\n   * Authorized AAD Object ID in GUID format. The AAD Object ID of a user authorized by the owner of the User Delegation Key\n   * to perform the action granted by the SAS. The Azure Storage service will ensure that the owner of the user delegation key\n   * has the required permissions before granting access but no additional permission check for the user specified in\n   * this value will be performed. This cannot be used in conjuction with {@link signedUnauthorizedUserObjectId}.\n   * This is only used for User Delegation SAS.\n   */\n  preauthorizedAgentObjectId?: string;\n  /**\n   * A GUID value that will be logged in the storage diagnostic logs and can be used to correlate SAS generation with storage resource access.\n   * This is only used for User Delegation SAS.\n   */\n  correlationId?: string;\n}\n\n/**\n * Represents the components that make up an Azure Storage SAS' query parameters. This type is not constructed directly\n * by the user; it is only generated by the {@link AccountSASSignatureValues} and {@link BlobSASSignatureValues}\n * types. Once generated, it can be encoded into a {@link String} and appended to a URL directly (though caution should\n * be taken here in case there are existing query parameters, which might affect the appropriate means of appending\n * these query parameters).\n *\n * NOTE: Instances of this class are immutable.\n */\nexport class SASQueryParameters {\n  /**\n   * The storage API version.\n   */\n  public readonly version: string;\n\n  /**\n   * Optional. The allowed HTTP protocol(s).\n   */\n  public readonly protocol?: SASProtocol;\n\n  /**\n   * Optional. The start time for this SAS token.\n   */\n  public readonly startsOn?: Date;\n\n  /**\n   * Optional only when identifier is provided. The expiry time for this SAS token.\n   */\n  public readonly expiresOn?: Date;\n\n  /**\n   * Optional only when identifier is provided.\n   * Please refer to {@link AccountSASPermissions}, {@link BlobSASPermissions}, or {@link ContainerSASPermissions} for\n   * more details.\n   */\n  public readonly permissions?: string;\n\n  /**\n   * Optional. The storage services being accessed (only for Account SAS). Please refer to {@link AccountSASServices}\n   * for more details.\n   */\n  public readonly services?: string;\n\n  /**\n   * Optional. The storage resource types being accessed (only for Account SAS). Please refer to\n   * {@link AccountSASResourceTypes} for more details.\n   */\n  public readonly resourceTypes?: string;\n\n  /**\n   * Optional. The signed identifier (only for {@link BlobSASSignatureValues}).\n   *\n   * @see https://docs.microsoft.com/en-us/rest/api/storageservices/establishing-a-stored-access-policy\n   */\n  public readonly identifier?: string;\n\n  /**\n   * Optional. Encryption scope to use when sending requests authorized with this SAS URI.\n   */\n  public readonly encryptionScope?: string;\n\n  /**\n   * Optional. Specifies which resources are accessible via the SAS (only for {@link BlobSASSignatureValues}).\n   * @see https://docs.microsoft.com/rest/api/storageservices/create-service-sas#specifying-the-signed-resource-blob-service-only\n   */\n  public readonly resource?: string;\n\n  /**\n   * The signature for the SAS token.\n   */\n  public readonly signature: string;\n\n  /**\n   * Value for cache-control header in Blob/File Service SAS.\n   */\n  public readonly cacheControl?: string;\n\n  /**\n   * Value for content-disposition header in Blob/File Service SAS.\n   */\n  public readonly contentDisposition?: string;\n\n  /**\n   * Value for content-encoding header in Blob/File Service SAS.\n   */\n  public readonly contentEncoding?: string;\n\n  /**\n   * Value for content-length header in Blob/File Service SAS.\n   */\n  public readonly contentLanguage?: string;\n\n  /**\n   * Value for content-type header in Blob/File Service SAS.\n   */\n  public readonly contentType?: string;\n\n  /**\n   * Inner value of getter ipRange.\n   */\n  private readonly ipRangeInner?: SasIPRange;\n\n  /**\n   * The Azure Active Directory object ID in GUID format.\n   * Property of user delegation key.\n   */\n  private readonly signedOid?: string;\n\n  /**\n   * The Azure Active Directory tenant ID in GUID format.\n   * Property of user delegation key.\n   */\n  private readonly signedTenantId?: string;\n\n  /**\n   * The date-time the key is active.\n   * Property of user delegation key.\n   */\n  private readonly signedStartsOn?: Date;\n\n  /**\n   * The date-time the key expires.\n   * Property of user delegation key.\n   */\n  private readonly signedExpiresOn?: Date;\n\n  /**\n   * Abbreviation of the Azure Storage service that accepts the user delegation key.\n   * Property of user delegation key.\n   */\n  private readonly signedService?: string;\n\n  /**\n   * The service version that created the user delegation key.\n   * Property of user delegation key.\n   */\n  private readonly signedVersion?: string;\n\n  /**\n   * Authorized AAD Object ID in GUID format. The AAD Object ID of a user authorized by the owner of the User Delegation Key\n   * to perform the action granted by the SAS. The Azure Storage service will ensure that the owner of the user delegation key\n   * has the required permissions before granting access but no additional permission check for the user specified in\n   * this value will be performed. This is only used for User Delegation SAS.\n   */\n  public readonly preauthorizedAgentObjectId?: string;\n\n  /**\n   * A GUID value that will be logged in the storage diagnostic logs and can be used to correlate SAS generation with storage resource access.\n   * This is only used for User Delegation SAS.\n   */\n  public readonly correlationId?: string;\n\n  /**\n   * Optional. IP range allowed for this SAS.\n   *\n   * @readonly\n   */\n  public get ipRange(): SasIPRange | undefined {\n    if (this.ipRangeInner) {\n      return {\n        end: this.ipRangeInner.end,\n        start: this.ipRangeInner.start,\n      };\n    }\n    return undefined;\n  }\n\n  /**\n   * Creates an instance of SASQueryParameters.\n   *\n   * @param version - Representing the storage version\n   * @param signature - Representing the signature for the SAS token\n   * @param permissions - Representing the storage permissions\n   * @param services - Representing the storage services being accessed (only for Account SAS)\n   * @param resourceTypes - Representing the storage resource types being accessed (only for Account SAS)\n   * @param protocol - Representing the allowed HTTP protocol(s)\n   * @param startsOn - Representing the start time for this SAS token\n   * @param expiresOn - Representing the expiry time for this SAS token\n   * @param ipRange - Representing the range of valid IP addresses for this SAS token\n   * @param identifier - Representing the signed identifier (only for Service SAS)\n   * @param resource - Representing the storage container or blob (only for Service SAS)\n   * @param cacheControl - Representing the cache-control header (only for Blob/File Service SAS)\n   * @param contentDisposition - Representing the content-disposition header (only for Blob/File Service SAS)\n   * @param contentEncoding - Representing the content-encoding header (only for Blob/File Service SAS)\n   * @param contentLanguage - Representing the content-language header (only for Blob/File Service SAS)\n   * @param contentType - Representing the content-type header (only for Blob/File Service SAS)\n   * @param userDelegationKey - Representing the user delegation key properties\n   * @param preauthorizedAgentObjectId - Representing the authorized AAD Object ID (only for User Delegation SAS)\n   * @param correlationId - Representing the correlation ID (only for User Delegation SAS)\n   * @param encryptionScope -\n   */\n  constructor(\n    version: string,\n    signature: string,\n    permissions?: string,\n    services?: string,\n    resourceTypes?: string,\n    protocol?: SASProtocol,\n    startsOn?: Date,\n    expiresOn?: Date,\n    ipRange?: SasIPRange,\n    identifier?: string,\n    resource?: string,\n    cacheControl?: string,\n    contentDisposition?: string,\n    contentEncoding?: string,\n    contentLanguage?: string,\n    contentType?: string,\n    userDelegationKey?: UserDelegationKey,\n    preauthorizedAgentObjectId?: string,\n    correlationId?: string,\n    encryptionScope?: string,\n  );\n\n  /**\n   * Creates an instance of SASQueryParameters.\n   *\n   * @param version - Representing the storage version\n   * @param signature - Representing the signature for the SAS token\n   * @param options - Optional. Options to construct the SASQueryParameters.\n   */\n  constructor(version: string, signature: string, options?: SASQueryParametersOptions);\n\n  constructor(\n    version: string,\n    signature: string,\n    permissionsOrOptions?: string | SASQueryParametersOptions,\n    services?: string,\n    resourceTypes?: string,\n    protocol?: SASProtocol,\n    startsOn?: Date,\n    expiresOn?: Date,\n    ipRange?: SasIPRange,\n    identifier?: string,\n    resource?: string,\n    cacheControl?: string,\n    contentDisposition?: string,\n    contentEncoding?: string,\n    contentLanguage?: string,\n    contentType?: string,\n    userDelegationKey?: UserDelegationKey,\n    preauthorizedAgentObjectId?: string,\n    correlationId?: string,\n    encryptionScope?: string,\n  ) {\n    this.version = version;\n    this.signature = signature;\n\n    if (permissionsOrOptions !== undefined && typeof permissionsOrOptions !== \"string\") {\n      // SASQueryParametersOptions\n      this.permissions = permissionsOrOptions.permissions;\n      this.services = permissionsOrOptions.services;\n      this.resourceTypes = permissionsOrOptions.resourceTypes;\n      this.protocol = permissionsOrOptions.protocol;\n      this.startsOn = permissionsOrOptions.startsOn;\n      this.expiresOn = permissionsOrOptions.expiresOn;\n      this.ipRangeInner = permissionsOrOptions.ipRange;\n      this.identifier = permissionsOrOptions.identifier;\n      this.encryptionScope = permissionsOrOptions.encryptionScope;\n      this.resource = permissionsOrOptions.resource;\n      this.cacheControl = permissionsOrOptions.cacheControl;\n      this.contentDisposition = permissionsOrOptions.contentDisposition;\n      this.contentEncoding = permissionsOrOptions.contentEncoding;\n      this.contentLanguage = permissionsOrOptions.contentLanguage;\n      this.contentType = permissionsOrOptions.contentType;\n\n      if (permissionsOrOptions.userDelegationKey) {\n        this.signedOid = permissionsOrOptions.userDelegationKey.signedObjectId;\n        this.signedTenantId = permissionsOrOptions.userDelegationKey.signedTenantId;\n        this.signedStartsOn = permissionsOrOptions.userDelegationKey.signedStartsOn;\n        this.signedExpiresOn = permissionsOrOptions.userDelegationKey.signedExpiresOn;\n        this.signedService = permissionsOrOptions.userDelegationKey.signedService;\n        this.signedVersion = permissionsOrOptions.userDelegationKey.signedVersion;\n\n        this.preauthorizedAgentObjectId = permissionsOrOptions.preauthorizedAgentObjectId;\n        this.correlationId = permissionsOrOptions.correlationId;\n      }\n    } else {\n      this.services = services;\n      this.resourceTypes = resourceTypes;\n      this.expiresOn = expiresOn;\n      this.permissions = permissionsOrOptions;\n      this.protocol = protocol;\n      this.startsOn = startsOn;\n      this.ipRangeInner = ipRange;\n      this.encryptionScope = encryptionScope;\n      this.identifier = identifier;\n      this.resource = resource;\n      this.cacheControl = cacheControl;\n      this.contentDisposition = contentDisposition;\n      this.contentEncoding = contentEncoding;\n      this.contentLanguage = contentLanguage;\n      this.contentType = contentType;\n\n      if (userDelegationKey) {\n        this.signedOid = userDelegationKey.signedObjectId;\n        this.signedTenantId = userDelegationKey.signedTenantId;\n        this.signedStartsOn = userDelegationKey.signedStartsOn;\n        this.signedExpiresOn = userDelegationKey.signedExpiresOn;\n        this.signedService = userDelegationKey.signedService;\n        this.signedVersion = userDelegationKey.signedVersion;\n\n        this.preauthorizedAgentObjectId = preauthorizedAgentObjectId;\n        this.correlationId = correlationId;\n      }\n    }\n  }\n\n  /**\n   * Encodes all SAS query parameters into a string that can be appended to a URL.\n   *\n   */\n  public toString(): string {\n    const params: string[] = [\n      \"sv\",\n      \"ss\",\n      \"srt\",\n      \"spr\",\n      \"st\",\n      \"se\",\n      \"sip\",\n      \"si\",\n      \"ses\",\n      \"skoid\", // Signed object ID\n      \"sktid\", // Signed tenant ID\n      \"skt\", // Signed key start time\n      \"ske\", // Signed key expiry time\n      \"sks\", // Signed key service\n      \"skv\", // Signed key version\n      \"sr\",\n      \"sp\",\n      \"sig\",\n      \"rscc\",\n      \"rscd\",\n      \"rsce\",\n      \"rscl\",\n      \"rsct\",\n      \"saoid\",\n      \"scid\",\n    ];\n    const queries: string[] = [];\n\n    for (const param of params) {\n      switch (param) {\n        case \"sv\":\n          this.tryAppendQueryParameter(queries, param, this.version);\n          break;\n        case \"ss\":\n          this.tryAppendQueryParameter(queries, param, this.services);\n          break;\n        case \"srt\":\n          this.tryAppendQueryParameter(queries, param, this.resourceTypes);\n          break;\n        case \"spr\":\n          this.tryAppendQueryParameter(queries, param, this.protocol);\n          break;\n        case \"st\":\n          this.tryAppendQueryParameter(\n            queries,\n            param,\n            this.startsOn ? truncatedISO8061Date(this.startsOn, false) : undefined,\n          );\n          break;\n        case \"se\":\n          this.tryAppendQueryParameter(\n            queries,\n            param,\n            this.expiresOn ? truncatedISO8061Date(this.expiresOn, false) : undefined,\n          );\n          break;\n        case \"sip\":\n          this.tryAppendQueryParameter(\n            queries,\n            param,\n            this.ipRange ? ipRangeToString(this.ipRange) : undefined,\n          );\n          break;\n        case \"si\":\n          this.tryAppendQueryParameter(queries, param, this.identifier);\n          break;\n        case \"ses\":\n          this.tryAppendQueryParameter(queries, param, this.encryptionScope);\n          break;\n        case \"skoid\": // Signed object ID\n          this.tryAppendQueryParameter(queries, param, this.signedOid);\n          break;\n        case \"sktid\": // Signed tenant ID\n          this.tryAppendQueryParameter(queries, param, this.signedTenantId);\n          break;\n        case \"skt\": // Signed key start time\n          this.tryAppendQueryParameter(\n            queries,\n            param,\n            this.signedStartsOn ? truncatedISO8061Date(this.signedStartsOn, false) : undefined,\n          );\n          break;\n        case \"ske\": // Signed key expiry time\n          this.tryAppendQueryParameter(\n            queries,\n            param,\n            this.signedExpiresOn ? truncatedISO8061Date(this.signedExpiresOn, false) : undefined,\n          );\n          break;\n        case \"sks\": // Signed key service\n          this.tryAppendQueryParameter(queries, param, this.signedService);\n          break;\n        case \"skv\": // Signed key version\n          this.tryAppendQueryParameter(queries, param, this.signedVersion);\n          break;\n        case \"sr\":\n          this.tryAppendQueryParameter(queries, param, this.resource);\n          break;\n        case \"sp\":\n          this.tryAppendQueryParameter(queries, param, this.permissions);\n          break;\n        case \"sig\":\n          this.tryAppendQueryParameter(queries, param, this.signature);\n          break;\n        case \"rscc\":\n          this.tryAppendQueryParameter(queries, param, this.cacheControl);\n          break;\n        case \"rscd\":\n          this.tryAppendQueryParameter(queries, param, this.contentDisposition);\n          break;\n        case \"rsce\":\n          this.tryAppendQueryParameter(queries, param, this.contentEncoding);\n          break;\n        case \"rscl\":\n          this.tryAppendQueryParameter(queries, param, this.contentLanguage);\n          break;\n        case \"rsct\":\n          this.tryAppendQueryParameter(queries, param, this.contentType);\n          break;\n        case \"saoid\":\n          this.tryAppendQueryParameter(queries, param, this.preauthorizedAgentObjectId);\n          break;\n        case \"scid\":\n          this.tryAppendQueryParameter(queries, param, this.correlationId);\n          break;\n      }\n    }\n    return queries.join(\"&\");\n  }\n\n  /**\n   * A private helper method used to filter and append query key/value pairs into an array.\n   *\n   * @param queries -\n   * @param key -\n   * @param value -\n   */\n  private tryAppendQueryParameter(queries: string[], key: string, value?: string): void {\n    if (!value) {\n      return;\n    }\n\n    key = encodeURIComponent(key);\n    value = encodeURIComponent(value);\n    if (key.length > 0 && value.length > 0) {\n      queries.push(`${key}=${value}`);\n    }\n  }\n}\n"]}
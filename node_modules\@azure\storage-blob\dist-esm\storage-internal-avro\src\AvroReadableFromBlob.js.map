{"version": 3, "file": "AvroReadableFromBlob.js", "sourceRoot": "", "sources": ["../../../../storage-internal-avro/src/AvroReadableFromBlob.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAC;AAC9C,OAAO,EAAE,UAAU,EAAE,MAAM,yBAAyB,CAAC;AAErD,MAAM,WAAW,GAAG,IAAI,UAAU,CAAC,yCAAyC,CAAC,CAAC;AAE9E,MAAM,OAAO,oBAAqB,SAAQ,YAAY;IAIpD,YAAY,IAAU;QACpB,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;IACrB,CAAC;IAED,IAAW,QAAQ;QACjB,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,IAAY,EAAE,UAAmC,EAAE;QACnE,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC;QACxD,IAAI,IAAI,IAAI,CAAC,EAAE,CAAC;YACd,OAAO,IAAI,UAAU,EAAE,CAAC;QAC1B,CAAC;QAED,MAAM,UAAU,GAAG,IAAI,UAAU,EAAE,CAAC;QACpC,OAAO,IAAI,OAAO,CAAa,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACjD,SAAS,OAAO;gBACd,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;oBACxB,OAAO,CAAC,WAAY,CAAC,mBAAmB,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;gBAClE,CAAC;YACH,CAAC;YAED,SAAS,YAAY;gBACnB,UAAU,CAAC,KAAK,EAAE,CAAC;gBACnB,OAAO,EAAE,CAAC;gBACV,MAAM,CAAC,WAAW,CAAC,CAAC;YACtB,CAAC;YAED,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;gBACxB,OAAO,CAAC,WAAW,CAAC,gBAAgB,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;YAC9D,CAAC;YAED,UAAU,CAAC,SAAS,GAAG,CAAC,EAAO,EAAE,EAAE;gBACjC,OAAO,EAAE,CAAC;gBACV,OAAO,CAAC,IAAI,UAAU,CAAC,EAAE,CAAC,MAAO,CAAC,MAAM,CAAC,CAAC,CAAC;YAC7C,CAAC,CAAC;YAEF,UAAU,CAAC,OAAO,GAAG,GAAG,EAAE;gBACxB,OAAO,EAAE,CAAC;gBACV,MAAM,EAAE,CAAC;YACX,CAAC,CAAC;YAEF,UAAU,CAAC,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC;QAC3F,CAAC,CAAC,CAAC;IACL,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { AvroReadableReadOptions } from \"./AvroReadable\";\nimport { AvroReadable } from \"./AvroReadable\";\nimport { AbortError } from \"@azure/abort-controller\";\n\nconst ABORT_ERROR = new AbortError(\"Reading from the avro blob was aborted.\");\n\nexport class AvroReadableFromBlob extends AvroReadable {\n  private _position: number;\n  private _blob: Blob;\n\n  constructor(blob: Blob) {\n    super();\n    this._blob = blob;\n    this._position = 0;\n  }\n\n  public get position(): number {\n    return this._position;\n  }\n\n  public async read(size: number, options: AvroReadableReadOptions = {}): Promise<Uint8Array> {\n    size = Math.min(size, this._blob.size - this._position);\n    if (size <= 0) {\n      return new Uint8Array();\n    }\n\n    const fileReader = new FileReader();\n    return new Promise<Uint8Array>((resolve, reject) => {\n      function cleanUp(): void {\n        if (options.abortSignal) {\n          options.abortSignal!.removeEventListener(\"abort\", abortHandler);\n        }\n      }\n\n      function abortHandler(): void {\n        fileReader.abort();\n        cleanUp();\n        reject(ABORT_ERROR);\n      }\n\n      if (options.abortSignal) {\n        options.abortSignal.addEventListener(\"abort\", abortHandler);\n      }\n\n      fileReader.onloadend = (ev: any) => {\n        cleanUp();\n        resolve(new Uint8Array(ev.target!.result));\n      };\n\n      fileReader.onerror = () => {\n        cleanUp();\n        reject();\n      };\n\n      fileReader.readAsArrayBuffer(this._blob.slice(this._position, (this._position += size)));\n    });\n  }\n}\n"]}
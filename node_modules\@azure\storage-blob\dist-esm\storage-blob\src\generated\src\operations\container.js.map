{"version": 3, "file": "container.js", "sourceRoot": "", "sources": ["../../../../../../src/generated/src/operations/container.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAGH,OAAO,KAAK,UAAU,MAAM,oBAAoB,CAAC;AAEjD,OAAO,KAAK,OAAO,MAAM,mBAAmB,CAAC;AAC7C,OAAO,KAAK,UAAU,MAAM,sBAAsB,CAAC;AAyCnD,6CAA6C;AAC7C,MAAM,OAAO,aAAa;IAGxB;;;OAGG;IACH,YAAY,MAAqB;QAC/B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IAED;;;;OAIG;IACH,MAAM,CACJ,OAAuC;QAEvC,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,EAAE,OAAO,EAAE,EAAE,mBAAmB,CAAC,CAAC;IAC5E,CAAC;IAED;;;;OAIG;IACH,aAAa,CACX,OAA8C;QAE9C,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,OAAO,EAAE,EACX,0BAA0B,CAC3B,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACH,MAAM,CACJ,OAAuC;QAEvC,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,EAAE,OAAO,EAAE,EAAE,mBAAmB,CAAC,CAAC;IAC5E,CAAC;IAED;;;OAGG;IACH,WAAW,CACT,OAA4C;QAE5C,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,OAAO,EAAE,EACX,wBAAwB,CACzB,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACH,eAAe,CACb,OAAgD;QAEhD,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,OAAO,EAAE,EACX,4BAA4B,CAC7B,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACH,eAAe,CACb,OAAgD;QAEhD,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,OAAO,EAAE,EACX,4BAA4B,CAC7B,CAAC;IACJ,CAAC;IAED;;;OAGG;IACH,OAAO,CACL,OAAwC;QAExC,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,EAAE,OAAO,EAAE,EAAE,oBAAoB,CAAC,CAAC;IAC7E,CAAC;IAED;;;;OAIG;IACH,MAAM,CACJ,mBAA2B,EAC3B,OAAuC;QAEvC,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,mBAAmB,EAAE,OAAO,EAAE,EAChC,mBAAmB,CACpB,CAAC;IACJ,CAAC;IAED;;;;;;;OAOG;IACH,WAAW,CACT,aAAqB,EACrB,oBAA4B,EAC5B,IAAsC,EACtC,OAA4C;QAE5C,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,aAAa,EAAE,oBAAoB,EAAE,IAAI,EAAE,OAAO,EAAE,EACtD,wBAAwB,CACzB,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACH,WAAW,CACT,OAA4C;QAE5C,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,OAAO,EAAE,EACX,wBAAwB,CACzB,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACH,YAAY,CACV,OAA6C;QAE7C,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,OAAO,EAAE,EACX,yBAAyB,CAC1B,CAAC;IACJ,CAAC;IAED;;;;;OAKG;IACH,YAAY,CACV,OAAe,EACf,OAA6C;QAE7C,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,OAAO,EAAE,OAAO,EAAE,EACpB,yBAAyB,CAC1B,CAAC;IACJ,CAAC;IAED;;;;;OAKG;IACH,UAAU,CACR,OAAe,EACf,OAA2C;QAE3C,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,OAAO,EAAE,OAAO,EAAE,EACpB,uBAAuB,CACxB,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACH,UAAU,CACR,OAA2C;QAE3C,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,OAAO,EAAE,EACX,uBAAuB,CACxB,CAAC;IACJ,CAAC;IAED;;;;;;;;OAQG;IACH,WAAW,CACT,OAAe,EACf,eAAuB,EACvB,OAA4C;QAE5C,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,OAAO,EAAE,eAAe,EAAE,OAAO,EAAE,EACrC,wBAAwB,CACzB,CAAC;IACJ,CAAC;IAED;;;OAGG;IACH,mBAAmB,CACjB,OAAoD;QAEpD,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,OAAO,EAAE,EACX,gCAAgC,CACjC,CAAC;IACJ,CAAC;IAED;;;;;;;OAOG;IACH,wBAAwB,CACtB,SAAiB,EACjB,OAAyD;QAEzD,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,SAAS,EAAE,OAAO,EAAE,EACtB,qCAAqC,CACtC,CAAC;IACJ,CAAC;IAED;;;OAGG;IACH,cAAc,CACZ,OAA+C;QAE/C,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,OAAO,EAAE,EACX,2BAA2B,CAC5B,CAAC;IACJ,CAAC;CACF;AACD,2BAA2B;AAC3B,MAAM,aAAa,GAAG,UAAU,CAAC,gBAAgB,CAAC,OAAO,EAAE,WAAW,CAAC,IAAI,CAAC,CAAC;AAE7E,MAAM,mBAAmB,GAA6B;IACpD,IAAI,EAAE,kBAAkB;IACxB,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,aAAa,EAAE,OAAO,CAAC,sBAAsB;SAC9C;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,+BAA+B;SACvD;KACF;IACD,eAAe,EAAE,CAAC,UAAU,CAAC,gBAAgB,EAAE,UAAU,CAAC,QAAQ,CAAC;IACnE,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,QAAQ;QACnB,UAAU,CAAC,MAAM;QACjB,UAAU,CAAC,sBAAsB;QACjC,UAAU,CAAC,8BAA8B;KAC1C;IACD,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,0BAA0B,GAA6B;IAC3D,IAAI,EAAE,kBAAkB;IACxB,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,aAAa,EAAE,OAAO,CAAC,6BAA6B;SACrD;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,sCAAsC;SAC9D;KACF;IACD,eAAe,EAAE,CAAC,UAAU,CAAC,gBAAgB,EAAE,UAAU,CAAC,QAAQ,CAAC;IACnE,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,OAAO;KACnB;IACD,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,mBAAmB,GAA6B;IACpD,IAAI,EAAE,kBAAkB;IACxB,UAAU,EAAE,QAAQ;IACpB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,aAAa,EAAE,OAAO,CAAC,sBAAsB;SAC9C;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,+BAA+B;SACvD;KACF;IACD,eAAe,EAAE,CAAC,UAAU,CAAC,gBAAgB,EAAE,UAAU,CAAC,QAAQ,CAAC;IACnE,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,eAAe;QAC1B,UAAU,CAAC,iBAAiB;KAC7B;IACD,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,wBAAwB,GAA6B;IACzD,IAAI,EAAE,kBAAkB;IACxB,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,aAAa,EAAE,OAAO,CAAC,2BAA2B;SACnD;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,oCAAoC;SAC5D;KACF;IACD,eAAe,EAAE;QACf,UAAU,CAAC,gBAAgB;QAC3B,UAAU,CAAC,QAAQ;QACnB,UAAU,CAAC,KAAK;KACjB;IACD,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,QAAQ;QACnB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,eAAe;KAC3B;IACD,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,4BAA4B,GAA6B;IAC7D,IAAI,EAAE,kBAAkB;IACxB,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,UAAU,EAAE;gBACV,IAAI,EAAE;oBACJ,IAAI,EAAE,UAAU;oBAChB,OAAO,EAAE;wBACP,IAAI,EAAE,EAAE,IAAI,EAAE,WAAW,EAAE,SAAS,EAAE,kBAAkB,EAAE;qBAC3D;iBACF;gBACD,cAAc,EAAE,mBAAmB;gBACnC,OAAO,EAAE,mBAAmB;gBAC5B,YAAY,EAAE,IAAI;gBAClB,cAAc,EAAE,kBAAkB;aACnC;YACD,aAAa,EAAE,OAAO,CAAC,+BAA+B;SACvD;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,wCAAwC;SAChE;KACF;IACD,eAAe,EAAE;QACf,UAAU,CAAC,gBAAgB;QAC3B,UAAU,CAAC,QAAQ;QACnB,UAAU,CAAC,KAAK;KACjB;IACD,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,OAAO;KACnB;IACD,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,4BAA4B,GAA6B;IAC7D,IAAI,EAAE,kBAAkB;IACxB,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,aAAa,EAAE,OAAO,CAAC,+BAA+B;SACvD;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,wCAAwC;SAChE;KACF;IACD,WAAW,EAAE,UAAU,CAAC,YAAY;IACpC,eAAe,EAAE;QACf,UAAU,CAAC,gBAAgB;QAC3B,UAAU,CAAC,QAAQ;QACnB,UAAU,CAAC,KAAK;KACjB;IACD,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,MAAM;QACjB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,MAAM;QACjB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,eAAe;QAC1B,UAAU,CAAC,iBAAiB;KAC7B;IACD,KAAK,EAAE,IAAI;IACX,WAAW,EAAE,gCAAgC;IAC7C,SAAS,EAAE,KAAK;IAChB,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,oBAAoB,GAA6B;IACrD,IAAI,EAAE,kBAAkB;IACxB,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,aAAa,EAAE,OAAO,CAAC,uBAAuB;SAC/C;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,gCAAgC;SACxD;KACF;IACD,eAAe,EAAE;QACf,UAAU,CAAC,gBAAgB;QAC3B,UAAU,CAAC,QAAQ;QACnB,UAAU,CAAC,KAAK;KACjB;IACD,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,oBAAoB;QAC/B,UAAU,CAAC,uBAAuB;KACnC;IACD,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,mBAAmB,GAA6B;IACpD,IAAI,EAAE,kBAAkB;IACxB,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,aAAa,EAAE,OAAO,CAAC,sBAAsB;SAC9C;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,+BAA+B;SACvD;KACF;IACD,eAAe,EAAE;QACf,UAAU,CAAC,gBAAgB;QAC3B,UAAU,CAAC,QAAQ;QACnB,UAAU,CAAC,KAAK;KACjB;IACD,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,mBAAmB;QAC9B,UAAU,CAAC,aAAa;KACzB;IACD,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,wBAAwB,GAA6B;IACzD,IAAI,EAAE,kBAAkB;IACxB,UAAU,EAAE,MAAM;IAClB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,UAAU,EAAE;gBACV,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;gBACxB,cAAc,EAAE,gBAAgB;aACjC;YACD,aAAa,EAAE,OAAO,CAAC,2BAA2B;SACnD;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,oCAAoC;SAC5D;KACF;IACD,WAAW,EAAE,UAAU,CAAC,IAAI;IAC5B,eAAe,EAAE;QACf,UAAU,CAAC,gBAAgB;QAC3B,UAAU,CAAC,KAAK;QAChB,UAAU,CAAC,QAAQ;KACpB;IACD,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,MAAM;QACjB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,aAAa;QACxB,UAAU,CAAC,oBAAoB;KAChC;IACD,KAAK,EAAE,IAAI;IACX,WAAW,EAAE,gCAAgC;IAC7C,SAAS,EAAE,KAAK;IAChB,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,wBAAwB,GAA6B;IACzD,IAAI,EAAE,kBAAkB;IACxB,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,UAAU,EAAE,OAAO,CAAC,iBAAiB;YACrC,aAAa,EAAE,OAAO,CAAC,2BAA2B;SACnD;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,oCAAoC;SAC5D;KACF;IACD,eAAe,EAAE;QACf,UAAU,CAAC,gBAAgB;QAC3B,UAAU,CAAC,MAAM;QACjB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,KAAK;QAChB,UAAU,CAAC,KAAK;QAChB,UAAU,CAAC,QAAQ;KACpB;IACD,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,OAAO;KACnB;IACD,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,yBAAyB,GAA6B;IAC1D,IAAI,EAAE,kBAAkB;IACxB,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,aAAa,EAAE,OAAO,CAAC,4BAA4B;SACpD;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,qCAAqC;SAC7D;KACF;IACD,eAAe,EAAE;QACf,UAAU,CAAC,gBAAgB;QAC3B,UAAU,CAAC,QAAQ;QACnB,UAAU,CAAC,MAAM;KAClB;IACD,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,eAAe;QAC1B,UAAU,CAAC,iBAAiB;QAC5B,UAAU,CAAC,MAAM;QACjB,UAAU,CAAC,QAAQ;QACnB,UAAU,CAAC,eAAe;KAC3B;IACD,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,yBAAyB,GAA6B;IAC1D,IAAI,EAAE,kBAAkB;IACxB,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,aAAa,EAAE,OAAO,CAAC,4BAA4B;SACpD;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,qCAAqC;SAC7D;KACF;IACD,eAAe,EAAE;QACf,UAAU,CAAC,gBAAgB;QAC3B,UAAU,CAAC,QAAQ;QACnB,UAAU,CAAC,MAAM;KAClB;IACD,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,eAAe;QAC1B,UAAU,CAAC,iBAAiB;QAC5B,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,QAAQ;KACpB;IACD,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,uBAAuB,GAA6B;IACxD,IAAI,EAAE,kBAAkB;IACxB,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,aAAa,EAAE,OAAO,CAAC,0BAA0B;SAClD;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,mCAAmC;SAC3D;KACF;IACD,eAAe,EAAE;QACf,UAAU,CAAC,gBAAgB;QAC3B,UAAU,CAAC,QAAQ;QACnB,UAAU,CAAC,MAAM;KAClB;IACD,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,eAAe;QAC1B,UAAU,CAAC,iBAAiB;QAC5B,UAAU,CAAC,QAAQ;QACnB,UAAU,CAAC,OAAO;KACnB;IACD,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,uBAAuB,GAA6B;IACxD,IAAI,EAAE,kBAAkB;IACxB,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,aAAa,EAAE,OAAO,CAAC,0BAA0B;SAClD;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,mCAAmC;SAC3D;KACF;IACD,eAAe,EAAE;QACf,UAAU,CAAC,gBAAgB;QAC3B,UAAU,CAAC,QAAQ;QACnB,UAAU,CAAC,MAAM;KAClB;IACD,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,eAAe;QAC1B,UAAU,CAAC,iBAAiB;QAC5B,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,WAAW;KACvB;IACD,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,wBAAwB,GAA6B;IACzD,IAAI,EAAE,kBAAkB;IACxB,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,aAAa,EAAE,OAAO,CAAC,2BAA2B;SACnD;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,oCAAoC;SAC5D;KACF;IACD,eAAe,EAAE;QACf,UAAU,CAAC,gBAAgB;QAC3B,UAAU,CAAC,QAAQ;QACnB,UAAU,CAAC,MAAM;KAClB;IACD,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,eAAe;QAC1B,UAAU,CAAC,iBAAiB;QAC5B,UAAU,CAAC,QAAQ;QACnB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,gBAAgB;KAC5B;IACD,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,gCAAgC,GAA6B;IACjE,IAAI,EAAE,kBAAkB;IACxB,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,UAAU,EAAE,OAAO,CAAC,4BAA4B;YAChD,aAAa,EAAE,OAAO,CAAC,mCAAmC;SAC3D;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,4CAA4C;SACpE;KACF;IACD,eAAe,EAAE;QACf,UAAU,CAAC,gBAAgB;QAC3B,UAAU,CAAC,KAAK;QAChB,UAAU,CAAC,MAAM;QACjB,UAAU,CAAC,MAAM;QACjB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,QAAQ;QACnB,UAAU,CAAC,QAAQ;KACpB;IACD,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,OAAO;KACnB;IACD,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,qCAAqC,GAA6B;IACtE,IAAI,EAAE,kBAAkB;IACxB,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,UAAU,EAAE,OAAO,CAAC,iCAAiC;YACrD,aAAa,EAAE,OAAO,CAAC,wCAAwC;SAChE;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,iDAAiD;SACzE;KACF;IACD,eAAe,EAAE;QACf,UAAU,CAAC,gBAAgB;QAC3B,UAAU,CAAC,KAAK;QAChB,UAAU,CAAC,MAAM;QACjB,UAAU,CAAC,MAAM;QACjB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,QAAQ;QACnB,UAAU,CAAC,QAAQ;QACnB,UAAU,CAAC,SAAS;KACrB;IACD,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,OAAO;KACnB;IACD,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,2BAA2B,GAA6B;IAC5D,IAAI,EAAE,kBAAkB;IACxB,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,aAAa,EAAE,OAAO,CAAC,8BAA8B;SACtD;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,uCAAuC;SAC/D;KACF;IACD,eAAe,EAAE;QACf,UAAU,CAAC,IAAI;QACf,UAAU,CAAC,gBAAgB;QAC3B,UAAU,CAAC,QAAQ;KACpB;IACD,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,OAAO;KACnB;IACD,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,aAAa;CAC1B,CAAC", "sourcesContent": ["/*\n * Copyright (c) Microsoft Corporation.\n * Licensed under the MIT License.\n *\n * Code generated by Microsoft (R) AutoRest Code Generator.\n * Changes may cause incorrect behavior and will be lost if the code is regenerated.\n */\n\nimport { Container } from \"../operationsInterfaces\";\nimport * as coreClient from \"@azure/core-client\";\nimport * as coreRestPipeline from \"@azure/core-rest-pipeline\";\nimport * as Mappers from \"../models/mappers\";\nimport * as Parameters from \"../models/parameters\";\nimport { StorageClient } from \"../storageClient\";\nimport {\n  ContainerCreateOptionalParams,\n  ContainerCreateResponse,\n  ContainerGetPropertiesOptionalParams,\n  ContainerGetPropertiesResponse,\n  ContainerDeleteOptionalParams,\n  ContainerDeleteResponse,\n  ContainerSetMetadataOptionalParams,\n  ContainerSetMetadataResponse,\n  ContainerGetAccessPolicyOptionalParams,\n  ContainerGetAccessPolicyResponse,\n  ContainerSetAccessPolicyOptionalParams,\n  ContainerSetAccessPolicyResponse,\n  ContainerRestoreOptionalParams,\n  ContainerRestoreResponse,\n  ContainerRenameOptionalParams,\n  ContainerRenameResponse,\n  ContainerSubmitBatchOptionalParams,\n  ContainerSubmitBatchResponse,\n  ContainerFilterBlobsOptionalParams,\n  ContainerFilterBlobsResponse,\n  ContainerAcquireLeaseOptionalParams,\n  ContainerAcquireLeaseResponse,\n  ContainerReleaseLeaseOptionalParams,\n  ContainerReleaseLeaseResponse,\n  ContainerRenewLeaseOptionalParams,\n  ContainerRenewLeaseResponse,\n  ContainerBreakLeaseOptionalParams,\n  ContainerBreakLeaseResponse,\n  ContainerChangeLeaseOptionalParams,\n  ContainerChangeLeaseResponse,\n  ContainerListBlobFlatSegmentOptionalParams,\n  ContainerListBlobFlatSegmentResponse,\n  ContainerListBlobHierarchySegmentOptionalParams,\n  ContainerListBlobHierarchySegmentResponse,\n  ContainerGetAccountInfoOptionalParams,\n  ContainerGetAccountInfoResponse,\n} from \"../models\";\n\n/** Class containing Container operations. */\nexport class ContainerImpl implements Container {\n  private readonly client: StorageClient;\n\n  /**\n   * Initialize a new instance of the class Container class.\n   * @param client Reference to the service client\n   */\n  constructor(client: StorageClient) {\n    this.client = client;\n  }\n\n  /**\n   * creates a new container under the specified account. If the container with the same name already\n   * exists, the operation fails\n   * @param options The options parameters.\n   */\n  create(\n    options?: ContainerCreateOptionalParams,\n  ): Promise<ContainerCreateResponse> {\n    return this.client.sendOperationRequest({ options }, createOperationSpec);\n  }\n\n  /**\n   * returns all user-defined metadata and system properties for the specified container. The data\n   * returned does not include the container's list of blobs\n   * @param options The options parameters.\n   */\n  getProperties(\n    options?: ContainerGetPropertiesOptionalParams,\n  ): Promise<ContainerGetPropertiesResponse> {\n    return this.client.sendOperationRequest(\n      { options },\n      getPropertiesOperationSpec,\n    );\n  }\n\n  /**\n   * operation marks the specified container for deletion. The container and any blobs contained within\n   * it are later deleted during garbage collection\n   * @param options The options parameters.\n   */\n  delete(\n    options?: ContainerDeleteOptionalParams,\n  ): Promise<ContainerDeleteResponse> {\n    return this.client.sendOperationRequest({ options }, deleteOperationSpec);\n  }\n\n  /**\n   * operation sets one or more user-defined name-value pairs for the specified container.\n   * @param options The options parameters.\n   */\n  setMetadata(\n    options?: ContainerSetMetadataOptionalParams,\n  ): Promise<ContainerSetMetadataResponse> {\n    return this.client.sendOperationRequest(\n      { options },\n      setMetadataOperationSpec,\n    );\n  }\n\n  /**\n   * gets the permissions for the specified container. The permissions indicate whether container data\n   * may be accessed publicly.\n   * @param options The options parameters.\n   */\n  getAccessPolicy(\n    options?: ContainerGetAccessPolicyOptionalParams,\n  ): Promise<ContainerGetAccessPolicyResponse> {\n    return this.client.sendOperationRequest(\n      { options },\n      getAccessPolicyOperationSpec,\n    );\n  }\n\n  /**\n   * sets the permissions for the specified container. The permissions indicate whether blobs in a\n   * container may be accessed publicly.\n   * @param options The options parameters.\n   */\n  setAccessPolicy(\n    options?: ContainerSetAccessPolicyOptionalParams,\n  ): Promise<ContainerSetAccessPolicyResponse> {\n    return this.client.sendOperationRequest(\n      { options },\n      setAccessPolicyOperationSpec,\n    );\n  }\n\n  /**\n   * Restores a previously-deleted container.\n   * @param options The options parameters.\n   */\n  restore(\n    options?: ContainerRestoreOptionalParams,\n  ): Promise<ContainerRestoreResponse> {\n    return this.client.sendOperationRequest({ options }, restoreOperationSpec);\n  }\n\n  /**\n   * Renames an existing container.\n   * @param sourceContainerName Required.  Specifies the name of the container to rename.\n   * @param options The options parameters.\n   */\n  rename(\n    sourceContainerName: string,\n    options?: ContainerRenameOptionalParams,\n  ): Promise<ContainerRenameResponse> {\n    return this.client.sendOperationRequest(\n      { sourceContainerName, options },\n      renameOperationSpec,\n    );\n  }\n\n  /**\n   * The Batch operation allows multiple API calls to be embedded into a single HTTP request.\n   * @param contentLength The length of the request.\n   * @param multipartContentType Required. The value of this header must be multipart/mixed with a batch\n   *                             boundary. Example header value: multipart/mixed; boundary=batch_<GUID>\n   * @param body Initial data\n   * @param options The options parameters.\n   */\n  submitBatch(\n    contentLength: number,\n    multipartContentType: string,\n    body: coreRestPipeline.RequestBodyType,\n    options?: ContainerSubmitBatchOptionalParams,\n  ): Promise<ContainerSubmitBatchResponse> {\n    return this.client.sendOperationRequest(\n      { contentLength, multipartContentType, body, options },\n      submitBatchOperationSpec,\n    );\n  }\n\n  /**\n   * The Filter Blobs operation enables callers to list blobs in a container whose tags match a given\n   * search expression.  Filter blobs searches within the given container.\n   * @param options The options parameters.\n   */\n  filterBlobs(\n    options?: ContainerFilterBlobsOptionalParams,\n  ): Promise<ContainerFilterBlobsResponse> {\n    return this.client.sendOperationRequest(\n      { options },\n      filterBlobsOperationSpec,\n    );\n  }\n\n  /**\n   * [Update] establishes and manages a lock on a container for delete operations. The lock duration can\n   * be 15 to 60 seconds, or can be infinite\n   * @param options The options parameters.\n   */\n  acquireLease(\n    options?: ContainerAcquireLeaseOptionalParams,\n  ): Promise<ContainerAcquireLeaseResponse> {\n    return this.client.sendOperationRequest(\n      { options },\n      acquireLeaseOperationSpec,\n    );\n  }\n\n  /**\n   * [Update] establishes and manages a lock on a container for delete operations. The lock duration can\n   * be 15 to 60 seconds, or can be infinite\n   * @param leaseId Specifies the current lease ID on the resource.\n   * @param options The options parameters.\n   */\n  releaseLease(\n    leaseId: string,\n    options?: ContainerReleaseLeaseOptionalParams,\n  ): Promise<ContainerReleaseLeaseResponse> {\n    return this.client.sendOperationRequest(\n      { leaseId, options },\n      releaseLeaseOperationSpec,\n    );\n  }\n\n  /**\n   * [Update] establishes and manages a lock on a container for delete operations. The lock duration can\n   * be 15 to 60 seconds, or can be infinite\n   * @param leaseId Specifies the current lease ID on the resource.\n   * @param options The options parameters.\n   */\n  renewLease(\n    leaseId: string,\n    options?: ContainerRenewLeaseOptionalParams,\n  ): Promise<ContainerRenewLeaseResponse> {\n    return this.client.sendOperationRequest(\n      { leaseId, options },\n      renewLeaseOperationSpec,\n    );\n  }\n\n  /**\n   * [Update] establishes and manages a lock on a container for delete operations. The lock duration can\n   * be 15 to 60 seconds, or can be infinite\n   * @param options The options parameters.\n   */\n  breakLease(\n    options?: ContainerBreakLeaseOptionalParams,\n  ): Promise<ContainerBreakLeaseResponse> {\n    return this.client.sendOperationRequest(\n      { options },\n      breakLeaseOperationSpec,\n    );\n  }\n\n  /**\n   * [Update] establishes and manages a lock on a container for delete operations. The lock duration can\n   * be 15 to 60 seconds, or can be infinite\n   * @param leaseId Specifies the current lease ID on the resource.\n   * @param proposedLeaseId Proposed lease ID, in a GUID string format. The Blob service returns 400\n   *                        (Invalid request) if the proposed lease ID is not in the correct format. See Guid Constructor\n   *                        (String) for a list of valid GUID string formats.\n   * @param options The options parameters.\n   */\n  changeLease(\n    leaseId: string,\n    proposedLeaseId: string,\n    options?: ContainerChangeLeaseOptionalParams,\n  ): Promise<ContainerChangeLeaseResponse> {\n    return this.client.sendOperationRequest(\n      { leaseId, proposedLeaseId, options },\n      changeLeaseOperationSpec,\n    );\n  }\n\n  /**\n   * [Update] The List Blobs operation returns a list of the blobs under the specified container\n   * @param options The options parameters.\n   */\n  listBlobFlatSegment(\n    options?: ContainerListBlobFlatSegmentOptionalParams,\n  ): Promise<ContainerListBlobFlatSegmentResponse> {\n    return this.client.sendOperationRequest(\n      { options },\n      listBlobFlatSegmentOperationSpec,\n    );\n  }\n\n  /**\n   * [Update] The List Blobs operation returns a list of the blobs under the specified container\n   * @param delimiter When the request includes this parameter, the operation returns a BlobPrefix\n   *                  element in the response body that acts as a placeholder for all blobs whose names begin with the\n   *                  same substring up to the appearance of the delimiter character. The delimiter may be a single\n   *                  character or a string.\n   * @param options The options parameters.\n   */\n  listBlobHierarchySegment(\n    delimiter: string,\n    options?: ContainerListBlobHierarchySegmentOptionalParams,\n  ): Promise<ContainerListBlobHierarchySegmentResponse> {\n    return this.client.sendOperationRequest(\n      { delimiter, options },\n      listBlobHierarchySegmentOperationSpec,\n    );\n  }\n\n  /**\n   * Returns the sku name and account kind\n   * @param options The options parameters.\n   */\n  getAccountInfo(\n    options?: ContainerGetAccountInfoOptionalParams,\n  ): Promise<ContainerGetAccountInfoResponse> {\n    return this.client.sendOperationRequest(\n      { options },\n      getAccountInfoOperationSpec,\n    );\n  }\n}\n// Operation Specifications\nconst xmlSerializer = coreClient.createSerializer(Mappers, /* isXml */ true);\n\nconst createOperationSpec: coreClient.OperationSpec = {\n  path: \"/{containerName}\",\n  httpMethod: \"PUT\",\n  responses: {\n    201: {\n      headersMapper: Mappers.ContainerCreateHeaders,\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.ContainerCreateExceptionHeaders,\n    },\n  },\n  queryParameters: [Parameters.timeoutInSeconds, Parameters.restype2],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.accept1,\n    Parameters.metadata,\n    Parameters.access,\n    Parameters.defaultEncryptionScope,\n    Parameters.preventEncryptionScopeOverride,\n  ],\n  isXML: true,\n  serializer: xmlSerializer,\n};\nconst getPropertiesOperationSpec: coreClient.OperationSpec = {\n  path: \"/{containerName}\",\n  httpMethod: \"GET\",\n  responses: {\n    200: {\n      headersMapper: Mappers.ContainerGetPropertiesHeaders,\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.ContainerGetPropertiesExceptionHeaders,\n    },\n  },\n  queryParameters: [Parameters.timeoutInSeconds, Parameters.restype2],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.accept1,\n    Parameters.leaseId,\n  ],\n  isXML: true,\n  serializer: xmlSerializer,\n};\nconst deleteOperationSpec: coreClient.OperationSpec = {\n  path: \"/{containerName}\",\n  httpMethod: \"DELETE\",\n  responses: {\n    202: {\n      headersMapper: Mappers.ContainerDeleteHeaders,\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.ContainerDeleteExceptionHeaders,\n    },\n  },\n  queryParameters: [Parameters.timeoutInSeconds, Parameters.restype2],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.accept1,\n    Parameters.leaseId,\n    Parameters.ifModifiedSince,\n    Parameters.ifUnmodifiedSince,\n  ],\n  isXML: true,\n  serializer: xmlSerializer,\n};\nconst setMetadataOperationSpec: coreClient.OperationSpec = {\n  path: \"/{containerName}\",\n  httpMethod: \"PUT\",\n  responses: {\n    200: {\n      headersMapper: Mappers.ContainerSetMetadataHeaders,\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.ContainerSetMetadataExceptionHeaders,\n    },\n  },\n  queryParameters: [\n    Parameters.timeoutInSeconds,\n    Parameters.restype2,\n    Parameters.comp6,\n  ],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.accept1,\n    Parameters.metadata,\n    Parameters.leaseId,\n    Parameters.ifModifiedSince,\n  ],\n  isXML: true,\n  serializer: xmlSerializer,\n};\nconst getAccessPolicyOperationSpec: coreClient.OperationSpec = {\n  path: \"/{containerName}\",\n  httpMethod: \"GET\",\n  responses: {\n    200: {\n      bodyMapper: {\n        type: {\n          name: \"Sequence\",\n          element: {\n            type: { name: \"Composite\", className: \"SignedIdentifier\" },\n          },\n        },\n        serializedName: \"SignedIdentifiers\",\n        xmlName: \"SignedIdentifiers\",\n        xmlIsWrapped: true,\n        xmlElementName: \"SignedIdentifier\",\n      },\n      headersMapper: Mappers.ContainerGetAccessPolicyHeaders,\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.ContainerGetAccessPolicyExceptionHeaders,\n    },\n  },\n  queryParameters: [\n    Parameters.timeoutInSeconds,\n    Parameters.restype2,\n    Parameters.comp7,\n  ],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.accept1,\n    Parameters.leaseId,\n  ],\n  isXML: true,\n  serializer: xmlSerializer,\n};\nconst setAccessPolicyOperationSpec: coreClient.OperationSpec = {\n  path: \"/{containerName}\",\n  httpMethod: \"PUT\",\n  responses: {\n    200: {\n      headersMapper: Mappers.ContainerSetAccessPolicyHeaders,\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.ContainerSetAccessPolicyExceptionHeaders,\n    },\n  },\n  requestBody: Parameters.containerAcl,\n  queryParameters: [\n    Parameters.timeoutInSeconds,\n    Parameters.restype2,\n    Parameters.comp7,\n  ],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.contentType,\n    Parameters.accept,\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.access,\n    Parameters.leaseId,\n    Parameters.ifModifiedSince,\n    Parameters.ifUnmodifiedSince,\n  ],\n  isXML: true,\n  contentType: \"application/xml; charset=utf-8\",\n  mediaType: \"xml\",\n  serializer: xmlSerializer,\n};\nconst restoreOperationSpec: coreClient.OperationSpec = {\n  path: \"/{containerName}\",\n  httpMethod: \"PUT\",\n  responses: {\n    201: {\n      headersMapper: Mappers.ContainerRestoreHeaders,\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.ContainerRestoreExceptionHeaders,\n    },\n  },\n  queryParameters: [\n    Parameters.timeoutInSeconds,\n    Parameters.restype2,\n    Parameters.comp8,\n  ],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.accept1,\n    Parameters.deletedContainerName,\n    Parameters.deletedContainerVersion,\n  ],\n  isXML: true,\n  serializer: xmlSerializer,\n};\nconst renameOperationSpec: coreClient.OperationSpec = {\n  path: \"/{containerName}\",\n  httpMethod: \"PUT\",\n  responses: {\n    200: {\n      headersMapper: Mappers.ContainerRenameHeaders,\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.ContainerRenameExceptionHeaders,\n    },\n  },\n  queryParameters: [\n    Parameters.timeoutInSeconds,\n    Parameters.restype2,\n    Parameters.comp9,\n  ],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.accept1,\n    Parameters.sourceContainerName,\n    Parameters.sourceLeaseId,\n  ],\n  isXML: true,\n  serializer: xmlSerializer,\n};\nconst submitBatchOperationSpec: coreClient.OperationSpec = {\n  path: \"/{containerName}\",\n  httpMethod: \"POST\",\n  responses: {\n    202: {\n      bodyMapper: {\n        type: { name: \"Stream\" },\n        serializedName: \"parsedResponse\",\n      },\n      headersMapper: Mappers.ContainerSubmitBatchHeaders,\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.ContainerSubmitBatchExceptionHeaders,\n    },\n  },\n  requestBody: Parameters.body,\n  queryParameters: [\n    Parameters.timeoutInSeconds,\n    Parameters.comp4,\n    Parameters.restype2,\n  ],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.accept,\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.contentLength,\n    Parameters.multipartContentType,\n  ],\n  isXML: true,\n  contentType: \"application/xml; charset=utf-8\",\n  mediaType: \"xml\",\n  serializer: xmlSerializer,\n};\nconst filterBlobsOperationSpec: coreClient.OperationSpec = {\n  path: \"/{containerName}\",\n  httpMethod: \"GET\",\n  responses: {\n    200: {\n      bodyMapper: Mappers.FilterBlobSegment,\n      headersMapper: Mappers.ContainerFilterBlobsHeaders,\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.ContainerFilterBlobsExceptionHeaders,\n    },\n  },\n  queryParameters: [\n    Parameters.timeoutInSeconds,\n    Parameters.marker,\n    Parameters.maxPageSize,\n    Parameters.comp5,\n    Parameters.where,\n    Parameters.restype2,\n  ],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.accept1,\n  ],\n  isXML: true,\n  serializer: xmlSerializer,\n};\nconst acquireLeaseOperationSpec: coreClient.OperationSpec = {\n  path: \"/{containerName}\",\n  httpMethod: \"PUT\",\n  responses: {\n    201: {\n      headersMapper: Mappers.ContainerAcquireLeaseHeaders,\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.ContainerAcquireLeaseExceptionHeaders,\n    },\n  },\n  queryParameters: [\n    Parameters.timeoutInSeconds,\n    Parameters.restype2,\n    Parameters.comp10,\n  ],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.accept1,\n    Parameters.ifModifiedSince,\n    Parameters.ifUnmodifiedSince,\n    Parameters.action,\n    Parameters.duration,\n    Parameters.proposedLeaseId,\n  ],\n  isXML: true,\n  serializer: xmlSerializer,\n};\nconst releaseLeaseOperationSpec: coreClient.OperationSpec = {\n  path: \"/{containerName}\",\n  httpMethod: \"PUT\",\n  responses: {\n    200: {\n      headersMapper: Mappers.ContainerReleaseLeaseHeaders,\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.ContainerReleaseLeaseExceptionHeaders,\n    },\n  },\n  queryParameters: [\n    Parameters.timeoutInSeconds,\n    Parameters.restype2,\n    Parameters.comp10,\n  ],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.accept1,\n    Parameters.ifModifiedSince,\n    Parameters.ifUnmodifiedSince,\n    Parameters.action1,\n    Parameters.leaseId1,\n  ],\n  isXML: true,\n  serializer: xmlSerializer,\n};\nconst renewLeaseOperationSpec: coreClient.OperationSpec = {\n  path: \"/{containerName}\",\n  httpMethod: \"PUT\",\n  responses: {\n    200: {\n      headersMapper: Mappers.ContainerRenewLeaseHeaders,\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.ContainerRenewLeaseExceptionHeaders,\n    },\n  },\n  queryParameters: [\n    Parameters.timeoutInSeconds,\n    Parameters.restype2,\n    Parameters.comp10,\n  ],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.accept1,\n    Parameters.ifModifiedSince,\n    Parameters.ifUnmodifiedSince,\n    Parameters.leaseId1,\n    Parameters.action2,\n  ],\n  isXML: true,\n  serializer: xmlSerializer,\n};\nconst breakLeaseOperationSpec: coreClient.OperationSpec = {\n  path: \"/{containerName}\",\n  httpMethod: \"PUT\",\n  responses: {\n    202: {\n      headersMapper: Mappers.ContainerBreakLeaseHeaders,\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.ContainerBreakLeaseExceptionHeaders,\n    },\n  },\n  queryParameters: [\n    Parameters.timeoutInSeconds,\n    Parameters.restype2,\n    Parameters.comp10,\n  ],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.accept1,\n    Parameters.ifModifiedSince,\n    Parameters.ifUnmodifiedSince,\n    Parameters.action3,\n    Parameters.breakPeriod,\n  ],\n  isXML: true,\n  serializer: xmlSerializer,\n};\nconst changeLeaseOperationSpec: coreClient.OperationSpec = {\n  path: \"/{containerName}\",\n  httpMethod: \"PUT\",\n  responses: {\n    200: {\n      headersMapper: Mappers.ContainerChangeLeaseHeaders,\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.ContainerChangeLeaseExceptionHeaders,\n    },\n  },\n  queryParameters: [\n    Parameters.timeoutInSeconds,\n    Parameters.restype2,\n    Parameters.comp10,\n  ],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.accept1,\n    Parameters.ifModifiedSince,\n    Parameters.ifUnmodifiedSince,\n    Parameters.leaseId1,\n    Parameters.action4,\n    Parameters.proposedLeaseId1,\n  ],\n  isXML: true,\n  serializer: xmlSerializer,\n};\nconst listBlobFlatSegmentOperationSpec: coreClient.OperationSpec = {\n  path: \"/{containerName}\",\n  httpMethod: \"GET\",\n  responses: {\n    200: {\n      bodyMapper: Mappers.ListBlobsFlatSegmentResponse,\n      headersMapper: Mappers.ContainerListBlobFlatSegmentHeaders,\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.ContainerListBlobFlatSegmentExceptionHeaders,\n    },\n  },\n  queryParameters: [\n    Parameters.timeoutInSeconds,\n    Parameters.comp2,\n    Parameters.prefix,\n    Parameters.marker,\n    Parameters.maxPageSize,\n    Parameters.restype2,\n    Parameters.include1,\n  ],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.accept1,\n  ],\n  isXML: true,\n  serializer: xmlSerializer,\n};\nconst listBlobHierarchySegmentOperationSpec: coreClient.OperationSpec = {\n  path: \"/{containerName}\",\n  httpMethod: \"GET\",\n  responses: {\n    200: {\n      bodyMapper: Mappers.ListBlobsHierarchySegmentResponse,\n      headersMapper: Mappers.ContainerListBlobHierarchySegmentHeaders,\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.ContainerListBlobHierarchySegmentExceptionHeaders,\n    },\n  },\n  queryParameters: [\n    Parameters.timeoutInSeconds,\n    Parameters.comp2,\n    Parameters.prefix,\n    Parameters.marker,\n    Parameters.maxPageSize,\n    Parameters.restype2,\n    Parameters.include1,\n    Parameters.delimiter,\n  ],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.accept1,\n  ],\n  isXML: true,\n  serializer: xmlSerializer,\n};\nconst getAccountInfoOperationSpec: coreClient.OperationSpec = {\n  path: \"/{containerName}\",\n  httpMethod: \"GET\",\n  responses: {\n    200: {\n      headersMapper: Mappers.ContainerGetAccountInfoHeaders,\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.ContainerGetAccountInfoExceptionHeaders,\n    },\n  },\n  queryParameters: [\n    Parameters.comp,\n    Parameters.timeoutInSeconds,\n    Parameters.restype1,\n  ],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.accept1,\n  ],\n  isXML: true,\n  serializer: xmlSerializer,\n};\n"]}
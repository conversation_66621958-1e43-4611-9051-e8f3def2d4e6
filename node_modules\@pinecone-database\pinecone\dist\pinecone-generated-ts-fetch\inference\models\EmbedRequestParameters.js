"use strict";
/* tslint:disable */
/* eslint-disable */
/**
 * Pinecone Inference API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmbedRequestParametersToJSON = exports.EmbedRequestParametersFromJSONTyped = exports.EmbedRequestParametersFromJSON = exports.instanceOfEmbedRequestParameters = void 0;
var runtime_1 = require("../runtime");
/**
 * Check if a given object implements the EmbedRequestParameters interface.
 */
function instanceOfEmbedRequestParameters(value) {
    var isInstance = true;
    return isInstance;
}
exports.instanceOfEmbedRequestParameters = instanceOfEmbedRequestParameters;
function EmbedRequestParametersFromJSON(json) {
    return EmbedRequestParametersFromJSONTyped(json, false);
}
exports.EmbedRequestParametersFromJSON = EmbedRequestParametersFromJSON;
function EmbedRequestParametersFromJSONTyped(json, ignoreDiscriminator) {
    if ((json === undefined) || (json === null)) {
        return json;
    }
    return {
        'inputType': !(0, runtime_1.exists)(json, 'input_type') ? undefined : json['input_type'],
        'truncate': !(0, runtime_1.exists)(json, 'truncate') ? undefined : json['truncate'],
    };
}
exports.EmbedRequestParametersFromJSONTyped = EmbedRequestParametersFromJSONTyped;
function EmbedRequestParametersToJSON(value) {
    if (value === undefined) {
        return undefined;
    }
    if (value === null) {
        return null;
    }
    return {
        'input_type': value.inputType,
        'truncate': value.truncate,
    };
}
exports.EmbedRequestParametersToJSON = EmbedRequestParametersToJSON;
//# sourceMappingURL=EmbedRequestParameters.js.map
/**
 * Pinecone Data Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
/**
 * The request for the `describe_index_stats` operation.
 * @export
 * @interface DescribeIndexStatsRequest
 */
export interface DescribeIndexStatsRequest {
    /**
     * If this parameter is present, the operation only returns statistics for vectors that satisfy the filter. See [Filter with metadata](https://docs.pinecone.io/guides/data/filter-with-metadata).
     *
     * Serverless indexes do not support filtering `describe_index_stats` by metadata.
     * @type {object}
     * @memberof DescribeIndexStatsRequest
     */
    filter?: object;
}
/**
 * Check if a given object implements the DescribeIndexStatsRequest interface.
 */
export declare function instanceOfDescribeIndexStatsRequest(value: object): boolean;
export declare function DescribeIndexStatsRequestFromJSON(json: any): DescribeIndexStatsRequest;
export declare function DescribeIndexStatsRequestFromJSONTyped(json: any, ignoreDiscriminator: boolean): DescribeIndexStatsRequest;
export declare function DescribeIndexStatsRequestToJSON(value?: DescribeIndexStatsRequest | null): any;

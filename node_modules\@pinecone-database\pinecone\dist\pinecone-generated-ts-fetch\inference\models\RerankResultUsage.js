"use strict";
/* tslint:disable */
/* eslint-disable */
/**
 * Pinecone Inference API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.RerankResultUsageToJSON = exports.RerankResultUsageFromJSONTyped = exports.RerankResultUsageFromJSON = exports.instanceOfRerankResultUsage = void 0;
var runtime_1 = require("../runtime");
/**
 * Check if a given object implements the RerankResultUsage interface.
 */
function instanceOfRerankResultUsage(value) {
    var isInstance = true;
    return isInstance;
}
exports.instanceOfRerankResultUsage = instanceOfRerankResultUsage;
function RerankResultUsageFromJSON(json) {
    return RerankResultUsageFromJSONTyped(json, false);
}
exports.RerankResultUsageFromJSON = RerankResultUsageFromJSON;
function RerankResultUsageFromJSONTyped(json, ignoreDiscriminator) {
    if ((json === undefined) || (json === null)) {
        return json;
    }
    return {
        'rerankUnits': !(0, runtime_1.exists)(json, 'rerank_units') ? undefined : json['rerank_units'],
    };
}
exports.RerankResultUsageFromJSONTyped = RerankResultUsageFromJSONTyped;
function RerankResultUsageToJSON(value) {
    if (value === undefined) {
        return undefined;
    }
    if (value === null) {
        return null;
    }
    return {
        'rerank_units': value.rerankUnits,
    };
}
exports.RerankResultUsageToJSON = RerankResultUsageToJSON;
//# sourceMappingURL=RerankResultUsage.js.map
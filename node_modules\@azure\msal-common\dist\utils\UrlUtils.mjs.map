{"version": 3, "file": "UrlUtils.mjs", "sources": ["../../src/utils/UrlUtils.ts"], "sourcesContent": [null], "names": ["ClientAuthErrorCodes.hashNotDeserialized"], "mappings": ";;;;;AAAA;;;AAGG;AAQH;;;AAGG;AACG,SAAU,uBAAuB,CAAC,cAAsB,EAAA;AAC1D,IAAA,IAAI,cAAc,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;AACjC,QAAA,OAAO,cAAc,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;AACtC,KAAA;AAAM,SAAA,IACH,cAAc,CAAC,UAAU,CAAC,GAAG,CAAC;AAC9B,QAAA,cAAc,CAAC,UAAU,CAAC,GAAG,CAAC,EAChC;AACE,QAAA,OAAO,cAAc,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;AACtC,KAAA;AAED,IAAA,OAAO,cAAc,CAAC;AAC1B,CAAC;AAED;;AAEG;AACG,SAAU,uBAAuB,CACnC,cAAsB,EAAA;;IAGtB,IAAI,CAAC,cAAc,IAAI,cAAc,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;AACpD,QAAA,OAAO,IAAI,CAAC;AACf,KAAA;IACD,IAAI;;AAEA,QAAA,MAAM,kBAAkB,GAAG,uBAAuB,CAAC,cAAc,CAAC,CAAC;;AAEnE,QAAA,MAAM,gBAAgB,GAClB,MAAM,CAAC,WAAW,CAAC,IAAI,eAAe,CAAC,kBAAkB,CAAC,CAAC,CAAC;;QAGhE,IACI,gBAAgB,CAAC,IAAI;AACrB,YAAA,gBAAgB,CAAC,KAAK;AACtB,YAAA,gBAAgB,CAAC,iBAAiB;YAClC,gBAAgB,CAAC,KAAK,EACxB;AACE,YAAA,OAAO,gBAAgB,CAAC;AAC3B,SAAA;AACJ,KAAA;AAAC,IAAA,OAAO,CAAC,EAAE;AACR,QAAA,MAAM,qBAAqB,CAACA,mBAAwC,CAAC,CAAC;AACzE,KAAA;AAED,IAAA,OAAO,IAAI,CAAC;AAChB;;;;"}
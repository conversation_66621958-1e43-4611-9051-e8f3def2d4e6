{"version": 3, "file": "configureIndex.test.js", "sourceRoot": "", "sources": ["../../../src/integration/control/configureIndex.test.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,uCAIsB;AACtB,qCAAuC;AACvC,gDAKyB;AAEzB,IAAI,YAAoB,EAAE,mBAA2B,EAAE,QAAkB,CAAC;AAE1E,QAAQ,CAAC,iBAAiB,EAAE;IAC1B,SAAS,CAAC;;;;oBACR,QAAQ,GAAG,IAAI,gBAAQ,EAAE,CAAC;oBAC1B,YAAY,GAAG,IAAA,8BAAe,EAAC,eAAe,CAAC,CAAC;oBAChD,mBAAmB,GAAG,IAAA,8BAAe,EAAC,sBAAsB,CAAC,CAAC;oBAE9D,mBAAmB;oBACnB,qBAAM,QAAQ,CAAC,WAAW,CAAC;4BACzB,IAAI,EAAE,YAAY;4BAClB,SAAS,EAAE,CAAC;4BACZ,MAAM,EAAE,QAAQ;4BAChB,IAAI,EAAE;gCACJ,GAAG,EAAE;oCACH,WAAW,EAAE,cAAc;oCAC3B,OAAO,EAAE,OAAO;oCAChB,IAAI,EAAE,CAAC;iCACR;6BACF;4BACD,cAAc,EAAE,IAAI;yBACrB,CAAC,EAAA;;oBAbF,mBAAmB;oBACnB,SAYE,CAAC;oBAEH,0BAA0B;oBAC1B,qBAAM,QAAQ,CAAC,WAAW,CAAC;4BACzB,IAAI,EAAE,mBAAmB;4BACzB,SAAS,EAAE,CAAC;4BACZ,MAAM,EAAE,QAAQ;4BAChB,IAAI,EAAE;gCACJ,UAAU,EAAE;oCACV,KAAK,EAAE,KAAK;oCACZ,MAAM,EAAE,WAAW;iCACpB;6BACF;4BACD,cAAc,EAAE,IAAI;yBACrB,CAAC,EAAA;;oBAZF,0BAA0B;oBAC1B,SAWE,CAAC;;;;SACJ,CAAC,CAAC;IAEH,QAAQ,CAAC;;;;gBACP,+GAA+G;gBAC/G,8BAA8B;gBAC9B,qBAAM,IAAA,2BAAY,EAAC,QAAQ,EAAE,YAAY,CAAC,EAAA;;oBAF1C,+GAA+G;oBAC/G,8BAA8B;oBAC9B,SAA0C,CAAC;oBAC3C,qBAAM,IAAA,2BAAY,EAAC,QAAQ,EAAE,mBAAmB,CAAC,EAAA;;oBAAjD,SAAiD,CAAC;;;;SACnD,CAAC,CAAC;IAEH,QAAQ,CAAC,WAAW,EAAE;QACpB,IAAI,CAAC,mBAAmB,EAAE;;;;;4BACJ,qBAAM,QAAQ,CAAC,aAAa,CAAC,YAAY,CAAC,EAAA;;wBAAxD,WAAW,GAAG,SAA0C;wBAC9D,MAAM,CAAC,MAAA,WAAW,CAAC,IAAI,CAAC,GAAG,0CAAE,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;wBAElD,qBAAM,QAAQ,CAAC,cAAc,CAAC,YAAY,EAAE;gCAC1C,IAAI,EAAE,EAAE,GAAG,EAAE,EAAE,QAAQ,EAAE,CAAC,EAAE,EAAE;6BAC/B,CAAC,EAAA;;wBAFF,SAEE,CAAC;wBACkB,qBAAM,QAAQ,CAAC,aAAa,CAAC,YAAY,CAAC,EAAA;;wBAAzD,YAAY,GAAG,SAA0C;wBAC/D,MAAM,CAAC,MAAA,YAAY,CAAC,IAAI,CAAC,GAAG,0CAAE,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;;;;aACpD,CAAC,CAAC;QAEH,IAAI,CAAC,kBAAkB,EAAE;;;;;4BAEH,qBAAM,QAAQ,CAAC,aAAa,CAAC,YAAY,CAAC,EAAA;;wBAAxD,WAAW,GAAG,SAA0C;wBAC9D,MAAM,CAAC,MAAA,WAAW,CAAC,IAAI,CAAC,GAAG,0CAAE,OAAO,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;wBAGnD,KAAK,GAAG,IAAI,CAAC;wBACb,UAAU,GAAG,CAAC,CAAC;wBACb,UAAU,GAAG,EAAE,CAAC;;;6BACf,CAAA,KAAK,IAAI,UAAU,GAAG,UAAU,CAAA;;;;wBAEnC,qBAAM,QAAQ,CAAC,cAAc,CAAC,YAAY,EAAE;gCAC1C,IAAI,EAAE,EAAE,GAAG,EAAE,EAAE,OAAO,EAAE,OAAO,EAAE,EAAE;6BACpC,CAAC,EAAA;;wBAFF,SAEE,CAAC;wBACH,KAAK,GAAG,KAAK,CAAC;;;;6BAEV,CAAA,GAAC,YAAY,oCAA2B,CAAA,EAAxC,wBAAwC;wBAC1C,UAAU,EAAE,CAAC;wBACb,qBAAM,IAAA,oBAAK,EAAC,IAAI,CAAC,EAAA;;wBAAjB,SAAiB,CAAC;;;wBAElB,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE,GAAC,CAAC,CAAC;wBACpC,MAAM,GAAC,CAAC;;;6BAId,qBAAM,IAAA,6BAAc,EAAC,YAAY,CAAC,EAAA;;wBAAlC,SAAkC,CAAC;wBACd,qBAAM,QAAQ,CAAC,aAAa,CAAC,YAAY,CAAC,EAAA;;wBAAzD,YAAY,GAAG,SAA0C;wBAC/D,MAAM,CAAC,MAAA,YAAY,CAAC,IAAI,CAAC,GAAG,0CAAE,OAAO,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;;;;aACzD,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE;QAC3B,IAAI,CAAC,uCAAuC,EAAE;;;4BAC5C,qBAAM,QAAQ,CAAC,cAAc,CAAC,mBAAmB,EAAE;4BACjD,kBAAkB,EAAE,SAAS;yBAC9B,CAAC,EAAA;;wBAFF,SAEE,CAAC;wBACH,qBAAM,IAAA,6BAAc,EAAC,mBAAmB,CAAC,EAAA;;wBAAzC,SAAyC,CAAC;wBAE1C,oCAAoC;wBACpC,qBAAM,QAAQ,CAAC,WAAW,CAAC,mBAAmB,CAAC,CAAC,KAAK,CAAC,UAAC,CAAC;gCACtD,IAAM,GAAG,GAAG,CAA4B,CAAC;gCACzC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,yBAAyB,CAAC,CAAC;gCACpD,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,SAAS,CAC3B,+CAA+C,CAChD,CAAC;4BACJ,CAAC,CAAC,EAAA;;wBAPF,oCAAoC;wBACpC,SAME,CAAC;wBAEH,uCAAuC;wBACvC,qBAAM,QAAQ,CAAC,cAAc,CAAC,mBAAmB,EAAE;gCACjD,kBAAkB,EAAE,UAAU;6BAC/B,CAAC,EAAA;;wBAHF,uCAAuC;wBACvC,SAEE,CAAC;;;;aACJ,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,aAAa,EAAE;QACtB,IAAI,CAAC,gDAAgD,EAAE;;;;;;wBAEnD,qBAAM,QAAQ,CAAC,cAAc,CAAC,oBAAoB,EAAE;gCAClD,IAAI,EAAE,EAAE,GAAG,EAAE,EAAE,QAAQ,EAAE,CAAC,EAAE,EAAE;6BAC/B,CAAC,EAAA;;wBAFF,SAEE,CAAC;;;;wBAEG,GAAG,GAAG,GAAsB,CAAC;wBACnC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,uBAAuB,CAAC,CAAC;;;;;aAErD,CAAC,CAAC;QAEH,IAAI,CAAC,6CAA6C,EAAE;;;;;;wBAEhD,qBAAM,QAAQ,CAAC,cAAc,CAAC,YAAY,EAAE;gCAC1C,IAAI,EAAE,EAAE,GAAG,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE,EAAE;6BAChC,CAAC,EAAA;;wBAFF,SAEE,CAAC;;;;wBAEG,GAAG,GAAG,GAAsB,CAAC;wBACnC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,yBAAyB,CAAC,CAAC;wBACpD,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,SAAS,CAC3B,gDAAgD,CACjD,CAAC;wBACF,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,SAAS,CAC3B,qEAAqE,CACtE,CAAC;;;;;aAEL,CAAC,CAAC;QAEH,IAAI,CAAC,6BAA6B,EAAE;;;;;;wBAEhC,kCAAkC;wBAClC,qBAAM,QAAQ,CAAC,cAAc,CAAC,YAAY,EAAE;gCAC1C,IAAI,EAAE,EAAE,GAAG,EAAE,EAAE,OAAO,EAAE,OAAO,EAAE,EAAE;6BACpC,CAAC,EAAA;;wBAHF,kCAAkC;wBAClC,SAEE,CAAC;;;;wBAEG,GAAG,GAAG,GAAsB,CAAC;wBACnC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,yBAAyB,CAAC,CAAC;wBACpD,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,qCAAqC,CAAC,CAAC;;;;;aAExE,CAAC,CAAC;QAEH,IAAI,CAAC,mEAAmE,EAAE;;;;;;wBAEtE,qBAAM,QAAQ,CAAC,cAAc,CAAC,mBAAmB,EAAE;gCACjD,mBAAmB;gCACnB,kBAAkB,EAAE,OAAO;6BAC5B,CAAC,EAAA;;wBAHF,SAGE,CAAC;;;;wBAEG,GAAG,GAAG,GAAsB,CAAC;wBACnC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,yBAAyB,CAAC,CAAC;wBACpD,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,SAAS,CAC3B,yEAAyE,CAC1E,CAAC;;;;;aAEL,CAAC,CAAC;QAEH,IAAI,CAAC,0CAA0C,EAAE;;;;;;wBAE7C,qBAAM,QAAQ,CAAC,cAAc,CAAC,mBAAmB,EAAE;gCACjD,IAAI,EAAE,EAAE,GAAG,EAAE,EAAE,QAAQ,EAAE,CAAC,EAAE,EAAE;6BAC/B,CAAC,EAAA;;wBAFF,SAEE,CAAC;;;;wBAEG,GAAG,GAAG,GAAsB,CAAC;wBACnC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,yBAAyB,CAAC,CAAC;wBACpD,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,SAAS,CAC3B,mEAAmE,CACpE,CAAC;;;;;aAEL,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}
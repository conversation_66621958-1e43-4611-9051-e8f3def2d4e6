{"version": 3, "file": "blob.js", "sourceRoot": "", "sources": ["../../../../../../src/generated/src/operations/blob.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAGH,OAAO,KAAK,UAAU,MAAM,oBAAoB,CAAC;AACjD,OAAO,KAAK,OAAO,MAAM,mBAAmB,CAAC;AAC7C,OAAO,KAAK,UAAU,MAAM,sBAAsB,CAAC;AAuDnD,wCAAwC;AACxC,MAAM,OAAO,QAAQ;IAGnB;;;OAGG;IACH,YAAY,MAAqB;QAC/B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IAED;;;;OAIG;IACH,QAAQ,CACN,OAAoC;QAEpC,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,EAAE,OAAO,EAAE,EAAE,qBAAqB,CAAC,CAAC;IAC9E,CAAC;IAED;;;;OAIG;IACH,aAAa,CACX,OAAyC;QAEzC,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,OAAO,EAAE,EACX,0BAA0B,CAC3B,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;;;OAcG;IACH,MAAM,CAAC,OAAkC;QACvC,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,EAAE,OAAO,EAAE,EAAE,mBAAmB,CAAC,CAAC;IAC5E,CAAC;IAED;;;OAGG;IACH,QAAQ,CACN,OAAoC;QAEpC,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,EAAE,OAAO,EAAE,EAAE,qBAAqB,CAAC,CAAC;IAC9E,CAAC;IAED;;;;OAIG;IACH,SAAS,CACP,aAAgC,EAChC,OAAqC;QAErC,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,aAAa,EAAE,OAAO,EAAE,EAC1B,sBAAsB,CACvB,CAAC;IACJ,CAAC;IAED;;;OAGG;IACH,cAAc,CACZ,OAA0C;QAE1C,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,OAAO,EAAE,EACX,2BAA2B,CAC5B,CAAC;IACJ,CAAC;IAED;;;OAGG;IACH,qBAAqB,CACnB,OAAiD;QAEjD,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,OAAO,EAAE,EACX,kCAAkC,CACnC,CAAC;IACJ,CAAC;IAED;;;OAGG;IACH,wBAAwB,CACtB,OAAoD;QAEpD,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,OAAO,EAAE,EACX,qCAAqC,CACtC,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACH,YAAY,CACV,SAAkB,EAClB,OAAwC;QAExC,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,SAAS,EAAE,OAAO,EAAE,EACtB,yBAAyB,CAC1B,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACH,WAAW,CACT,OAAuC;QAEvC,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,OAAO,EAAE,EACX,wBAAwB,CACzB,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACH,YAAY,CACV,OAAwC;QAExC,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,OAAO,EAAE,EACX,yBAAyB,CAC1B,CAAC;IACJ,CAAC;IAED;;;;;OAKG;IACH,YAAY,CACV,OAAe,EACf,OAAwC;QAExC,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,OAAO,EAAE,OAAO,EAAE,EACpB,yBAAyB,CAC1B,CAAC;IACJ,CAAC;IAED;;;;;OAKG;IACH,UAAU,CACR,OAAe,EACf,OAAsC;QAEtC,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,OAAO,EAAE,OAAO,EAAE,EACpB,uBAAuB,CACxB,CAAC;IACJ,CAAC;IAED;;;;;;;;OAQG;IACH,WAAW,CACT,OAAe,EACf,eAAuB,EACvB,OAAuC;QAEvC,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,OAAO,EAAE,eAAe,EAAE,OAAO,EAAE,EACrC,wBAAwB,CACzB,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACH,UAAU,CACR,OAAsC;QAEtC,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,OAAO,EAAE,EACX,uBAAuB,CACxB,CAAC;IACJ,CAAC;IAED;;;OAGG;IACH,cAAc,CACZ,OAA0C;QAE1C,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,OAAO,EAAE,EACX,2BAA2B,CAC5B,CAAC;IACJ,CAAC;IAED;;;;;;;OAOG;IACH,gBAAgB,CACd,UAAkB,EAClB,OAA4C;QAE5C,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,UAAU,EAAE,OAAO,EAAE,EACvB,6BAA6B,CAC9B,CAAC;IACJ,CAAC;IAED;;;;;;;;OAQG;IACH,WAAW,CACT,UAAkB,EAClB,OAAuC;QAEvC,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,UAAU,EAAE,OAAO,EAAE,EACvB,wBAAwB,CACzB,CAAC;IACJ,CAAC;IAED;;;;;;OAMG;IACH,gBAAgB,CACd,MAAc,EACd,OAA4C;QAE5C,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,MAAM,EAAE,OAAO,EAAE,EACnB,6BAA6B,CAC9B,CAAC;IACJ,CAAC;IAED;;;;;;;;OAQG;IACH,OAAO,CACL,IAAgB,EAChB,OAAmC;QAEnC,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,IAAI,EAAE,OAAO,EAAE,EACjB,oBAAoB,CACrB,CAAC;IACJ,CAAC;IAED;;;OAGG;IACH,cAAc,CACZ,OAA0C;QAE1C,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,OAAO,EAAE,EACX,2BAA2B,CAC5B,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,OAAiC;QACrC,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,EAAE,OAAO,EAAE,EAAE,kBAAkB,CAAC,CAAC;IAC3E,CAAC;IAED;;;OAGG;IACH,OAAO,CAAC,OAAmC;QACzC,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,EAAE,OAAO,EAAE,EAAE,oBAAoB,CAAC,CAAC;IAC7E,CAAC;IAED;;;OAGG;IACH,OAAO,CAAC,OAAmC;QACzC,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,EAAE,OAAO,EAAE,EAAE,oBAAoB,CAAC,CAAC;IAC7E,CAAC;CACF;AACD,2BAA2B;AAC3B,MAAM,aAAa,GAAG,UAAU,CAAC,gBAAgB,CAAC,OAAO,EAAE,WAAW,CAAC,IAAI,CAAC,CAAC;AAE7E,MAAM,qBAAqB,GAA6B;IACtD,IAAI,EAAE,yBAAyB;IAC/B,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,UAAU,EAAE;gBACV,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;gBACxB,cAAc,EAAE,gBAAgB;aACjC;YACD,aAAa,EAAE,OAAO,CAAC,mBAAmB;SAC3C;QACD,GAAG,EAAE;YACH,UAAU,EAAE;gBACV,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;gBACxB,cAAc,EAAE,gBAAgB;aACjC;YACD,aAAa,EAAE,OAAO,CAAC,mBAAmB;SAC3C;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,4BAA4B;SACpD;KACF;IACD,eAAe,EAAE;QACf,UAAU,CAAC,gBAAgB;QAC3B,UAAU,CAAC,QAAQ;QACnB,UAAU,CAAC,SAAS;KACrB;IACD,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,eAAe;QAC1B,UAAU,CAAC,iBAAiB;QAC5B,UAAU,CAAC,KAAK;QAChB,UAAU,CAAC,kBAAkB;QAC7B,UAAU,CAAC,oBAAoB;QAC/B,UAAU,CAAC,aAAa;QACxB,UAAU,CAAC,mBAAmB;QAC9B,UAAU,CAAC,mBAAmB;QAC9B,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,MAAM;KAClB;IACD,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,0BAA0B,GAA6B;IAC3D,IAAI,EAAE,yBAAyB;IAC/B,UAAU,EAAE,MAAM;IAClB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,aAAa,EAAE,OAAO,CAAC,wBAAwB;SAChD;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,iCAAiC;SACzD;KACF;IACD,eAAe,EAAE;QACf,UAAU,CAAC,gBAAgB;QAC3B,UAAU,CAAC,QAAQ;QACnB,UAAU,CAAC,SAAS;KACrB;IACD,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,eAAe;QAC1B,UAAU,CAAC,iBAAiB;QAC5B,UAAU,CAAC,aAAa;QACxB,UAAU,CAAC,mBAAmB;QAC9B,UAAU,CAAC,mBAAmB;QAC9B,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,MAAM;KAClB;IACD,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,mBAAmB,GAA6B;IACpD,IAAI,EAAE,yBAAyB;IAC/B,UAAU,EAAE,QAAQ;IACpB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,aAAa,EAAE,OAAO,CAAC,iBAAiB;SACzC;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,0BAA0B;SAClD;KACF;IACD,eAAe,EAAE;QACf,UAAU,CAAC,gBAAgB;QAC3B,UAAU,CAAC,QAAQ;QACnB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,cAAc;KAC1B;IACD,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,eAAe;QAC1B,UAAU,CAAC,iBAAiB;QAC5B,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,MAAM;QACjB,UAAU,CAAC,eAAe;KAC3B;IACD,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,qBAAqB,GAA6B;IACtD,IAAI,EAAE,yBAAyB;IAC/B,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,aAAa,EAAE,OAAO,CAAC,mBAAmB;SAC3C;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,4BAA4B;SACpD;KACF;IACD,eAAe,EAAE,CAAC,UAAU,CAAC,gBAAgB,EAAE,UAAU,CAAC,KAAK,CAAC;IAChE,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,OAAO;KACnB;IACD,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,sBAAsB,GAA6B;IACvD,IAAI,EAAE,yBAAyB;IAC/B,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,aAAa,EAAE,OAAO,CAAC,oBAAoB;SAC5C;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,6BAA6B;SACrD;KACF;IACD,eAAe,EAAE,CAAC,UAAU,CAAC,gBAAgB,EAAE,UAAU,CAAC,MAAM,CAAC;IACjE,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,aAAa;QACxB,UAAU,CAAC,SAAS;KACrB;IACD,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,2BAA2B,GAA6B;IAC5D,IAAI,EAAE,yBAAyB;IAC/B,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,aAAa,EAAE,OAAO,CAAC,yBAAyB;SACjD;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,kCAAkC;SAC1D;KACF;IACD,eAAe,EAAE,CAAC,UAAU,CAAC,IAAI,EAAE,UAAU,CAAC,gBAAgB,CAAC;IAC/D,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,eAAe;QAC1B,UAAU,CAAC,iBAAiB;QAC5B,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,MAAM;QACjB,UAAU,CAAC,gBAAgB;QAC3B,UAAU,CAAC,eAAe;QAC1B,UAAU,CAAC,cAAc;QACzB,UAAU,CAAC,mBAAmB;QAC9B,UAAU,CAAC,mBAAmB;QAC9B,UAAU,CAAC,sBAAsB;KAClC;IACD,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,kCAAkC,GAA6B;IACnE,IAAI,EAAE,yBAAyB;IAC/B,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,aAAa,EAAE,OAAO,CAAC,gCAAgC;SACxD;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,yCAAyC;SACjE;KACF;IACD,eAAe,EAAE;QACf,UAAU,CAAC,gBAAgB;QAC3B,UAAU,CAAC,QAAQ;QACnB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,MAAM;KAClB;IACD,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,iBAAiB;QAC5B,UAAU,CAAC,wBAAwB;QACnC,UAAU,CAAC,sBAAsB;KAClC;IACD,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,qCAAqC,GAA6B;IACtE,IAAI,EAAE,yBAAyB;IAC/B,UAAU,EAAE,QAAQ;IACpB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,aAAa,EAAE,OAAO,CAAC,mCAAmC;SAC3D;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,4CAA4C;SACpE;KACF;IACD,eAAe,EAAE;QACf,UAAU,CAAC,gBAAgB;QAC3B,UAAU,CAAC,QAAQ;QACnB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,MAAM;KAClB;IACD,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,OAAO;KACnB;IACD,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,yBAAyB,GAA6B;IAC1D,IAAI,EAAE,yBAAyB;IAC/B,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,aAAa,EAAE,OAAO,CAAC,uBAAuB;SAC/C;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,gCAAgC;SACxD;KACF;IACD,eAAe,EAAE;QACf,UAAU,CAAC,gBAAgB;QAC3B,UAAU,CAAC,QAAQ;QACnB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,MAAM;KAClB;IACD,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;KACrB;IACD,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,wBAAwB,GAA6B;IACzD,IAAI,EAAE,yBAAyB;IAC/B,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,aAAa,EAAE,OAAO,CAAC,sBAAsB;SAC9C;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,+BAA+B;SACvD;KACF;IACD,eAAe,EAAE,CAAC,UAAU,CAAC,gBAAgB,EAAE,UAAU,CAAC,KAAK,CAAC;IAChE,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,QAAQ;QACnB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,eAAe;QAC1B,UAAU,CAAC,iBAAiB;QAC5B,UAAU,CAAC,aAAa;QACxB,UAAU,CAAC,mBAAmB;QAC9B,UAAU,CAAC,mBAAmB;QAC9B,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,MAAM;QACjB,UAAU,CAAC,eAAe;KAC3B;IACD,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,yBAAyB,GAA6B;IAC1D,IAAI,EAAE,yBAAyB;IAC/B,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,aAAa,EAAE,OAAO,CAAC,uBAAuB;SAC/C;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,gCAAgC;SACxD;KACF;IACD,eAAe,EAAE,CAAC,UAAU,CAAC,gBAAgB,EAAE,UAAU,CAAC,MAAM,CAAC;IACjE,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,eAAe;QAC1B,UAAU,CAAC,iBAAiB;QAC5B,UAAU,CAAC,MAAM;QACjB,UAAU,CAAC,QAAQ;QACnB,UAAU,CAAC,eAAe;QAC1B,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,MAAM;KAClB;IACD,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,yBAAyB,GAA6B;IAC1D,IAAI,EAAE,yBAAyB;IAC/B,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,aAAa,EAAE,OAAO,CAAC,uBAAuB;SAC/C;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,gCAAgC;SACxD;KACF;IACD,eAAe,EAAE,CAAC,UAAU,CAAC,gBAAgB,EAAE,UAAU,CAAC,MAAM,CAAC;IACjE,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,eAAe;QAC1B,UAAU,CAAC,iBAAiB;QAC5B,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,QAAQ;QACnB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,MAAM;KAClB;IACD,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,uBAAuB,GAA6B;IACxD,IAAI,EAAE,yBAAyB;IAC/B,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,aAAa,EAAE,OAAO,CAAC,qBAAqB;SAC7C;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,8BAA8B;SACtD;KACF;IACD,eAAe,EAAE,CAAC,UAAU,CAAC,gBAAgB,EAAE,UAAU,CAAC,MAAM,CAAC;IACjE,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,eAAe;QAC1B,UAAU,CAAC,iBAAiB;QAC5B,UAAU,CAAC,QAAQ;QACnB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,MAAM;KAClB;IACD,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,wBAAwB,GAA6B;IACzD,IAAI,EAAE,yBAAyB;IAC/B,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,aAAa,EAAE,OAAO,CAAC,sBAAsB;SAC9C;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,+BAA+B;SACvD;KACF;IACD,eAAe,EAAE,CAAC,UAAU,CAAC,gBAAgB,EAAE,UAAU,CAAC,MAAM,CAAC;IACjE,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,eAAe;QAC1B,UAAU,CAAC,iBAAiB;QAC5B,UAAU,CAAC,QAAQ;QACnB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,gBAAgB;QAC3B,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,MAAM;KAClB;IACD,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,uBAAuB,GAA6B;IACxD,IAAI,EAAE,yBAAyB;IAC/B,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,aAAa,EAAE,OAAO,CAAC,qBAAqB;SAC7C;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,8BAA8B;SACtD;KACF;IACD,eAAe,EAAE,CAAC,UAAU,CAAC,gBAAgB,EAAE,UAAU,CAAC,MAAM,CAAC;IACjE,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,eAAe;QAC1B,UAAU,CAAC,iBAAiB;QAC5B,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,MAAM;KAClB;IACD,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,2BAA2B,GAA6B;IAC5D,IAAI,EAAE,yBAAyB;IAC/B,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,aAAa,EAAE,OAAO,CAAC,yBAAyB;SACjD;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,kCAAkC;SAC1D;KACF;IACD,eAAe,EAAE,CAAC,UAAU,CAAC,gBAAgB,EAAE,UAAU,CAAC,MAAM,CAAC;IACjE,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,QAAQ;QACnB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,eAAe;QAC1B,UAAU,CAAC,iBAAiB;QAC5B,UAAU,CAAC,aAAa;QACxB,UAAU,CAAC,mBAAmB;QAC9B,UAAU,CAAC,mBAAmB;QAC9B,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,MAAM;QACjB,UAAU,CAAC,eAAe;KAC3B;IACD,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,6BAA6B,GAA6B;IAC9D,IAAI,EAAE,yBAAyB;IAC/B,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,aAAa,EAAE,OAAO,CAAC,2BAA2B;SACnD;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,oCAAoC;SAC5D;KACF;IACD,eAAe,EAAE,CAAC,UAAU,CAAC,gBAAgB,CAAC;IAC9C,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,QAAQ;QACnB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,eAAe;QAC1B,UAAU,CAAC,iBAAiB;QAC5B,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,MAAM;QACjB,UAAU,CAAC,wBAAwB;QACnC,UAAU,CAAC,sBAAsB;QACjC,UAAU,CAAC,IAAI;QACf,UAAU,CAAC,iBAAiB;QAC5B,UAAU,CAAC,qBAAqB;QAChC,UAAU,CAAC,uBAAuB;QAClC,UAAU,CAAC,aAAa;QACxB,UAAU,CAAC,iBAAiB;QAC5B,UAAU,CAAC,YAAY;QACvB,UAAU,CAAC,UAAU;QACrB,UAAU,CAAC,cAAc;QACzB,UAAU,CAAC,QAAQ;QACnB,UAAU,CAAC,UAAU;KACtB;IACD,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,wBAAwB,GAA6B;IACzD,IAAI,EAAE,yBAAyB;IAC/B,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,aAAa,EAAE,OAAO,CAAC,sBAAsB;SAC9C;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,+BAA+B;SACvD;KACF;IACD,eAAe,EAAE,CAAC,UAAU,CAAC,gBAAgB,CAAC;IAC9C,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,QAAQ;QACnB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,eAAe;QAC1B,UAAU,CAAC,iBAAiB;QAC5B,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,MAAM;QACjB,UAAU,CAAC,wBAAwB;QACnC,UAAU,CAAC,sBAAsB;QACjC,UAAU,CAAC,eAAe;QAC1B,UAAU,CAAC,IAAI;QACf,UAAU,CAAC,qBAAqB;QAChC,UAAU,CAAC,uBAAuB;QAClC,UAAU,CAAC,aAAa;QACxB,UAAU,CAAC,iBAAiB;QAC5B,UAAU,CAAC,UAAU;QACrB,UAAU,CAAC,cAAc;QACzB,UAAU,CAAC,UAAU;QACrB,UAAU,CAAC,eAAe;QAC1B,UAAU,CAAC,gBAAgB;QAC3B,UAAU,CAAC,uBAAuB;QAClC,UAAU,CAAC,cAAc;KAC1B;IACD,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,6BAA6B,GAA6B;IAC9D,IAAI,EAAE,yBAAyB;IAC/B,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,aAAa,EAAE,OAAO,CAAC,2BAA2B;SACnD;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,oCAAoC;SAC5D;KACF;IACD,eAAe,EAAE;QACf,UAAU,CAAC,gBAAgB;QAC3B,UAAU,CAAC,MAAM;QACjB,UAAU,CAAC,MAAM;KAClB;IACD,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,uBAAuB;KACnC;IACD,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,oBAAoB,GAA6B;IACrD,IAAI,EAAE,yBAAyB;IAC/B,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,aAAa,EAAE,OAAO,CAAC,kBAAkB;SAC1C;QACD,GAAG,EAAE;YACH,aAAa,EAAE,OAAO,CAAC,kBAAkB;SAC1C;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,2BAA2B;SACnD;KACF;IACD,eAAe,EAAE;QACf,UAAU,CAAC,gBAAgB;QAC3B,UAAU,CAAC,QAAQ;QACnB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,MAAM;KAClB;IACD,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,MAAM;QACjB,UAAU,CAAC,iBAAiB;QAC5B,UAAU,CAAC,KAAK;KACjB;IACD,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,2BAA2B,GAA6B;IAC5D,IAAI,EAAE,yBAAyB;IAC/B,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,aAAa,EAAE,OAAO,CAAC,yBAAyB;SACjD;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,kCAAkC;SAC1D;KACF;IACD,eAAe,EAAE;QACf,UAAU,CAAC,IAAI;QACf,UAAU,CAAC,gBAAgB;QAC3B,UAAU,CAAC,QAAQ;KACpB;IACD,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,OAAO;KACnB;IACD,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,kBAAkB,GAA6B;IACnD,IAAI,EAAE,yBAAyB;IAC/B,UAAU,EAAE,MAAM;IAClB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,UAAU,EAAE;gBACV,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;gBACxB,cAAc,EAAE,gBAAgB;aACjC;YACD,aAAa,EAAE,OAAO,CAAC,gBAAgB;SACxC;QACD,GAAG,EAAE;YACH,UAAU,EAAE;gBACV,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;gBACxB,cAAc,EAAE,gBAAgB;aACjC;YACD,aAAa,EAAE,OAAO,CAAC,gBAAgB;SACxC;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,yBAAyB;SACjD;KACF;IACD,WAAW,EAAE,UAAU,CAAC,YAAY;IACpC,eAAe,EAAE;QACf,UAAU,CAAC,gBAAgB;QAC3B,UAAU,CAAC,QAAQ;QACnB,UAAU,CAAC,MAAM;KAClB;IACD,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,MAAM;QACjB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,eAAe;QAC1B,UAAU,CAAC,iBAAiB;QAC5B,UAAU,CAAC,aAAa;QACxB,UAAU,CAAC,mBAAmB;QAC9B,UAAU,CAAC,mBAAmB;QAC9B,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,MAAM;KAClB;IACD,KAAK,EAAE,IAAI;IACX,WAAW,EAAE,gCAAgC;IAC7C,SAAS,EAAE,KAAK;IAChB,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,oBAAoB,GAA6B;IACrD,IAAI,EAAE,yBAAyB;IAC/B,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,UAAU,EAAE,OAAO,CAAC,QAAQ;YAC5B,aAAa,EAAE,OAAO,CAAC,kBAAkB;SAC1C;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,2BAA2B;SACnD;KACF;IACD,eAAe,EAAE;QACf,UAAU,CAAC,gBAAgB;QAC3B,UAAU,CAAC,QAAQ;QACnB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,MAAM;KAClB;IACD,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,MAAM;KAClB;IACD,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,oBAAoB,GAA6B;IACrD,IAAI,EAAE,yBAAyB;IAC/B,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,aAAa,EAAE,OAAO,CAAC,kBAAkB;SAC1C;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,2BAA2B;SACnD;KACF;IACD,WAAW,EAAE,UAAU,CAAC,IAAI;IAC5B,eAAe,EAAE;QACf,UAAU,CAAC,gBAAgB;QAC3B,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,MAAM;KAClB;IACD,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,MAAM;QACjB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,MAAM;QACjB,UAAU,CAAC,uBAAuB;QAClC,UAAU,CAAC,yBAAyB;KACrC;IACD,KAAK,EAAE,IAAI;IACX,WAAW,EAAE,gCAAgC;IAC7C,SAAS,EAAE,KAAK;IAChB,UAAU,EAAE,aAAa;CAC1B,CAAC", "sourcesContent": ["/*\n * Copyright (c) Microsoft Corporation.\n * Licensed under the MIT License.\n *\n * Code generated by Microsoft (R) AutoRest Code Generator.\n * Changes may cause incorrect behavior and will be lost if the code is regenerated.\n */\n\nimport { Blob } from \"../operationsInterfaces\";\nimport * as coreClient from \"@azure/core-client\";\nimport * as Mappers from \"../models/mappers\";\nimport * as Parameters from \"../models/parameters\";\nimport { StorageClient } from \"../storageClient\";\nimport {\n  BlobDownloadOptionalParams,\n  BlobDownloadResponse,\n  BlobGetPropertiesOptionalParams,\n  BlobGetPropertiesResponse,\n  BlobDeleteOptionalParams,\n  BlobDeleteResponse,\n  BlobUndeleteOptionalParams,\n  BlobUndeleteResponse,\n  BlobExpiryOptions,\n  BlobSetExpiryOptionalParams,\n  BlobSetExpiryResponse,\n  BlobSetHttpHeadersOptionalParams,\n  BlobSetHttpHeadersResponse,\n  BlobSetImmutabilityPolicyOptionalParams,\n  BlobSetImmutabilityPolicyResponse,\n  BlobDeleteImmutabilityPolicyOptionalParams,\n  BlobDeleteImmutabilityPolicyResponse,\n  BlobSetLegalHoldOptionalParams,\n  BlobSetLegalHoldResponse,\n  BlobSetMetadataOptionalParams,\n  BlobSetMetadataResponse,\n  BlobAcquireLeaseOptionalParams,\n  BlobAcquireLeaseResponse,\n  BlobReleaseLeaseOptionalParams,\n  BlobReleaseLeaseResponse,\n  BlobRenewLeaseOptionalParams,\n  BlobRenewLeaseResponse,\n  BlobChangeLeaseOptionalParams,\n  BlobChangeLeaseResponse,\n  BlobBreakLeaseOptionalParams,\n  BlobBreakLeaseResponse,\n  BlobCreateSnapshotOptionalParams,\n  BlobCreateSnapshotResponse,\n  BlobStartCopyFromURLOptionalParams,\n  BlobStartCopyFromURLResponse,\n  BlobCopyFromURLOptionalParams,\n  BlobCopyFromURLResponse,\n  BlobAbortCopyFromURLOptionalParams,\n  BlobAbortCopyFromURLResponse,\n  AccessTier,\n  BlobSetTierOptionalParams,\n  BlobSetTierResponse,\n  BlobGetAccountInfoOptionalParams,\n  BlobGetAccountInfoResponse,\n  BlobQueryOptionalParams,\n  BlobQueryResponse,\n  BlobGetTagsOptionalParams,\n  BlobGetTagsResponse,\n  BlobSetTagsOptionalParams,\n  BlobSetTagsResponse,\n} from \"../models\";\n\n/** Class containing Blob operations. */\nexport class BlobImpl implements Blob {\n  private readonly client: StorageClient;\n\n  /**\n   * Initialize a new instance of the class Blob class.\n   * @param client Reference to the service client\n   */\n  constructor(client: StorageClient) {\n    this.client = client;\n  }\n\n  /**\n   * The Download operation reads or downloads a blob from the system, including its metadata and\n   * properties. You can also call Download to read a snapshot.\n   * @param options The options parameters.\n   */\n  download(\n    options?: BlobDownloadOptionalParams,\n  ): Promise<BlobDownloadResponse> {\n    return this.client.sendOperationRequest({ options }, downloadOperationSpec);\n  }\n\n  /**\n   * The Get Properties operation returns all user-defined metadata, standard HTTP properties, and system\n   * properties for the blob. It does not return the content of the blob.\n   * @param options The options parameters.\n   */\n  getProperties(\n    options?: BlobGetPropertiesOptionalParams,\n  ): Promise<BlobGetPropertiesResponse> {\n    return this.client.sendOperationRequest(\n      { options },\n      getPropertiesOperationSpec,\n    );\n  }\n\n  /**\n   * If the storage account's soft delete feature is disabled then, when a blob is deleted, it is\n   * permanently removed from the storage account. If the storage account's soft delete feature is\n   * enabled, then, when a blob is deleted, it is marked for deletion and becomes inaccessible\n   * immediately. However, the blob service retains the blob or snapshot for the number of days specified\n   * by the DeleteRetentionPolicy section of [Storage service properties]\n   * (Set-Blob-Service-Properties.md). After the specified number of days has passed, the blob's data is\n   * permanently removed from the storage account. Note that you continue to be charged for the\n   * soft-deleted blob's storage until it is permanently removed. Use the List Blobs API and specify the\n   * \"include=deleted\" query parameter to discover which blobs and snapshots have been soft deleted. You\n   * can then use the Undelete Blob API to restore a soft-deleted blob. All other operations on a\n   * soft-deleted blob or snapshot causes the service to return an HTTP status code of 404\n   * (ResourceNotFound).\n   * @param options The options parameters.\n   */\n  delete(options?: BlobDeleteOptionalParams): Promise<BlobDeleteResponse> {\n    return this.client.sendOperationRequest({ options }, deleteOperationSpec);\n  }\n\n  /**\n   * Undelete a blob that was previously soft deleted\n   * @param options The options parameters.\n   */\n  undelete(\n    options?: BlobUndeleteOptionalParams,\n  ): Promise<BlobUndeleteResponse> {\n    return this.client.sendOperationRequest({ options }, undeleteOperationSpec);\n  }\n\n  /**\n   * Sets the time a blob will expire and be deleted.\n   * @param expiryOptions Required. Indicates mode of the expiry time\n   * @param options The options parameters.\n   */\n  setExpiry(\n    expiryOptions: BlobExpiryOptions,\n    options?: BlobSetExpiryOptionalParams,\n  ): Promise<BlobSetExpiryResponse> {\n    return this.client.sendOperationRequest(\n      { expiryOptions, options },\n      setExpiryOperationSpec,\n    );\n  }\n\n  /**\n   * The Set HTTP Headers operation sets system properties on the blob\n   * @param options The options parameters.\n   */\n  setHttpHeaders(\n    options?: BlobSetHttpHeadersOptionalParams,\n  ): Promise<BlobSetHttpHeadersResponse> {\n    return this.client.sendOperationRequest(\n      { options },\n      setHttpHeadersOperationSpec,\n    );\n  }\n\n  /**\n   * The Set Immutability Policy operation sets the immutability policy on the blob\n   * @param options The options parameters.\n   */\n  setImmutabilityPolicy(\n    options?: BlobSetImmutabilityPolicyOptionalParams,\n  ): Promise<BlobSetImmutabilityPolicyResponse> {\n    return this.client.sendOperationRequest(\n      { options },\n      setImmutabilityPolicyOperationSpec,\n    );\n  }\n\n  /**\n   * The Delete Immutability Policy operation deletes the immutability policy on the blob\n   * @param options The options parameters.\n   */\n  deleteImmutabilityPolicy(\n    options?: BlobDeleteImmutabilityPolicyOptionalParams,\n  ): Promise<BlobDeleteImmutabilityPolicyResponse> {\n    return this.client.sendOperationRequest(\n      { options },\n      deleteImmutabilityPolicyOperationSpec,\n    );\n  }\n\n  /**\n   * The Set Legal Hold operation sets a legal hold on the blob.\n   * @param legalHold Specified if a legal hold should be set on the blob.\n   * @param options The options parameters.\n   */\n  setLegalHold(\n    legalHold: boolean,\n    options?: BlobSetLegalHoldOptionalParams,\n  ): Promise<BlobSetLegalHoldResponse> {\n    return this.client.sendOperationRequest(\n      { legalHold, options },\n      setLegalHoldOperationSpec,\n    );\n  }\n\n  /**\n   * The Set Blob Metadata operation sets user-defined metadata for the specified blob as one or more\n   * name-value pairs\n   * @param options The options parameters.\n   */\n  setMetadata(\n    options?: BlobSetMetadataOptionalParams,\n  ): Promise<BlobSetMetadataResponse> {\n    return this.client.sendOperationRequest(\n      { options },\n      setMetadataOperationSpec,\n    );\n  }\n\n  /**\n   * [Update] The Lease Blob operation establishes and manages a lock on a blob for write and delete\n   * operations\n   * @param options The options parameters.\n   */\n  acquireLease(\n    options?: BlobAcquireLeaseOptionalParams,\n  ): Promise<BlobAcquireLeaseResponse> {\n    return this.client.sendOperationRequest(\n      { options },\n      acquireLeaseOperationSpec,\n    );\n  }\n\n  /**\n   * [Update] The Lease Blob operation establishes and manages a lock on a blob for write and delete\n   * operations\n   * @param leaseId Specifies the current lease ID on the resource.\n   * @param options The options parameters.\n   */\n  releaseLease(\n    leaseId: string,\n    options?: BlobReleaseLeaseOptionalParams,\n  ): Promise<BlobReleaseLeaseResponse> {\n    return this.client.sendOperationRequest(\n      { leaseId, options },\n      releaseLeaseOperationSpec,\n    );\n  }\n\n  /**\n   * [Update] The Lease Blob operation establishes and manages a lock on a blob for write and delete\n   * operations\n   * @param leaseId Specifies the current lease ID on the resource.\n   * @param options The options parameters.\n   */\n  renewLease(\n    leaseId: string,\n    options?: BlobRenewLeaseOptionalParams,\n  ): Promise<BlobRenewLeaseResponse> {\n    return this.client.sendOperationRequest(\n      { leaseId, options },\n      renewLeaseOperationSpec,\n    );\n  }\n\n  /**\n   * [Update] The Lease Blob operation establishes and manages a lock on a blob for write and delete\n   * operations\n   * @param leaseId Specifies the current lease ID on the resource.\n   * @param proposedLeaseId Proposed lease ID, in a GUID string format. The Blob service returns 400\n   *                        (Invalid request) if the proposed lease ID is not in the correct format. See Guid Constructor\n   *                        (String) for a list of valid GUID string formats.\n   * @param options The options parameters.\n   */\n  changeLease(\n    leaseId: string,\n    proposedLeaseId: string,\n    options?: BlobChangeLeaseOptionalParams,\n  ): Promise<BlobChangeLeaseResponse> {\n    return this.client.sendOperationRequest(\n      { leaseId, proposedLeaseId, options },\n      changeLeaseOperationSpec,\n    );\n  }\n\n  /**\n   * [Update] The Lease Blob operation establishes and manages a lock on a blob for write and delete\n   * operations\n   * @param options The options parameters.\n   */\n  breakLease(\n    options?: BlobBreakLeaseOptionalParams,\n  ): Promise<BlobBreakLeaseResponse> {\n    return this.client.sendOperationRequest(\n      { options },\n      breakLeaseOperationSpec,\n    );\n  }\n\n  /**\n   * The Create Snapshot operation creates a read-only snapshot of a blob\n   * @param options The options parameters.\n   */\n  createSnapshot(\n    options?: BlobCreateSnapshotOptionalParams,\n  ): Promise<BlobCreateSnapshotResponse> {\n    return this.client.sendOperationRequest(\n      { options },\n      createSnapshotOperationSpec,\n    );\n  }\n\n  /**\n   * The Start Copy From URL operation copies a blob or an internet resource to a new blob.\n   * @param copySource Specifies the name of the source page blob snapshot. This value is a URL of up to\n   *                   2 KB in length that specifies a page blob snapshot. The value should be URL-encoded as it would\n   *                   appear in a request URI. The source blob must either be public or must be authenticated via a shared\n   *                   access signature.\n   * @param options The options parameters.\n   */\n  startCopyFromURL(\n    copySource: string,\n    options?: BlobStartCopyFromURLOptionalParams,\n  ): Promise<BlobStartCopyFromURLResponse> {\n    return this.client.sendOperationRequest(\n      { copySource, options },\n      startCopyFromURLOperationSpec,\n    );\n  }\n\n  /**\n   * The Copy From URL operation copies a blob or an internet resource to a new blob. It will not return\n   * a response until the copy is complete.\n   * @param copySource Specifies the name of the source page blob snapshot. This value is a URL of up to\n   *                   2 KB in length that specifies a page blob snapshot. The value should be URL-encoded as it would\n   *                   appear in a request URI. The source blob must either be public or must be authenticated via a shared\n   *                   access signature.\n   * @param options The options parameters.\n   */\n  copyFromURL(\n    copySource: string,\n    options?: BlobCopyFromURLOptionalParams,\n  ): Promise<BlobCopyFromURLResponse> {\n    return this.client.sendOperationRequest(\n      { copySource, options },\n      copyFromURLOperationSpec,\n    );\n  }\n\n  /**\n   * The Abort Copy From URL operation aborts a pending Copy From URL operation, and leaves a destination\n   * blob with zero length and full metadata.\n   * @param copyId The copy identifier provided in the x-ms-copy-id header of the original Copy Blob\n   *               operation.\n   * @param options The options parameters.\n   */\n  abortCopyFromURL(\n    copyId: string,\n    options?: BlobAbortCopyFromURLOptionalParams,\n  ): Promise<BlobAbortCopyFromURLResponse> {\n    return this.client.sendOperationRequest(\n      { copyId, options },\n      abortCopyFromURLOperationSpec,\n    );\n  }\n\n  /**\n   * The Set Tier operation sets the tier on a blob. The operation is allowed on a page blob in a premium\n   * storage account and on a block blob in a blob storage account (locally redundant storage only). A\n   * premium page blob's tier determines the allowed size, IOPS, and bandwidth of the blob. A block\n   * blob's tier determines Hot/Cool/Archive storage type. This operation does not update the blob's\n   * ETag.\n   * @param tier Indicates the tier to be set on the blob.\n   * @param options The options parameters.\n   */\n  setTier(\n    tier: AccessTier,\n    options?: BlobSetTierOptionalParams,\n  ): Promise<BlobSetTierResponse> {\n    return this.client.sendOperationRequest(\n      { tier, options },\n      setTierOperationSpec,\n    );\n  }\n\n  /**\n   * Returns the sku name and account kind\n   * @param options The options parameters.\n   */\n  getAccountInfo(\n    options?: BlobGetAccountInfoOptionalParams,\n  ): Promise<BlobGetAccountInfoResponse> {\n    return this.client.sendOperationRequest(\n      { options },\n      getAccountInfoOperationSpec,\n    );\n  }\n\n  /**\n   * The Query operation enables users to select/project on blob data by providing simple query\n   * expressions.\n   * @param options The options parameters.\n   */\n  query(options?: BlobQueryOptionalParams): Promise<BlobQueryResponse> {\n    return this.client.sendOperationRequest({ options }, queryOperationSpec);\n  }\n\n  /**\n   * The Get Tags operation enables users to get the tags associated with a blob.\n   * @param options The options parameters.\n   */\n  getTags(options?: BlobGetTagsOptionalParams): Promise<BlobGetTagsResponse> {\n    return this.client.sendOperationRequest({ options }, getTagsOperationSpec);\n  }\n\n  /**\n   * The Set Tags operation enables users to set tags on a blob.\n   * @param options The options parameters.\n   */\n  setTags(options?: BlobSetTagsOptionalParams): Promise<BlobSetTagsResponse> {\n    return this.client.sendOperationRequest({ options }, setTagsOperationSpec);\n  }\n}\n// Operation Specifications\nconst xmlSerializer = coreClient.createSerializer(Mappers, /* isXml */ true);\n\nconst downloadOperationSpec: coreClient.OperationSpec = {\n  path: \"/{containerName}/{blob}\",\n  httpMethod: \"GET\",\n  responses: {\n    200: {\n      bodyMapper: {\n        type: { name: \"Stream\" },\n        serializedName: \"parsedResponse\",\n      },\n      headersMapper: Mappers.BlobDownloadHeaders,\n    },\n    206: {\n      bodyMapper: {\n        type: { name: \"Stream\" },\n        serializedName: \"parsedResponse\",\n      },\n      headersMapper: Mappers.BlobDownloadHeaders,\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.BlobDownloadExceptionHeaders,\n    },\n  },\n  queryParameters: [\n    Parameters.timeoutInSeconds,\n    Parameters.snapshot,\n    Parameters.versionId,\n  ],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.accept1,\n    Parameters.leaseId,\n    Parameters.ifModifiedSince,\n    Parameters.ifUnmodifiedSince,\n    Parameters.range,\n    Parameters.rangeGetContentMD5,\n    Parameters.rangeGetContentCRC64,\n    Parameters.encryptionKey,\n    Parameters.encryptionKeySha256,\n    Parameters.encryptionAlgorithm,\n    Parameters.ifMatch,\n    Parameters.ifNoneMatch,\n    Parameters.ifTags,\n  ],\n  isXML: true,\n  serializer: xmlSerializer,\n};\nconst getPropertiesOperationSpec: coreClient.OperationSpec = {\n  path: \"/{containerName}/{blob}\",\n  httpMethod: \"HEAD\",\n  responses: {\n    200: {\n      headersMapper: Mappers.BlobGetPropertiesHeaders,\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.BlobGetPropertiesExceptionHeaders,\n    },\n  },\n  queryParameters: [\n    Parameters.timeoutInSeconds,\n    Parameters.snapshot,\n    Parameters.versionId,\n  ],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.accept1,\n    Parameters.leaseId,\n    Parameters.ifModifiedSince,\n    Parameters.ifUnmodifiedSince,\n    Parameters.encryptionKey,\n    Parameters.encryptionKeySha256,\n    Parameters.encryptionAlgorithm,\n    Parameters.ifMatch,\n    Parameters.ifNoneMatch,\n    Parameters.ifTags,\n  ],\n  isXML: true,\n  serializer: xmlSerializer,\n};\nconst deleteOperationSpec: coreClient.OperationSpec = {\n  path: \"/{containerName}/{blob}\",\n  httpMethod: \"DELETE\",\n  responses: {\n    202: {\n      headersMapper: Mappers.BlobDeleteHeaders,\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.BlobDeleteExceptionHeaders,\n    },\n  },\n  queryParameters: [\n    Parameters.timeoutInSeconds,\n    Parameters.snapshot,\n    Parameters.versionId,\n    Parameters.blobDeleteType,\n  ],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.accept1,\n    Parameters.leaseId,\n    Parameters.ifModifiedSince,\n    Parameters.ifUnmodifiedSince,\n    Parameters.ifMatch,\n    Parameters.ifNoneMatch,\n    Parameters.ifTags,\n    Parameters.deleteSnapshots,\n  ],\n  isXML: true,\n  serializer: xmlSerializer,\n};\nconst undeleteOperationSpec: coreClient.OperationSpec = {\n  path: \"/{containerName}/{blob}\",\n  httpMethod: \"PUT\",\n  responses: {\n    200: {\n      headersMapper: Mappers.BlobUndeleteHeaders,\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.BlobUndeleteExceptionHeaders,\n    },\n  },\n  queryParameters: [Parameters.timeoutInSeconds, Parameters.comp8],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.accept1,\n  ],\n  isXML: true,\n  serializer: xmlSerializer,\n};\nconst setExpiryOperationSpec: coreClient.OperationSpec = {\n  path: \"/{containerName}/{blob}\",\n  httpMethod: \"PUT\",\n  responses: {\n    200: {\n      headersMapper: Mappers.BlobSetExpiryHeaders,\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.BlobSetExpiryExceptionHeaders,\n    },\n  },\n  queryParameters: [Parameters.timeoutInSeconds, Parameters.comp11],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.accept1,\n    Parameters.expiryOptions,\n    Parameters.expiresOn,\n  ],\n  isXML: true,\n  serializer: xmlSerializer,\n};\nconst setHttpHeadersOperationSpec: coreClient.OperationSpec = {\n  path: \"/{containerName}/{blob}\",\n  httpMethod: \"PUT\",\n  responses: {\n    200: {\n      headersMapper: Mappers.BlobSetHttpHeadersHeaders,\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.BlobSetHttpHeadersExceptionHeaders,\n    },\n  },\n  queryParameters: [Parameters.comp, Parameters.timeoutInSeconds],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.accept1,\n    Parameters.leaseId,\n    Parameters.ifModifiedSince,\n    Parameters.ifUnmodifiedSince,\n    Parameters.ifMatch,\n    Parameters.ifNoneMatch,\n    Parameters.ifTags,\n    Parameters.blobCacheControl,\n    Parameters.blobContentType,\n    Parameters.blobContentMD5,\n    Parameters.blobContentEncoding,\n    Parameters.blobContentLanguage,\n    Parameters.blobContentDisposition,\n  ],\n  isXML: true,\n  serializer: xmlSerializer,\n};\nconst setImmutabilityPolicyOperationSpec: coreClient.OperationSpec = {\n  path: \"/{containerName}/{blob}\",\n  httpMethod: \"PUT\",\n  responses: {\n    200: {\n      headersMapper: Mappers.BlobSetImmutabilityPolicyHeaders,\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.BlobSetImmutabilityPolicyExceptionHeaders,\n    },\n  },\n  queryParameters: [\n    Parameters.timeoutInSeconds,\n    Parameters.snapshot,\n    Parameters.versionId,\n    Parameters.comp12,\n  ],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.accept1,\n    Parameters.ifUnmodifiedSince,\n    Parameters.immutabilityPolicyExpiry,\n    Parameters.immutabilityPolicyMode,\n  ],\n  isXML: true,\n  serializer: xmlSerializer,\n};\nconst deleteImmutabilityPolicyOperationSpec: coreClient.OperationSpec = {\n  path: \"/{containerName}/{blob}\",\n  httpMethod: \"DELETE\",\n  responses: {\n    200: {\n      headersMapper: Mappers.BlobDeleteImmutabilityPolicyHeaders,\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.BlobDeleteImmutabilityPolicyExceptionHeaders,\n    },\n  },\n  queryParameters: [\n    Parameters.timeoutInSeconds,\n    Parameters.snapshot,\n    Parameters.versionId,\n    Parameters.comp12,\n  ],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.accept1,\n  ],\n  isXML: true,\n  serializer: xmlSerializer,\n};\nconst setLegalHoldOperationSpec: coreClient.OperationSpec = {\n  path: \"/{containerName}/{blob}\",\n  httpMethod: \"PUT\",\n  responses: {\n    200: {\n      headersMapper: Mappers.BlobSetLegalHoldHeaders,\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.BlobSetLegalHoldExceptionHeaders,\n    },\n  },\n  queryParameters: [\n    Parameters.timeoutInSeconds,\n    Parameters.snapshot,\n    Parameters.versionId,\n    Parameters.comp13,\n  ],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.accept1,\n    Parameters.legalHold,\n  ],\n  isXML: true,\n  serializer: xmlSerializer,\n};\nconst setMetadataOperationSpec: coreClient.OperationSpec = {\n  path: \"/{containerName}/{blob}\",\n  httpMethod: \"PUT\",\n  responses: {\n    200: {\n      headersMapper: Mappers.BlobSetMetadataHeaders,\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.BlobSetMetadataExceptionHeaders,\n    },\n  },\n  queryParameters: [Parameters.timeoutInSeconds, Parameters.comp6],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.accept1,\n    Parameters.metadata,\n    Parameters.leaseId,\n    Parameters.ifModifiedSince,\n    Parameters.ifUnmodifiedSince,\n    Parameters.encryptionKey,\n    Parameters.encryptionKeySha256,\n    Parameters.encryptionAlgorithm,\n    Parameters.ifMatch,\n    Parameters.ifNoneMatch,\n    Parameters.ifTags,\n    Parameters.encryptionScope,\n  ],\n  isXML: true,\n  serializer: xmlSerializer,\n};\nconst acquireLeaseOperationSpec: coreClient.OperationSpec = {\n  path: \"/{containerName}/{blob}\",\n  httpMethod: \"PUT\",\n  responses: {\n    201: {\n      headersMapper: Mappers.BlobAcquireLeaseHeaders,\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.BlobAcquireLeaseExceptionHeaders,\n    },\n  },\n  queryParameters: [Parameters.timeoutInSeconds, Parameters.comp10],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.accept1,\n    Parameters.ifModifiedSince,\n    Parameters.ifUnmodifiedSince,\n    Parameters.action,\n    Parameters.duration,\n    Parameters.proposedLeaseId,\n    Parameters.ifMatch,\n    Parameters.ifNoneMatch,\n    Parameters.ifTags,\n  ],\n  isXML: true,\n  serializer: xmlSerializer,\n};\nconst releaseLeaseOperationSpec: coreClient.OperationSpec = {\n  path: \"/{containerName}/{blob}\",\n  httpMethod: \"PUT\",\n  responses: {\n    200: {\n      headersMapper: Mappers.BlobReleaseLeaseHeaders,\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.BlobReleaseLeaseExceptionHeaders,\n    },\n  },\n  queryParameters: [Parameters.timeoutInSeconds, Parameters.comp10],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.accept1,\n    Parameters.ifModifiedSince,\n    Parameters.ifUnmodifiedSince,\n    Parameters.action1,\n    Parameters.leaseId1,\n    Parameters.ifMatch,\n    Parameters.ifNoneMatch,\n    Parameters.ifTags,\n  ],\n  isXML: true,\n  serializer: xmlSerializer,\n};\nconst renewLeaseOperationSpec: coreClient.OperationSpec = {\n  path: \"/{containerName}/{blob}\",\n  httpMethod: \"PUT\",\n  responses: {\n    200: {\n      headersMapper: Mappers.BlobRenewLeaseHeaders,\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.BlobRenewLeaseExceptionHeaders,\n    },\n  },\n  queryParameters: [Parameters.timeoutInSeconds, Parameters.comp10],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.accept1,\n    Parameters.ifModifiedSince,\n    Parameters.ifUnmodifiedSince,\n    Parameters.leaseId1,\n    Parameters.action2,\n    Parameters.ifMatch,\n    Parameters.ifNoneMatch,\n    Parameters.ifTags,\n  ],\n  isXML: true,\n  serializer: xmlSerializer,\n};\nconst changeLeaseOperationSpec: coreClient.OperationSpec = {\n  path: \"/{containerName}/{blob}\",\n  httpMethod: \"PUT\",\n  responses: {\n    200: {\n      headersMapper: Mappers.BlobChangeLeaseHeaders,\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.BlobChangeLeaseExceptionHeaders,\n    },\n  },\n  queryParameters: [Parameters.timeoutInSeconds, Parameters.comp10],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.accept1,\n    Parameters.ifModifiedSince,\n    Parameters.ifUnmodifiedSince,\n    Parameters.leaseId1,\n    Parameters.action4,\n    Parameters.proposedLeaseId1,\n    Parameters.ifMatch,\n    Parameters.ifNoneMatch,\n    Parameters.ifTags,\n  ],\n  isXML: true,\n  serializer: xmlSerializer,\n};\nconst breakLeaseOperationSpec: coreClient.OperationSpec = {\n  path: \"/{containerName}/{blob}\",\n  httpMethod: \"PUT\",\n  responses: {\n    202: {\n      headersMapper: Mappers.BlobBreakLeaseHeaders,\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.BlobBreakLeaseExceptionHeaders,\n    },\n  },\n  queryParameters: [Parameters.timeoutInSeconds, Parameters.comp10],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.accept1,\n    Parameters.ifModifiedSince,\n    Parameters.ifUnmodifiedSince,\n    Parameters.action3,\n    Parameters.breakPeriod,\n    Parameters.ifMatch,\n    Parameters.ifNoneMatch,\n    Parameters.ifTags,\n  ],\n  isXML: true,\n  serializer: xmlSerializer,\n};\nconst createSnapshotOperationSpec: coreClient.OperationSpec = {\n  path: \"/{containerName}/{blob}\",\n  httpMethod: \"PUT\",\n  responses: {\n    201: {\n      headersMapper: Mappers.BlobCreateSnapshotHeaders,\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.BlobCreateSnapshotExceptionHeaders,\n    },\n  },\n  queryParameters: [Parameters.timeoutInSeconds, Parameters.comp14],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.accept1,\n    Parameters.metadata,\n    Parameters.leaseId,\n    Parameters.ifModifiedSince,\n    Parameters.ifUnmodifiedSince,\n    Parameters.encryptionKey,\n    Parameters.encryptionKeySha256,\n    Parameters.encryptionAlgorithm,\n    Parameters.ifMatch,\n    Parameters.ifNoneMatch,\n    Parameters.ifTags,\n    Parameters.encryptionScope,\n  ],\n  isXML: true,\n  serializer: xmlSerializer,\n};\nconst startCopyFromURLOperationSpec: coreClient.OperationSpec = {\n  path: \"/{containerName}/{blob}\",\n  httpMethod: \"PUT\",\n  responses: {\n    202: {\n      headersMapper: Mappers.BlobStartCopyFromURLHeaders,\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.BlobStartCopyFromURLExceptionHeaders,\n    },\n  },\n  queryParameters: [Parameters.timeoutInSeconds],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.accept1,\n    Parameters.metadata,\n    Parameters.leaseId,\n    Parameters.ifModifiedSince,\n    Parameters.ifUnmodifiedSince,\n    Parameters.ifMatch,\n    Parameters.ifNoneMatch,\n    Parameters.ifTags,\n    Parameters.immutabilityPolicyExpiry,\n    Parameters.immutabilityPolicyMode,\n    Parameters.tier,\n    Parameters.rehydratePriority,\n    Parameters.sourceIfModifiedSince,\n    Parameters.sourceIfUnmodifiedSince,\n    Parameters.sourceIfMatch,\n    Parameters.sourceIfNoneMatch,\n    Parameters.sourceIfTags,\n    Parameters.copySource,\n    Parameters.blobTagsString,\n    Parameters.sealBlob,\n    Parameters.legalHold1,\n  ],\n  isXML: true,\n  serializer: xmlSerializer,\n};\nconst copyFromURLOperationSpec: coreClient.OperationSpec = {\n  path: \"/{containerName}/{blob}\",\n  httpMethod: \"PUT\",\n  responses: {\n    202: {\n      headersMapper: Mappers.BlobCopyFromURLHeaders,\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.BlobCopyFromURLExceptionHeaders,\n    },\n  },\n  queryParameters: [Parameters.timeoutInSeconds],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.accept1,\n    Parameters.metadata,\n    Parameters.leaseId,\n    Parameters.ifModifiedSince,\n    Parameters.ifUnmodifiedSince,\n    Parameters.ifMatch,\n    Parameters.ifNoneMatch,\n    Parameters.ifTags,\n    Parameters.immutabilityPolicyExpiry,\n    Parameters.immutabilityPolicyMode,\n    Parameters.encryptionScope,\n    Parameters.tier,\n    Parameters.sourceIfModifiedSince,\n    Parameters.sourceIfUnmodifiedSince,\n    Parameters.sourceIfMatch,\n    Parameters.sourceIfNoneMatch,\n    Parameters.copySource,\n    Parameters.blobTagsString,\n    Parameters.legalHold1,\n    Parameters.xMsRequiresSync,\n    Parameters.sourceContentMD5,\n    Parameters.copySourceAuthorization,\n    Parameters.copySourceTags,\n  ],\n  isXML: true,\n  serializer: xmlSerializer,\n};\nconst abortCopyFromURLOperationSpec: coreClient.OperationSpec = {\n  path: \"/{containerName}/{blob}\",\n  httpMethod: \"PUT\",\n  responses: {\n    204: {\n      headersMapper: Mappers.BlobAbortCopyFromURLHeaders,\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.BlobAbortCopyFromURLExceptionHeaders,\n    },\n  },\n  queryParameters: [\n    Parameters.timeoutInSeconds,\n    Parameters.comp15,\n    Parameters.copyId,\n  ],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.accept1,\n    Parameters.leaseId,\n    Parameters.copyActionAbortConstant,\n  ],\n  isXML: true,\n  serializer: xmlSerializer,\n};\nconst setTierOperationSpec: coreClient.OperationSpec = {\n  path: \"/{containerName}/{blob}\",\n  httpMethod: \"PUT\",\n  responses: {\n    200: {\n      headersMapper: Mappers.BlobSetTierHeaders,\n    },\n    202: {\n      headersMapper: Mappers.BlobSetTierHeaders,\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.BlobSetTierExceptionHeaders,\n    },\n  },\n  queryParameters: [\n    Parameters.timeoutInSeconds,\n    Parameters.snapshot,\n    Parameters.versionId,\n    Parameters.comp16,\n  ],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.accept1,\n    Parameters.leaseId,\n    Parameters.ifTags,\n    Parameters.rehydratePriority,\n    Parameters.tier1,\n  ],\n  isXML: true,\n  serializer: xmlSerializer,\n};\nconst getAccountInfoOperationSpec: coreClient.OperationSpec = {\n  path: \"/{containerName}/{blob}\",\n  httpMethod: \"GET\",\n  responses: {\n    200: {\n      headersMapper: Mappers.BlobGetAccountInfoHeaders,\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.BlobGetAccountInfoExceptionHeaders,\n    },\n  },\n  queryParameters: [\n    Parameters.comp,\n    Parameters.timeoutInSeconds,\n    Parameters.restype1,\n  ],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.accept1,\n  ],\n  isXML: true,\n  serializer: xmlSerializer,\n};\nconst queryOperationSpec: coreClient.OperationSpec = {\n  path: \"/{containerName}/{blob}\",\n  httpMethod: \"POST\",\n  responses: {\n    200: {\n      bodyMapper: {\n        type: { name: \"Stream\" },\n        serializedName: \"parsedResponse\",\n      },\n      headersMapper: Mappers.BlobQueryHeaders,\n    },\n    206: {\n      bodyMapper: {\n        type: { name: \"Stream\" },\n        serializedName: \"parsedResponse\",\n      },\n      headersMapper: Mappers.BlobQueryHeaders,\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.BlobQueryExceptionHeaders,\n    },\n  },\n  requestBody: Parameters.queryRequest,\n  queryParameters: [\n    Parameters.timeoutInSeconds,\n    Parameters.snapshot,\n    Parameters.comp17,\n  ],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.contentType,\n    Parameters.accept,\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.leaseId,\n    Parameters.ifModifiedSince,\n    Parameters.ifUnmodifiedSince,\n    Parameters.encryptionKey,\n    Parameters.encryptionKeySha256,\n    Parameters.encryptionAlgorithm,\n    Parameters.ifMatch,\n    Parameters.ifNoneMatch,\n    Parameters.ifTags,\n  ],\n  isXML: true,\n  contentType: \"application/xml; charset=utf-8\",\n  mediaType: \"xml\",\n  serializer: xmlSerializer,\n};\nconst getTagsOperationSpec: coreClient.OperationSpec = {\n  path: \"/{containerName}/{blob}\",\n  httpMethod: \"GET\",\n  responses: {\n    200: {\n      bodyMapper: Mappers.BlobTags,\n      headersMapper: Mappers.BlobGetTagsHeaders,\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.BlobGetTagsExceptionHeaders,\n    },\n  },\n  queryParameters: [\n    Parameters.timeoutInSeconds,\n    Parameters.snapshot,\n    Parameters.versionId,\n    Parameters.comp18,\n  ],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.accept1,\n    Parameters.leaseId,\n    Parameters.ifTags,\n  ],\n  isXML: true,\n  serializer: xmlSerializer,\n};\nconst setTagsOperationSpec: coreClient.OperationSpec = {\n  path: \"/{containerName}/{blob}\",\n  httpMethod: \"PUT\",\n  responses: {\n    204: {\n      headersMapper: Mappers.BlobSetTagsHeaders,\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.BlobSetTagsExceptionHeaders,\n    },\n  },\n  requestBody: Parameters.tags,\n  queryParameters: [\n    Parameters.timeoutInSeconds,\n    Parameters.versionId,\n    Parameters.comp18,\n  ],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.contentType,\n    Parameters.accept,\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.leaseId,\n    Parameters.ifTags,\n    Parameters.transactionalContentMD5,\n    Parameters.transactionalContentCrc64,\n  ],\n  isXML: true,\n  contentType: \"application/xml; charset=utf-8\",\n  mediaType: \"xml\",\n  serializer: xmlSerializer,\n};\n"]}
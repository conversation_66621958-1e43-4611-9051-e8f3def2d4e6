/*
 * Copyright (c) Microsoft Corporation.
 * Licensed under the MIT License.
 *
 * Code generated by Microsoft (R) AutoRest Code Generator.
 * Changes may cause incorrect behavior and will be lost if the code is regenerated.
 */
import * as coreClient from "@azure/core-client";
import * as Mappers from "../models/mappers";
import * as Parameters from "../models/parameters";
/** Class containing BlockBlob operations. */
export class BlockBlobImpl {
    /**
     * Initialize a new instance of the class BlockBlob class.
     * @param client Reference to the service client
     */
    constructor(client) {
        this.client = client;
    }
    /**
     * The Upload Block Blob operation updates the content of an existing block blob. Updating an existing
     * block blob overwrites any existing metadata on the blob. Partial updates are not supported with Put
     * Blob; the content of the existing blob is overwritten with the content of the new blob. To perform a
     * partial update of the content of a block blob, use the Put Block List operation.
     * @param contentLength The length of the request.
     * @param body Initial data
     * @param options The options parameters.
     */
    upload(contentLength, body, options) {
        return this.client.sendOperationRequest({ contentLength, body, options }, uploadOperationSpec);
    }
    /**
     * The Put Blob from URL operation creates a new Block Blob where the contents of the blob are read
     * from a given URL.  This API is supported beginning with the 2020-04-08 version. Partial updates are
     * not supported with Put Blob from URL; the content of an existing blob is overwritten with the
     * content of the new blob.  To perform partial updates to a block blob’s contents using a source URL,
     * use the Put Block from URL API in conjunction with Put Block List.
     * @param contentLength The length of the request.
     * @param copySource Specifies the name of the source page blob snapshot. This value is a URL of up to
     *                   2 KB in length that specifies a page blob snapshot. The value should be URL-encoded as it would
     *                   appear in a request URI. The source blob must either be public or must be authenticated via a shared
     *                   access signature.
     * @param options The options parameters.
     */
    putBlobFromUrl(contentLength, copySource, options) {
        return this.client.sendOperationRequest({ contentLength, copySource, options }, putBlobFromUrlOperationSpec);
    }
    /**
     * The Stage Block operation creates a new block to be committed as part of a blob
     * @param blockId A valid Base64 string value that identifies the block. Prior to encoding, the string
     *                must be less than or equal to 64 bytes in size. For a given blob, the length of the value specified
     *                for the blockid parameter must be the same size for each block.
     * @param contentLength The length of the request.
     * @param body Initial data
     * @param options The options parameters.
     */
    stageBlock(blockId, contentLength, body, options) {
        return this.client.sendOperationRequest({ blockId, contentLength, body, options }, stageBlockOperationSpec);
    }
    /**
     * The Stage Block operation creates a new block to be committed as part of a blob where the contents
     * are read from a URL.
     * @param blockId A valid Base64 string value that identifies the block. Prior to encoding, the string
     *                must be less than or equal to 64 bytes in size. For a given blob, the length of the value specified
     *                for the blockid parameter must be the same size for each block.
     * @param contentLength The length of the request.
     * @param sourceUrl Specify a URL to the copy source.
     * @param options The options parameters.
     */
    stageBlockFromURL(blockId, contentLength, sourceUrl, options) {
        return this.client.sendOperationRequest({ blockId, contentLength, sourceUrl, options }, stageBlockFromURLOperationSpec);
    }
    /**
     * The Commit Block List operation writes a blob by specifying the list of block IDs that make up the
     * blob. In order to be written as part of a blob, a block must have been successfully written to the
     * server in a prior Put Block operation. You can call Put Block List to update a blob by uploading
     * only those blocks that have changed, then committing the new and existing blocks together. You can
     * do this by specifying whether to commit a block from the committed block list or from the
     * uncommitted block list, or to commit the most recently uploaded version of the block, whichever list
     * it may belong to.
     * @param blocks Blob Blocks.
     * @param options The options parameters.
     */
    commitBlockList(blocks, options) {
        return this.client.sendOperationRequest({ blocks, options }, commitBlockListOperationSpec);
    }
    /**
     * The Get Block List operation retrieves the list of blocks that have been uploaded as part of a block
     * blob
     * @param listType Specifies whether to return the list of committed blocks, the list of uncommitted
     *                 blocks, or both lists together.
     * @param options The options parameters.
     */
    getBlockList(listType, options) {
        return this.client.sendOperationRequest({ listType, options }, getBlockListOperationSpec);
    }
}
// Operation Specifications
const xmlSerializer = coreClient.createSerializer(Mappers, /* isXml */ true);
const uploadOperationSpec = {
    path: "/{containerName}/{blob}",
    httpMethod: "PUT",
    responses: {
        201: {
            headersMapper: Mappers.BlockBlobUploadHeaders,
        },
        default: {
            bodyMapper: Mappers.StorageError,
            headersMapper: Mappers.BlockBlobUploadExceptionHeaders,
        },
    },
    requestBody: Parameters.body1,
    queryParameters: [Parameters.timeoutInSeconds],
    urlParameters: [Parameters.url],
    headerParameters: [
        Parameters.version,
        Parameters.requestId,
        Parameters.contentLength,
        Parameters.metadata,
        Parameters.leaseId,
        Parameters.ifModifiedSince,
        Parameters.ifUnmodifiedSince,
        Parameters.encryptionKey,
        Parameters.encryptionKeySha256,
        Parameters.encryptionAlgorithm,
        Parameters.ifMatch,
        Parameters.ifNoneMatch,
        Parameters.ifTags,
        Parameters.blobCacheControl,
        Parameters.blobContentType,
        Parameters.blobContentMD5,
        Parameters.blobContentEncoding,
        Parameters.blobContentLanguage,
        Parameters.blobContentDisposition,
        Parameters.immutabilityPolicyExpiry,
        Parameters.immutabilityPolicyMode,
        Parameters.encryptionScope,
        Parameters.tier,
        Parameters.blobTagsString,
        Parameters.legalHold1,
        Parameters.transactionalContentMD5,
        Parameters.transactionalContentCrc64,
        Parameters.contentType1,
        Parameters.accept2,
        Parameters.blobType2,
    ],
    isXML: true,
    contentType: "application/xml; charset=utf-8",
    mediaType: "binary",
    serializer: xmlSerializer,
};
const putBlobFromUrlOperationSpec = {
    path: "/{containerName}/{blob}",
    httpMethod: "PUT",
    responses: {
        201: {
            headersMapper: Mappers.BlockBlobPutBlobFromUrlHeaders,
        },
        default: {
            bodyMapper: Mappers.StorageError,
            headersMapper: Mappers.BlockBlobPutBlobFromUrlExceptionHeaders,
        },
    },
    queryParameters: [Parameters.timeoutInSeconds],
    urlParameters: [Parameters.url],
    headerParameters: [
        Parameters.version,
        Parameters.requestId,
        Parameters.accept1,
        Parameters.contentLength,
        Parameters.metadata,
        Parameters.leaseId,
        Parameters.ifModifiedSince,
        Parameters.ifUnmodifiedSince,
        Parameters.encryptionKey,
        Parameters.encryptionKeySha256,
        Parameters.encryptionAlgorithm,
        Parameters.ifMatch,
        Parameters.ifNoneMatch,
        Parameters.ifTags,
        Parameters.blobCacheControl,
        Parameters.blobContentType,
        Parameters.blobContentMD5,
        Parameters.blobContentEncoding,
        Parameters.blobContentLanguage,
        Parameters.blobContentDisposition,
        Parameters.encryptionScope,
        Parameters.tier,
        Parameters.sourceIfModifiedSince,
        Parameters.sourceIfUnmodifiedSince,
        Parameters.sourceIfMatch,
        Parameters.sourceIfNoneMatch,
        Parameters.sourceIfTags,
        Parameters.copySource,
        Parameters.blobTagsString,
        Parameters.sourceContentMD5,
        Parameters.copySourceAuthorization,
        Parameters.copySourceTags,
        Parameters.transactionalContentMD5,
        Parameters.blobType2,
        Parameters.copySourceBlobProperties,
    ],
    isXML: true,
    serializer: xmlSerializer,
};
const stageBlockOperationSpec = {
    path: "/{containerName}/{blob}",
    httpMethod: "PUT",
    responses: {
        201: {
            headersMapper: Mappers.BlockBlobStageBlockHeaders,
        },
        default: {
            bodyMapper: Mappers.StorageError,
            headersMapper: Mappers.BlockBlobStageBlockExceptionHeaders,
        },
    },
    requestBody: Parameters.body1,
    queryParameters: [
        Parameters.timeoutInSeconds,
        Parameters.comp24,
        Parameters.blockId,
    ],
    urlParameters: [Parameters.url],
    headerParameters: [
        Parameters.version,
        Parameters.requestId,
        Parameters.contentLength,
        Parameters.leaseId,
        Parameters.encryptionKey,
        Parameters.encryptionKeySha256,
        Parameters.encryptionAlgorithm,
        Parameters.encryptionScope,
        Parameters.transactionalContentMD5,
        Parameters.transactionalContentCrc64,
        Parameters.contentType1,
        Parameters.accept2,
    ],
    isXML: true,
    contentType: "application/xml; charset=utf-8",
    mediaType: "binary",
    serializer: xmlSerializer,
};
const stageBlockFromURLOperationSpec = {
    path: "/{containerName}/{blob}",
    httpMethod: "PUT",
    responses: {
        201: {
            headersMapper: Mappers.BlockBlobStageBlockFromURLHeaders,
        },
        default: {
            bodyMapper: Mappers.StorageError,
            headersMapper: Mappers.BlockBlobStageBlockFromURLExceptionHeaders,
        },
    },
    queryParameters: [
        Parameters.timeoutInSeconds,
        Parameters.comp24,
        Parameters.blockId,
    ],
    urlParameters: [Parameters.url],
    headerParameters: [
        Parameters.version,
        Parameters.requestId,
        Parameters.accept1,
        Parameters.contentLength,
        Parameters.leaseId,
        Parameters.encryptionKey,
        Parameters.encryptionKeySha256,
        Parameters.encryptionAlgorithm,
        Parameters.encryptionScope,
        Parameters.sourceIfModifiedSince,
        Parameters.sourceIfUnmodifiedSince,
        Parameters.sourceIfMatch,
        Parameters.sourceIfNoneMatch,
        Parameters.sourceContentMD5,
        Parameters.copySourceAuthorization,
        Parameters.sourceUrl,
        Parameters.sourceContentCrc64,
        Parameters.sourceRange1,
    ],
    isXML: true,
    serializer: xmlSerializer,
};
const commitBlockListOperationSpec = {
    path: "/{containerName}/{blob}",
    httpMethod: "PUT",
    responses: {
        201: {
            headersMapper: Mappers.BlockBlobCommitBlockListHeaders,
        },
        default: {
            bodyMapper: Mappers.StorageError,
            headersMapper: Mappers.BlockBlobCommitBlockListExceptionHeaders,
        },
    },
    requestBody: Parameters.blocks,
    queryParameters: [Parameters.timeoutInSeconds, Parameters.comp25],
    urlParameters: [Parameters.url],
    headerParameters: [
        Parameters.contentType,
        Parameters.accept,
        Parameters.version,
        Parameters.requestId,
        Parameters.metadata,
        Parameters.leaseId,
        Parameters.ifModifiedSince,
        Parameters.ifUnmodifiedSince,
        Parameters.encryptionKey,
        Parameters.encryptionKeySha256,
        Parameters.encryptionAlgorithm,
        Parameters.ifMatch,
        Parameters.ifNoneMatch,
        Parameters.ifTags,
        Parameters.blobCacheControl,
        Parameters.blobContentType,
        Parameters.blobContentMD5,
        Parameters.blobContentEncoding,
        Parameters.blobContentLanguage,
        Parameters.blobContentDisposition,
        Parameters.immutabilityPolicyExpiry,
        Parameters.immutabilityPolicyMode,
        Parameters.encryptionScope,
        Parameters.tier,
        Parameters.blobTagsString,
        Parameters.legalHold1,
        Parameters.transactionalContentMD5,
        Parameters.transactionalContentCrc64,
    ],
    isXML: true,
    contentType: "application/xml; charset=utf-8",
    mediaType: "xml",
    serializer: xmlSerializer,
};
const getBlockListOperationSpec = {
    path: "/{containerName}/{blob}",
    httpMethod: "GET",
    responses: {
        200: {
            bodyMapper: Mappers.BlockList,
            headersMapper: Mappers.BlockBlobGetBlockListHeaders,
        },
        default: {
            bodyMapper: Mappers.StorageError,
            headersMapper: Mappers.BlockBlobGetBlockListExceptionHeaders,
        },
    },
    queryParameters: [
        Parameters.timeoutInSeconds,
        Parameters.snapshot,
        Parameters.comp25,
        Parameters.listType,
    ],
    urlParameters: [Parameters.url],
    headerParameters: [
        Parameters.version,
        Parameters.requestId,
        Parameters.accept1,
        Parameters.leaseId,
        Parameters.ifTags,
    ],
    isXML: true,
    serializer: xmlSerializer,
};
//# sourceMappingURL=blockBlob.js.map
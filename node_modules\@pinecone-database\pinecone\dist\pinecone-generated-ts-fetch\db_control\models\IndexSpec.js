"use strict";
/* tslint:disable */
/* eslint-disable */
/**
 * Pinecone Control Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.IndexSpecToJSON = exports.IndexSpecFromJSONTyped = exports.IndexSpecFromJSON = exports.instanceOfIndexSpec = void 0;
var runtime_1 = require("../runtime");
var PodSpec_1 = require("./PodSpec");
var ServerlessSpec_1 = require("./ServerlessSpec");
/**
 * Check if a given object implements the IndexSpec interface.
 */
function instanceOfIndexSpec(value) {
    var isInstance = true;
    return isInstance;
}
exports.instanceOfIndexSpec = instanceOfIndexSpec;
function IndexSpecFromJSON(json) {
    return IndexSpecFromJSONTyped(json, false);
}
exports.IndexSpecFromJSON = IndexSpecFromJSON;
function IndexSpecFromJSONTyped(json, ignoreDiscriminator) {
    if ((json === undefined) || (json === null)) {
        return json;
    }
    return {
        'serverless': !(0, runtime_1.exists)(json, 'serverless') ? undefined : (0, ServerlessSpec_1.ServerlessSpecFromJSON)(json['serverless']),
        'pod': !(0, runtime_1.exists)(json, 'pod') ? undefined : (0, PodSpec_1.PodSpecFromJSON)(json['pod']),
    };
}
exports.IndexSpecFromJSONTyped = IndexSpecFromJSONTyped;
function IndexSpecToJSON(value) {
    if (value === undefined) {
        return undefined;
    }
    if (value === null) {
        return null;
    }
    return {
        'serverless': (0, ServerlessSpec_1.ServerlessSpecToJSON)(value.serverless),
        'pod': (0, PodSpec_1.PodSpecToJSON)(value.pod),
    };
}
exports.IndexSpecToJSON = IndexSpecToJSON;
//# sourceMappingURL=IndexSpec.js.map
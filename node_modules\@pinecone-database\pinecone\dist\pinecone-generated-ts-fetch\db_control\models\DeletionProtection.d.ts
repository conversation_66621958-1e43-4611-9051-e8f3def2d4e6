/**
 * Pinecone Control Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
/**
 * Whether [deletion protection](http://docs.pinecone.io/guides/indexes/prevent-index-deletion) is enabled/disabled for the index.
 * @export
 */
export declare const DeletionProtection: {
    readonly Disabled: "disabled";
    readonly Enabled: "enabled";
};
export type DeletionProtection = typeof DeletionProtection[keyof typeof DeletionProtection];
export declare function DeletionProtectionFromJSON(json: any): DeletionProtection;
export declare function DeletionProtectionFromJSONTyped(json: any, ignoreDiscriminator: boolean): DeletionProtection;
export declare function DeletionProtectionToJSON(value?: DeletionProtection | null): any;

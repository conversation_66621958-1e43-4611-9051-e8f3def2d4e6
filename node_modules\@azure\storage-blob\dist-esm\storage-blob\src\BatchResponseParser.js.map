{"version": 3, "file": "BatchResponseParser.js", "sourceRoot": "", "sources": ["../../../src/BatchResponseParser.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,iBAAiB,EAAE,MAAM,2BAA2B,CAAC;AAC9D,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAG5D,OAAO,EACL,gBAAgB,EAChB,gBAAgB,EAChB,eAAe,EACf,iBAAiB,GAClB,MAAM,mBAAmB,CAAC;AAC3B,OAAO,EAAE,aAAa,EAAE,MAAM,cAAc,CAAC;AAG7C,OAAO,EAAE,MAAM,EAAE,MAAM,OAAO,CAAC;AAE/B,MAAM,qBAAqB,GAAG,IAAI,CAAC;AACnC,MAAM,eAAe,GAAG,GAAG,CAAC;AAC5B,MAAM,SAAS,GAAG,CAAC,CAAC,CAAC;AAErB;;GAEG;AACH,MAAM,OAAO,mBAAmB;IAO9B,YACE,aAA8C,EAC9C,WAAyC;QAEzC,IAAI,CAAC,aAAa,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,CAAC;YACjD,+FAA+F;YAC/F,MAAM,IAAI,UAAU,CAAC,mEAAmE,CAAC,CAAC;QAC5F,CAAC;QAED,IAAI,CAAC,WAAW,IAAI,WAAW,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;YAC3C,wCAAwC;YACxC,MAAM,IAAI,UAAU,CAAC,0DAA0D,CAAC,CAAC;QACnF,CAAC;QAED,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,aAAa,CAAC,WAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3E,IAAI,CAAC,iBAAiB,GAAG,KAAK,IAAI,CAAC,qBAAqB,GAAG,gBAAgB,EAAE,CAAC;QAC9E,IAAI,CAAC,mBAAmB,GAAG,KAAK,IAAI,CAAC,qBAAqB,IAAI,CAAC;IACjE,CAAC;IAED,yHAAyH;IAClH,KAAK,CAAC,kBAAkB;QAC7B,uGAAuG;QACvG,0BAA0B;QAC1B,IAAI,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,MAAM,KAAK,iBAAiB,CAAC,aAAa,EAAE,CAAC;YAC5E,MAAM,IAAI,KAAK,CACb,qDAAqD,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,MAAM,IAAI,CAC7F,CAAC;QACJ,CAAC;QAED,MAAM,kBAAkB,GAAG,MAAM,aAAa,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAEnE,MAAM,YAAY,GAAG,kBAAkB;aACpC,KAAK,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,iCAAiC;aACpE,KAAK,CAAC,IAAI,CAAC,iBAAiB,CAAC;aAC7B,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,mDAAmD;QAChE,MAAM,gBAAgB,GAAG,YAAY,CAAC,MAAM,CAAC;QAE7C,uDAAuD;QACvD,4EAA4E;QAC5E,+FAA+F;QAC/F,uGAAuG;QACvG,IAAI,gBAAgB,KAAK,IAAI,CAAC,WAAW,CAAC,IAAI,IAAI,gBAAgB,KAAK,CAAC,EAAE,CAAC;YACzE,MAAM,IAAI,KAAK,CAAC,0EAA0E,CAAC,CAAC;QAC9F,CAAC;QAED,MAAM,wBAAwB,GAA4B,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC;QACtF,IAAI,0BAA0B,GAAW,CAAC,CAAC;QAC3C,IAAI,uBAAuB,GAAW,CAAC,CAAC;QAExC,0BAA0B;QAC1B,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,gBAAgB,EAAE,KAAK,EAAE,EAAE,CAAC;YACtD,MAAM,WAAW,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;YACxC,MAAM,uBAAuB,GAAG,EAAsB,CAAC;YACvD,uBAAuB,CAAC,OAAO,GAAG,iBAAiB,CAAC,iBAAiB,EAAE,CAAC,CAAC;YAEzE,MAAM,aAAa,GAAG,WAAW,CAAC,KAAK,CAAC,GAAG,gBAAgB,EAAE,CAAC,CAAC;YAC/D,IAAI,uBAAuB,GAAG,KAAK,CAAC;YACpC,IAAI,qBAAqB,GAAG,KAAK,CAAC;YAClC,IAAI,aAAa,GAAG,KAAK,CAAC;YAC1B,IAAI,SAAS,GAAG,SAAS,CAAC;YAE1B,KAAK,MAAM,YAAY,IAAI,aAAa,EAAE,CAAC;gBACzC,IAAI,CAAC,uBAAuB,EAAE,CAAC;oBAC7B,yCAAyC;oBACzC,IAAI,YAAY,CAAC,UAAU,CAAC,eAAe,CAAC,UAAU,CAAC,EAAE,CAAC;wBACxD,SAAS,GAAG,QAAQ,CAAC,YAAY,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACrE,CAAC;oBAED,oFAAoF;oBACpF,iCAAiC;oBACjC,IAAI,YAAY,CAAC,UAAU,CAAC,gBAAgB,CAAC,EAAE,CAAC;wBAC9C,uBAAuB,GAAG,IAAI,CAAC;wBAE/B,MAAM,MAAM,GAAG,YAAY,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;wBACnD,uBAAuB,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;wBACrD,uBAAuB,CAAC,aAAa,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;oBAChF,CAAC;oBAED,SAAS,CAAC,iHAAiH;gBAC7H,CAAC;gBAED,IAAI,YAAY,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC;oBAC/B,4GAA4G;oBAC5G,IAAI,CAAC,qBAAqB,EAAE,CAAC;wBAC3B,qBAAqB,GAAG,IAAI,CAAC;oBAC/B,CAAC;oBAED,SAAS,CAAC,kBAAkB;gBAC9B,CAAC;gBAED,2EAA2E;gBAC3E,IAAI,CAAC,qBAAqB,EAAE,CAAC;oBAC3B,IAAI,YAAY,CAAC,OAAO,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;wBACvD,2DAA2D;wBAC3D,MAAM,IAAI,KAAK,CACb,uCAAuC,YAAY,oCAAoC,qBAAqB,IAAI,CACjH,CAAC;oBACJ,CAAC;oBAED,iCAAiC;oBACjC,MAAM,MAAM,GAAG,YAAY,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC;oBACzD,uBAAuB,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC1D,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,eAAe,CAAC,eAAe,EAAE,CAAC;wBAClD,uBAAuB,CAAC,SAAS,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;wBAC9C,aAAa,GAAG,IAAI,CAAC;oBACvB,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,iCAAiC;oBACjC,IAAI,CAAC,uBAAuB,CAAC,UAAU,EAAE,CAAC;wBACxC,uBAAuB,CAAC,UAAU,GAAG,EAAE,CAAC;oBAC1C,CAAC;oBAED,uBAAuB,CAAC,UAAU,IAAI,YAAY,CAAC;gBACrD,CAAC;YACH,CAAC,CAAC,gBAAgB;YAElB,kHAAkH;YAClH,uHAAuH;YACvH,oHAAoH;YACpH,uHAAuH;YACvH,IACE,SAAS,KAAK,SAAS;gBACvB,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC;gBAC3B,SAAS,IAAI,CAAC;gBACd,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI;gBACjC,wBAAwB,CAAC,SAAS,CAAC,KAAK,SAAS,EACjD,CAAC;gBACD,uBAAuB,CAAC,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,SAAS,CAAE,CAAC;gBACpE,wBAAwB,CAAC,SAAS,CAAC,GAAG,uBAAuB,CAAC;YAChE,CAAC;iBAAM,CAAC;gBACN,MAAM,CAAC,KAAK,CACV,gBAAgB,KAAK,uEAAuE,SAAS,EAAE,CACxG,CAAC;YACJ,CAAC;YAED,IAAI,aAAa,EAAE,CAAC;gBAClB,uBAAuB,EAAE,CAAC;YAC5B,CAAC;iBAAM,CAAC;gBACN,0BAA0B,EAAE,CAAC;YAC/B,CAAC;QACH,CAAC;QAED,OAAO;YACL,YAAY,EAAE,wBAAwB;YACtC,0BAA0B,EAAE,0BAA0B;YACtD,uBAAuB,EAAE,uBAAuB;SACjD,CAAC;IACJ,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport { createHttpHeaders } from \"@azure/core-rest-pipeline\";\nimport { toHttpHeadersLike } from \"@azure/core-http-compat\";\n\nimport type { ServiceSubmitBatchResponseModel } from \"./generatedModels\";\nimport {\n  HTTP_VERSION_1_1,\n  HTTP_LINE_ENDING,\n  HeaderConstants,\n  HTTPURLConnection,\n} from \"./utils/constants\";\nimport { getBodyAsText } from \"./BatchUtils\";\nimport type { BatchSubRequest } from \"./BlobBatch\";\nimport type { BatchSubResponse, ParsedBatchResponse } from \"./BatchResponse\";\nimport { logger } from \"./log\";\n\nconst HTTP_HEADER_DELIMITER = \": \";\nconst SPACE_DELIMITER = \" \";\nconst NOT_FOUND = -1;\n\n/**\n * Util class for parsing batch response.\n */\nexport class BatchResponseParser {\n  private readonly batchResponse: ServiceSubmitBatchResponseModel;\n  private readonly responseBatchBoundary: string;\n  private readonly perResponsePrefix: string;\n  private readonly batchResponseEnding: string;\n  private readonly subRequests: Map<number, BatchSubRequest>;\n\n  constructor(\n    batchResponse: ServiceSubmitBatchResponseModel,\n    subRequests: Map<number, BatchSubRequest>,\n  ) {\n    if (!batchResponse || !batchResponse.contentType) {\n      // In special case(reported), server may return invalid content-type which could not be parsed.\n      throw new RangeError(\"batchResponse is malformed or doesn't contain valid content-type.\");\n    }\n\n    if (!subRequests || subRequests.size === 0) {\n      // This should be prevent during coding.\n      throw new RangeError(\"Invalid state: subRequests is not provided or size is 0.\");\n    }\n\n    this.batchResponse = batchResponse;\n    this.subRequests = subRequests;\n    this.responseBatchBoundary = this.batchResponse.contentType!.split(\"=\")[1];\n    this.perResponsePrefix = `--${this.responseBatchBoundary}${HTTP_LINE_ENDING}`;\n    this.batchResponseEnding = `--${this.responseBatchBoundary}--`;\n  }\n\n  // For example of response, please refer to https://docs.microsoft.com/en-us/rest/api/storageservices/blob-batch#response\n  public async parseBatchResponse(): Promise<ParsedBatchResponse> {\n    // When logic reach here, suppose batch request has already succeeded with 202, so we can further parse\n    // sub request's response.\n    if (this.batchResponse._response.status !== HTTPURLConnection.HTTP_ACCEPTED) {\n      throw new Error(\n        `Invalid state: batch request failed with status: '${this.batchResponse._response.status}'.`,\n      );\n    }\n\n    const responseBodyAsText = await getBodyAsText(this.batchResponse);\n\n    const subResponses = responseBodyAsText\n      .split(this.batchResponseEnding)[0] // string after ending is useless\n      .split(this.perResponsePrefix)\n      .slice(1); // string before first response boundary is useless\n    const subResponseCount = subResponses.length;\n\n    // Defensive coding in case of potential error parsing.\n    // Note: subResponseCount == 1 is special case where sub request is invalid.\n    // We try to prevent such cases through early validation, e.g. validate sub request count >= 1.\n    // While in unexpected sub request invalid case, we allow sub response to be parsed and return to user.\n    if (subResponseCount !== this.subRequests.size && subResponseCount !== 1) {\n      throw new Error(\"Invalid state: sub responses' count is not equal to sub requests' count.\");\n    }\n\n    const deserializedSubResponses: Array<BatchSubResponse> = new Array(subResponseCount);\n    let subResponsesSucceededCount: number = 0;\n    let subResponsesFailedCount: number = 0;\n\n    // Parse sub subResponses.\n    for (let index = 0; index < subResponseCount; index++) {\n      const subResponse = subResponses[index];\n      const deserializedSubResponse = {} as BatchSubResponse;\n      deserializedSubResponse.headers = toHttpHeadersLike(createHttpHeaders());\n\n      const responseLines = subResponse.split(`${HTTP_LINE_ENDING}`);\n      let subRespHeaderStartFound = false;\n      let subRespHeaderEndFound = false;\n      let subRespFailed = false;\n      let contentId = NOT_FOUND;\n\n      for (const responseLine of responseLines) {\n        if (!subRespHeaderStartFound) {\n          // Convention line to indicate content ID\n          if (responseLine.startsWith(HeaderConstants.CONTENT_ID)) {\n            contentId = parseInt(responseLine.split(HTTP_HEADER_DELIMITER)[1]);\n          }\n\n          // Http version line with status code indicates the start of sub request's response.\n          // Example: HTTP/1.1 202 Accepted\n          if (responseLine.startsWith(HTTP_VERSION_1_1)) {\n            subRespHeaderStartFound = true;\n\n            const tokens = responseLine.split(SPACE_DELIMITER);\n            deserializedSubResponse.status = parseInt(tokens[1]);\n            deserializedSubResponse.statusMessage = tokens.slice(2).join(SPACE_DELIMITER);\n          }\n\n          continue; // Skip convention headers not specifically for sub request i.e. Content-Type: application/http and Content-ID: *\n        }\n\n        if (responseLine.trim() === \"\") {\n          // Sub response's header start line already found, and the first empty line indicates header end line found.\n          if (!subRespHeaderEndFound) {\n            subRespHeaderEndFound = true;\n          }\n\n          continue; // Skip empty line\n        }\n\n        // Note: when code reach here, it indicates subRespHeaderStartFound == true\n        if (!subRespHeaderEndFound) {\n          if (responseLine.indexOf(HTTP_HEADER_DELIMITER) === -1) {\n            // Defensive coding to prevent from missing valuable lines.\n            throw new Error(\n              `Invalid state: find non-empty line '${responseLine}' without HTTP header delimiter '${HTTP_HEADER_DELIMITER}'.`,\n            );\n          }\n\n          // Parse headers of sub response.\n          const tokens = responseLine.split(HTTP_HEADER_DELIMITER);\n          deserializedSubResponse.headers.set(tokens[0], tokens[1]);\n          if (tokens[0] === HeaderConstants.X_MS_ERROR_CODE) {\n            deserializedSubResponse.errorCode = tokens[1];\n            subRespFailed = true;\n          }\n        } else {\n          // Assemble body of sub response.\n          if (!deserializedSubResponse.bodyAsText) {\n            deserializedSubResponse.bodyAsText = \"\";\n          }\n\n          deserializedSubResponse.bodyAsText += responseLine;\n        }\n      } // Inner for end\n\n      // The response will contain the Content-ID header for each corresponding subrequest response to use for tracking.\n      // The Content-IDs are set to a valid index in the subrequests we sent. In the status code 202 path, we could expect it\n      // to be 1-1 mapping from the [0, subRequests.size) to the Content-IDs returned. If not, we simply don't return that\n      // unexpected subResponse in the parsed reponse and we can always look it up in the raw response for debugging purpose.\n      if (\n        contentId !== NOT_FOUND &&\n        Number.isInteger(contentId) &&\n        contentId >= 0 &&\n        contentId < this.subRequests.size &&\n        deserializedSubResponses[contentId] === undefined\n      ) {\n        deserializedSubResponse._request = this.subRequests.get(contentId)!;\n        deserializedSubResponses[contentId] = deserializedSubResponse;\n      } else {\n        logger.error(\n          `subResponses[${index}] is dropped as the Content-ID is not found or invalid, Content-ID: ${contentId}`,\n        );\n      }\n\n      if (subRespFailed) {\n        subResponsesFailedCount++;\n      } else {\n        subResponsesSucceededCount++;\n      }\n    }\n\n    return {\n      subResponses: deserializedSubResponses,\n      subResponsesSucceededCount: subResponsesSucceededCount,\n      subResponsesFailedCount: subResponsesFailedCount,\n    };\n  }\n}\n"]}
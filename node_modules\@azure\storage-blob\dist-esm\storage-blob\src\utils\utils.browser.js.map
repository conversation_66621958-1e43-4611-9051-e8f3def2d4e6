{"version": 3, "file": "utils.browser.js", "sourceRoot": "", "sources": ["../../../../src/utils/utils.browser.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC;;;;GAIG;AACH,MAAM,CAAC,KAAK,UAAU,iBAAiB,CAAC,IAAU;IAChD,MAAM,UAAU,GAAG,IAAI,UAAU,EAAE,CAAC;IACpC,OAAO,IAAI,OAAO,CAAc,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QAClD,UAAU,CAAC,SAAS,GAAG,CAAC,EAAO,EAAE,EAAE;YACjC,OAAO,CAAC,EAAE,CAAC,MAAO,CAAC,MAAM,CAAC,CAAC;QAC7B,CAAC,CAAC;QACF,UAAU,CAAC,OAAO,GAAG,MAAM,CAAC;QAC5B,UAAU,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;IACrC,CAAC,CAAC,CAAC;AACL,CAAC;AAED;;;;GAIG;AACH,MAAM,CAAC,KAAK,UAAU,YAAY,CAAC,IAAU;IAC3C,MAAM,UAAU,GAAG,IAAI,UAAU,EAAE,CAAC;IACpC,OAAO,IAAI,OAAO,CAAS,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QAC7C,UAAU,CAAC,SAAS,GAAG,CAAC,EAAO,EAAE,EAAE;YACjC,OAAO,CAAC,EAAE,CAAC,MAAO,CAAC,MAAM,CAAC,CAAC;QAC7B,CAAC,CAAC;QACF,UAAU,CAAC,OAAO,GAAG,MAAM,CAAC;QAC5B,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;IAC9B,CAAC,CAAC,CAAC;AACL,CAAC;AAED,MAAM,UAAU,cAAc;IAC5B,WAAW;AACb,CAAC;AAED,MAAM,UAAU,eAAe;IAC7B,WAAW;AACb,CAAC;AAED,MAAM,UAAU,qBAAqB;IACnC,WAAW;AACb,CAAC;AAED,MAAM,CAAC,MAAM,MAAM,GAAG,SAAS,IAAI;IACjC,WAAW;AACb,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,kBAAkB,GAAG,SAAS,gBAAgB;IACzD,WAAW;AACb,CAAC,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\n/**\n * Convert a Browser Blob object into ArrayBuffer.\n *\n * @param blob -\n */\nexport async function blobToArrayBuffer(blob: Blob): Promise<ArrayBuffer> {\n  const fileReader = new FileReader();\n  return new Promise<ArrayBuffer>((resolve, reject) => {\n    fileReader.onloadend = (ev: any) => {\n      resolve(ev.target!.result);\n    };\n    fileReader.onerror = reject;\n    fileReader.readAsArrayBuffer(blob);\n  });\n}\n\n/**\n * Convert a Browser Blob object into string.\n *\n * @param blob -\n */\nexport async function blobToString(blob: Blob): Promise<string> {\n  const fileReader = new FileReader();\n  return new Promise<string>((resolve, reject) => {\n    fileReader.onloadend = (ev: any) => {\n      resolve(ev.target!.result);\n    };\n    fileReader.onerror = reject;\n    fileReader.readAsText(blob);\n  });\n}\n\nexport function streamToBuffer(): void {\n  /* empty */\n}\n\nexport function streamToBuffer2(): void {\n  /* empty */\n}\n\nexport function readStreamToLocalFile(): void {\n  /* empty */\n}\n\nexport const fsStat = function stat(): void {\n  /* empty */\n};\n\nexport const fsCreateReadStream = function createReadStream(): void {\n  /* empty */\n};\n"]}
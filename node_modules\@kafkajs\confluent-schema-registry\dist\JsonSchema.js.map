{"version": 3, "file": "JsonSchema.js", "sourceRoot": "", "sources": ["../src/JsonSchema.ts"], "names": [], "mappings": ";;;;;AACA,8CAAqB;AACrB,qCAAiE;AAqBjE,MAAqB,UAAU;IAI7B,YAAY,MAA2B,EAAE,IAAkB;QACzD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;QAChD,IAAI,CAAC,kBAAkB,GAAG,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,kBAAkB,EAAC,CAAC,CAAC,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,kBAAkB,CAAC,CAAC,CAAC,KAAK,CAAA;IACvF,CAAC;IAEO,aAAa,CAAC,MAA2B,EAAE,IAAkB;;QACnE,MAAM,GAAG,GAAG,MAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,mCAAI,IAAI,aAAG,CAAC,IAAI,CAAC,CAAA;QAC9C,MAAM,iBAAiB,GAAG,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,iBAAiB,CAAA;QACjD,IAAI,iBAAiB,EAAE,CAAC;YACtB,iBAAiB,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;gBACpC,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;gBAC5C,GAAG,CAAC,SAAS,CAAC,OAAO,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAA;YACxC,CAAC,CAAC,CAAA;QACJ,CAAC;QACD,MAAM,QAAQ,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAA;QACvD,OAAO,QAAQ,CAAA;IACjB,CAAC;IAEO,eAAe,CAAC,OAAY;QAClC,MAAM,KAAK,GAAU,EAAE,CAAA;QAEvB,IACE,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;YACrB,SAAS,EAAE,CAAC,IAAI,EAAE,OAAO,EAAE,EAAE;gBAC3B,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;oBAC5B,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,CAAA;gBAC/B,CAAC;qBAAM,CAAC;oBACN,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;gBAClB,CAAC;YACH,CAAC;SACF,CAAC,EACF,CAAC;YACD,MAAM,IAAI,+CAAsC,CAAC,iBAAiB,EAAE,KAAK,CAAC,CAAA;QAC5E,CAAC;IACH,CAAC;IAEM,QAAQ,CAAC,OAAe;QAC7B,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAA;QAC7B,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAA;IAC7C,CAAC;IAEM,UAAU,CAAC,MAAc;QAC9B,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAA;QAC7C,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAA;QAC7B,OAAO,OAAO,CAAA;IAChB,CAAC;IAEM,OAAO,CACZ,OAAe,EACf,IAA2E;;QAE3E,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YAC5B,IAAI,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,SAAS,EAAE,CAAC;gBACpB,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,MAA8B,EAAE,CAAC;oBAC/D,MAAM,IAAI,GAAG,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,YAAY,CAAA;oBAChF,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,EAAE,MAAA,GAAG,CAAC,OAAO,mCAAI,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,MAAM,CAAC,CAAA;gBAC7D,CAAC;gBACD,OAAO,KAAK,CAAA;YACd,CAAC;QACH,CAAC;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAEO,uBAAuB,CAAC,KAAyB;QACvD,OAAQ,KAA+B,CAAC,QAAQ,IAAI,IAAI,CAAA;IAC1D,CAAC;CACF;AAtED,6BAsEC"}
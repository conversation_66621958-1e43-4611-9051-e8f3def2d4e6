import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { de_ListFlowExecutionsCommand, se_ListFlowExecutionsCommand } from "../protocols/Aws_restJson1";
export { $Command };
export class ListFlowExecutionsCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("AmazonBedrockAgentRunTimeService", "ListFlowExecutions", {})
    .n("BedrockAgentRuntimeClient", "ListFlowExecutionsCommand")
    .f(void 0, void 0)
    .ser(se_ListFlowExecutionsCommand)
    .de(de_ListFlowExecutionsCommand)
    .build() {
}

{"name": "@opentelemetry/api-logs", "version": "0.53.0", "description": "Public logs API for OpenTelemetry", "main": "build/src/index.js", "module": "build/esm/index.js", "esnext": "build/esnext/index.js", "types": "build/src/index.d.ts", "browser": {"./src/platform/index.ts": "./src/platform/browser/index.ts", "./build/esm/platform/index.js": "./build/esm/platform/browser/index.js", "./build/esnext/platform/index.js": "./build/esnext/platform/browser/index.js", "./build/src/platform/index.js": "./build/src/platform/browser/index.js"}, "repository": "open-telemetry/opentelemetry-js", "scripts": {"prepublishOnly": "npm run compile", "compile": "tsc --build tsconfig.json tsconfig.esm.json tsconfig.esnext.json", "clean": "tsc --build --clean tsconfig.json tsconfig.esm.json tsconfig.esnext.json", "test": "nyc mocha test/**/*.test.ts", "test:browser": "karma start --single-run", "codecov": "nyc report --reporter=json && codecov -f coverage/*.json -p ../../../", "codecov:browser": "nyc report --reporter=json && codecov -f coverage/*.json -p ../../../", "build": "npm run compile", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "version": "node ../../../scripts/version-update.js", "watch": "tsc --build --watch tsconfig.json tsconfig.esm.json tsconfig.esnext.json", "precompile": "cross-var lerna run version --scope $npm_package_name --include-dependencies", "prewatch": "node ../../../scripts/version-update.js", "align-api-deps": "node ../../../scripts/align-api-deps.js"}, "keywords": ["opentelemetry", "nodejs", "browser", "profiling", "logs", "stats", "monitoring"], "author": "OpenTelemetry Authors", "license": "Apache-2.0", "engines": {"node": ">=14"}, "files": ["build/esm/**/*.js", "build/esm/**/*.js.map", "build/esm/**/*.d.ts", "build/esnext/**/*.js", "build/esnext/**/*.js.map", "build/esnext/**/*.d.ts", "build/src/**/*.js", "build/src/**/*.js.map", "build/src/**/*.d.ts", "doc", "LICENSE", "README.md"], "publishConfig": {"access": "public"}, "dependencies": {"@opentelemetry/api": "^1.0.0"}, "devDependencies": {"@types/mocha": "10.0.7", "@types/node": "18.6.5", "@types/webpack-env": "1.16.3", "babel-plugin-istanbul": "7.0.0", "codecov": "3.8.3", "cross-var": "1.1.0", "karma": "6.4.4", "karma-chrome-launcher": "3.1.0", "karma-coverage": "2.2.1", "karma-mocha": "2.0.1", "karma-spec-reporter": "0.0.36", "karma-webpack": "5.0.1", "lerna": "6.6.2", "mocha": "10.7.3", "nyc": "15.1.0", "ts-loader": "9.5.1", "typescript": "4.4.4", "webpack": "5.89.0"}, "homepage": "https://github.com/open-telemetry/opentelemetry-js/tree/main/experimental/packages/api-logs", "sideEffects": false, "gitHead": "720bc8c70d47029cb6b41a34ffdc3d25cbaa2f80"}
{"version": 3, "file": "SchemaRegistry.js", "sourceRoot": "", "sources": ["../src/SchemaRegistry.ts"], "names": [], "mappings": ";;;;;AAGA,+CAAkD;AAClD,gEAAkC;AAClC,2CAA8D;AAC9D,gDAAiF;AACjF,oDAA2B;AAC3B,qCAKiB;AACjB,qCAciB;AACjB,6DAI6B;AAmB7B,MAAM,YAAY,GAAG;IACnB,aAAa,EAAE,yBAAa,CAAC,QAAQ;IACrC,SAAS,EAAE,6BAAiB;CAC7B,CAAA;AACD,MAAqB,cAAc;IAOjC,YACE,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,EAA+B,EAChF,OAAwC;QAPlC,sBAAiB,GAAyC,EAAE,CAAA;QASlE,IAAI,CAAC,GAAG,GAAG,IAAA,aAAG,EAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,EAAE,CAAC,CAAA;QACnE,IAAI,CAAC,KAAK,GAAG,IAAI,eAAK,EAAE,CAAA;QACxB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAA;IACxB,CAAC;IAEO,iBAAiB,CACvB,MAAoD;QAEpD,OAAQ,MAA0B,CAAC,MAAM,IAAI,IAAI,CAAA;IACnD,CAAC;IAEO,kBAAkB,CACxB,MAAoD;QAEpD,IAAI,eAAgC,CAAA;QACpC,0DAA0D;QAC1D,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,EAAE,CAAC;YACpC,mDAAmD;YACnD,eAAe,GAAG;gBAChB,IAAI,EAAE,mBAAU,CAAC,IAAI;gBACrB,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;aAC/B,CAAA;QACH,CAAC;aAAM,CAAC;YACN,eAAe,GAAG,MAAyB,CAAA;QAC7C,CAAC;QACD,OAAO,eAAe,CAAA;IACxB,CAAC;IAcM,KAAK,CAAC,QAAQ,CACnB,MAAuC,EACvC,QAAe;QAEf,MAAM,EAAE,aAAa,EAAE,SAAS,EAAE,GAAG,EAAE,GAAG,YAAY,EAAE,GAAG,QAAQ,EAAE,CAAA;QAErE,MAAM,eAAe,GAAoB,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAA;QAExE,MAAM,MAAM,GAAG,IAAA,6CAAwB,EAAC,eAAe,CAAC,IAAI,CAAC,CAAA;QAE7D,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,iCAAiC,CAAC,eAAe,EAAE,IAAI,CAAC,OAAO,CAAC,CAAA;QAC3F,MAAM,cAAc,GAAG,IAAA,8CAAyB,EAAC,eAAe,EAAE,OAAO,CAAC,CAAA;QAC1E,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAA;QAC/B,IAAI,uBAAuB,GAAG,KAAK,CAAA;QACnC,IAAI,OAAyB,CAAA;QAC7B,IAAI,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,OAAO,EAAE,CAAC;YACtB,OAAO,GAAG;gBACR,IAAI,EAAE,QAAQ,CAAC,OAAO;aACvB,CAAA;QACH,CAAC;aAAM,CAAC;YACN,OAAO,GAAG,MAAM,CAAC,UAAU,CAAC,eAAe,EAAE,cAAc,EAAE,SAAS,CAAC,CAAA;QACzE,CAAC;QAED,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,OAAO,EAAE,OAAO,CAAC,IAAI,EAAE,CAAC,CAAA;YACzE,MAAM,EAAE,kBAAkB,EAAE,GAA0C,QAAQ,CAAC,IAAI,EAAE,CAAA;YAErF,IAAI,kBAAkB,CAAC,WAAW,EAAE,KAAK,aAAa,EAAE,CAAC;gBACvD,MAAM,IAAI,kDAAyC,CACjD,mDAAmD,aAAa,OAAO,kBAAkB,CAAC,WAAW,EAAE,GAAG,CAC3G,CAAA;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,CAAC,CAAC,QAAQ,IAAI,KAAK,CAAC,IAAI,KAAK,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC;gBACxF,MAAM,KAAK,CAAA;YACb,CAAC;iBAAM,CAAC;gBACN,uBAAuB,GAAG,IAAI,CAAA;YAChC,CAAC;QACH,CAAC;QAED,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC;YAC/C,OAAO,EAAE,OAAO,CAAC,IAAI;YACrB,IAAI,EAAE;gBACJ,UAAU,EAAE,eAAe,CAAC,IAAI,KAAK,mBAAU,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,eAAe,CAAC,IAAI;gBACvF,MAAM,EAAE,eAAe,CAAC,MAAM;gBAC9B,UAAU,EAAE,eAAe,CAAC,UAAU;aACvC;SACF,CAAC,CAAA;QAEF,IAAI,aAAa,IAAI,uBAAuB,EAAE,CAAC;YAC7C,MAAM,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,OAAO,EAAE,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,aAAa,EAAE,EAAE,CAAC,CAAA;QACzF,CAAC;QAED,MAAM,gBAAgB,GAAqB,QAAQ,CAAC,IAAI,EAAE,CAAA;QAC1D,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,OAAO,CAAC,IAAI,EAAE,gBAAgB,CAAC,EAAE,CAAC,CAAA;QACjE,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,gBAAgB,CAAC,EAAE,EAAE,eAAe,CAAC,IAAI,EAAE,cAAc,CAAC,CAAA;QAE/E,OAAO,gBAAgB,CAAA;IACzB,CAAC;IAEO,KAAK,CAAC,iCAAiC,CAC7C,MAAuB,EACvB,OAAwC;QAExC,MAAM,MAAM,GAAG,IAAA,6CAAwB,EAAC,MAAM,CAAC,IAAI,CAAC,CAAA;QACpD,MAAM,iBAAiB,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;QAEzE,MAAM,eAAe,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAA;QACvD,OAAO,MAAM,CAAC,iCAAiC,CAAC,iBAAiB,EAAE,eAAe,CAAC,CAAA;IACrF,CAAC;IAEO,iBAAiB,CAAC,OAAwC;QAChE,IAAI,CAAC,CAAC,OAAyB,aAAzB,OAAO,uBAAP,OAAO,CAAoB,gBAAgB,CAAA,EAAE,CAAC;YAClD,OAAO,OAAsC,CAAA;QAC/C,CAAC;QACD,OAAO;YACL,CAAC,mBAAU,CAAC,IAAI,CAAC,EAAG,OAAyB,aAAzB,OAAO,uBAAP,OAAO,CAAoB,gBAAgB;SAChE,CAAA;IACH,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAChC,MAAuB,EACvB,MAAoB;QAEpB,MAAM,aAAa,GAAG,IAAI,GAAG,EAAU,CAAA;QACvC,OAAO,IAAI,CAAC,6BAA6B,CAAC,MAAM,EAAE,MAAM,EAAE,aAAa,CAAC,CAAA;IAC1E,CAAC;IAEO,KAAK,CAAC,6BAA6B,CACzC,MAAuB,EACvB,MAAoB,EACpB,aAA0B;QAE1B,MAAM,UAAU,GAAG,MAAM,CAAC,UAAU,IAAI,EAAE,CAAA;QAE1C,IAAI,iBAAiB,GAAsB,EAAE,CAAA;QAC7C,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE,CAAC;YACnC,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,iCAAiC,CAAC,SAAS,EAAE,MAAM,EAAE,aAAa,CAAC,CAAA;YAC9F,iBAAiB,GAAG,iBAAiB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;QACvD,CAAC;QACD,OAAO,iBAAiB,CAAA;IAC1B,CAAC;IAED,KAAK,CAAC,iCAAiC,CACrC,SAA0B,EAC1B,MAAoB,EACpB,aAA0B;QAE1B,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,SAAS,CAAA;QAC5C,MAAM,GAAG,GAAG,GAAG,IAAI,IAAI,OAAO,IAAI,OAAO,EAAE,CAAA;QAE3C,mBAAmB;QACnB,IAAI,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;YAC3B,OAAO,EAAE,CAAA;QACX,CAAC;QACD,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;QAEtB,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAA;QACjE,MAAM,WAAW,GAAG,eAAe,CAAC,IAAI,EAAoB,CAAA;QAE5D,MAAM,MAAM,GAAG,MAAM,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAA;QACpD,MAAM,iBAAiB,GAAG,MAAM,IAAI,CAAC,6BAA6B,CAChE,MAAM,EACN,MAAM,EACN,aAAa,CACd,CAAA;QAED,iBAAiB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;QAC9B,OAAO,iBAAiB,CAAA;IAC1B,CAAC;IAEO,KAAK,CAAC,UAAU,CACtB,UAAkB;QAElB,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,UAAU,CAAC,CAAA;QAEnD,IAAI,UAAU,EAAE,CAAC;YACf,OAAO,UAAU,CAAA;QACnB,CAAC;QAED,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,UAAU,CAAC,CAAA;QAC9D,MAAM,WAAW,GAAmB,QAAQ,CAAC,IAAI,EAAE,CAAA;QAEnD,MAAM,UAAU,GAAG,IAAA,yCAAoB,EAAC,WAAW,CAAC,UAAU,CAAC,CAAA;QAE/D,MAAM,MAAM,GAAG,IAAA,6CAAwB,EAAC,UAAU,CAAC,CAAA;QACnD,MAAM,eAAe,GAAG,MAAM,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAA;QAE7D,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,iCAAiC,CAAC,eAAe,EAAE,IAAI,CAAC,OAAO,CAAC,CAAA;QAC3F,MAAM,cAAc,GAAG,IAAA,8CAAyB,EAAC,eAAe,EAAE,OAAO,CAAC,CAAA;QAC1E,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,UAAU,EAAE,UAAU,EAAE,cAAc,CAAC,CAAA;IACrE,CAAC;IAEM,KAAK,CAAC,SAAS,CAAC,UAAkB;QACvC,OAAO,MAAM,CAAC,MAAM,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAA;IACzD,CAAC;IAEM,KAAK,CAAC,MAAM,CAAC,UAAkB,EAAE,OAAY;QAClD,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,MAAM,IAAI,6CAAoC,CAC5C,uBAAuB,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,EAAE,CACpD,CAAA;QACH,CAAC;QAED,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,CAAA;QACpD,IAAI,CAAC;YACH,MAAM,iBAAiB,GAAG,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAA;YAClD,OAAO,IAAA,oBAAM,EAAC,UAAU,EAAE,iBAAiB,CAAC,CAAA;QAC9C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,+CAAsC;gBAAE,MAAM,KAAK,CAAA;YAExE,MAAM,KAAK,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;YACvD,MAAM,IAAI,+CAAsC,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;QAChE,CAAC;IACH,CAAC;IAEO,mBAAmB,CAAC,MAAc,EAAE,WAAmB;QAC7D,MAAM,KAAK,GAAe,EAAE,CAAA;QAC5B,MAAM,CAAC,OAAO,CAAC,WAAW,EAAE;YAC1B,SAAS,EAAE,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;SACpC,CAAC,CAAA;QAEF,OAAO,KAAK,CAAA;IACd,CAAC;IAEM,KAAK,CAAC,MAAM,CAAC,MAAc,EAAE,OAAuB;;QACzD,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YAC7B,MAAM,IAAI,6CAAoC,CAAC,gBAAgB,CAAC,CAAA;QAClE,CAAC;QAED,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,GAAG,IAAA,qBAAM,EAAC,MAAM,CAAC,CAAA;QACzD,IAAI,MAAM,CAAC,OAAO,CAAC,wBAAU,EAAE,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC;YAChD,MAAM,IAAI,6CAAoC,CAC5C,mCAAmC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,cAAc,IAAI,CAAC,SAAS,CACtF,wBAAU,CACX,EAAE,CACJ,CAAA;QACH,CAAC;QAED,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,YAAY,EAAE,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,CAAA;QAExE,IAAI,eAAe,CAAA;QACnB,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,mBAAU,CAAC,IAAI;gBAClB,eAAe,GAAG,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAG,mBAAU,CAAC,IAAI,CAAC,0CAAE,YAA0C,CAAA;QAC5F,CAAC;QACD,IAAI,eAAe,EAAE,CAAC;YACpB,MAAM,YAAY,GAAG,IAAA,8CAAyB,EAC5C,EAAE,IAAI,EAAE,mBAAU,CAAC,IAAI,EAAE,MAAM,EAAE,eAAe,EAAE,EAClD,IAAI,CAAC,OAAO,CACC,CAAA;YACf,IAAI,YAAY,CAAC,MAAM,CAAC,YAAoB,CAAC,EAAE,CAAC;gBAC9C;;;iEAGiD;gBACjD,OAAO,YAAY,CAAC,UAAU,CAAC,OAAO,CAAC,CAAA;YACzC,CAAC;iBAAM,CAAC;gBACN,4DAA4D;gBAC5D,OAAO,YAAY,CAAC,UAAU,CAAC,OAAO,EAAE,YAAY,CAAC,cAAc,CAAC,YAAoB,CAAC,CAAC,CAAA;YAC5F,CAAC;QACH,CAAC;QAED,OAAO,YAAY,CAAC,UAAU,CAAC,OAAO,CAAC,CAAA;IACzC,CAAC;IAEM,KAAK,CAAC,aAAa,CAAC,OAAe,EAAE,OAAwB;QAClE,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,CAAA;QACrE,MAAM,EAAE,EAAE,EAAE,GAAmB,QAAQ,CAAC,IAAI,EAAE,CAAA;QAE9C,OAAO,EAAE,CAAA;IACX,CAAC;IAEM,KAAK,CAAC,qBAAqB,CAChC,OAAe,EACf,MAAoD;QAEpD,IAAI,CAAC;YACH,MAAM,eAAe,GAAoB,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAA;YACxE,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC;gBACjD,OAAO;gBACP,IAAI,EAAE;oBACJ,UAAU,EAAE,eAAe,CAAC,IAAI,KAAK,mBAAU,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,eAAe,CAAC,IAAI;oBACvF,MAAM,EAAE,eAAe,CAAC,MAAM;iBAC/B;aACF,CAAC,CAAA;YACF,MAAM,EAAE,EAAE,EAAE,GAAmB,QAAQ,CAAC,IAAI,EAAE,CAAA;YAE9C,OAAO,EAAE,CAAA;QACX,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,QAAQ,IAAI,KAAK,IAAI,KAAK,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC;gBACpF,MAAM,IAAI,qCAA4B,CAAC,KAAK,CAAC,CAAA;YAC/C,CAAC;YAED,MAAM,KAAK,CAAA;QACb,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,iBAAiB,CAAC,OAAe;QAC5C,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE,OAAO,EAAE,CAAC,CAAA;QAClE,MAAM,EAAE,EAAE,EAAE,GAAmB,QAAQ,CAAC,IAAI,EAAE,CAAA;QAE9C,OAAO,EAAE,CAAA;IACX,CAAC;IAEO,KAAK,CAAC,sBAAsB,CAAC,UAAkB;QACrD,6DAA6D;QAC7D,MAAM,GAAG,GAAG,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAA;QAC9C,IAAI,GAAG;YAAE,OAAO,GAAG,CAAA;QAEnB,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,UAAU,EAAE,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE;YACpE,OAAO,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAA;QAC3C,CAAC,CAAC,CAAA;QAEF,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,GAAG,OAAO,CAAA;QAE5C,OAAO,OAAO,CAAA;IAChB,CAAC;CACF;AAzUD,iCAyUC"}
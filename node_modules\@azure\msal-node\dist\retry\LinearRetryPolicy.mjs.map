{"version": 3, "file": "LinearRetryPolicy.mjs", "sources": ["../../src/retry/LinearRetryPolicy.ts"], "sourcesContent": [null], "names": [], "mappings": ";;AAAA;;;AAGG;MAKU,iBAAiB,CAAA;AAK1B,IAAA,WAAA,CACI,UAAkB,EAClB,UAAkB,EAClB,wBAAuC,EAAA;AAEvC,QAAA,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;AAC7B,QAAA,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;AAC7B,QAAA,IAAI,CAAC,wBAAwB,GAAG,wBAAwB,CAAC;KAC5D;AAEO,IAAA,6BAA6B,CACjC,WAAoD,EAAA;QAEpD,IAAI,CAAC,WAAW,EAAE;AACd,YAAA,OAAO,CAAC,CAAC;AACZ,SAAA;;AAGD,QAAA,IAAI,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC,CAAC;AAE/D;;;AAGG;AACH,QAAA,IAAI,KAAK,CAAC,aAAa,CAAC,EAAE;AACtB,YAAA,aAAa,GAAG,IAAI,CAAC,GAAG,CACpB,CAAC;;AAED,YAAA,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC,OAAO,EAAE,GAAG,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,CACzD,CAAC;AACL,SAAA;AAED,QAAA,OAAO,aAAa,CAAC;KACxB;AAED,IAAA,MAAM,aAAa,CACf,cAAsB,EACtB,YAAoB,EACpB,gBAAyD,EAAA;AAEzD,QAAA,IACI,IAAI,CAAC,wBAAwB,CAAC,QAAQ,CAAC,cAAc,CAAC;AACtD,YAAA,YAAY,GAAG,IAAI,CAAC,UAAU,EAChC;YACE,MAAM,eAAe,GACjB,IAAI,CAAC,6BAA6B,CAAC,gBAAgB,CAAC,CAAC;AAEzD,YAAA,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,KAAI;;gBAE1B,OAAO,UAAU,CAAC,OAAO,EAAE,eAAe,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC;AACnE,aAAC,CAAC,CAAC;AAEH,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;AAED,QAAA,OAAO,KAAK,CAAC;KAChB;AACJ;;;;"}
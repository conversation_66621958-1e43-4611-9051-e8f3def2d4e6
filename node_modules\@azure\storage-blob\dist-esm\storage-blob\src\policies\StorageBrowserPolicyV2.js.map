{"version": 3, "file": "StorageBrowserPolicyV2.js", "sourceRoot": "", "sources": ["../../../../src/policies/StorageBrowserPolicyV2.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAQlC,OAAO,EAAE,MAAM,EAAE,MAAM,kBAAkB,CAAC;AAC1C,OAAO,EAAE,eAAe,EAAE,YAAY,EAAE,MAAM,oBAAoB,CAAC;AACnE,OAAO,EAAE,eAAe,EAAE,MAAM,uBAAuB,CAAC;AAExD;;GAEG;AACH,MAAM,CAAC,MAAM,wBAAwB,GAAG,sBAAsB,CAAC;AAE/D;;;GAGG;AACH,MAAM,UAAU,oBAAoB;IAClC,OAAO;QACL,IAAI,EAAE,wBAAwB;QAC9B,KAAK,CAAC,WAAW,CAAC,OAAwB,EAAE,IAAiB;YAC3D,IAAI,MAAM,EAAE,CAAC;gBACX,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;YACvB,CAAC;YAED,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,IAAI,OAAO,CAAC,MAAM,KAAK,MAAM,EAAE,CAAC;gBAC1D,OAAO,CAAC,GAAG,GAAG,eAAe,CAC3B,OAAO,CAAC,GAAG,EACX,YAAY,CAAC,UAAU,CAAC,sBAAsB,EAC9C,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,CAChC,CAAC;YACJ,CAAC;YAED,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;YAE/C,oFAAoF;YACpF,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC;YACvD,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;QACvB,CAAC;KACF,CAAC;AACJ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type {\n  PipelineRequest,\n  PipelineResponse,\n  SendRequest,\n  PipelinePolicy,\n} from \"@azure/core-rest-pipeline\";\nimport { isNode } from \"@azure/core-util\";\nimport { HeaderConstants, URLConstants } from \"../utils/constants\";\nimport { setURLParameter } from \"../utils/utils.common\";\n\n/**\n * The programmatic identifier of the StorageBrowserPolicy.\n */\nexport const storageBrowserPolicyName = \"storageBrowserPolicy\";\n\n/**\n * storageBrowserPolicy is a policy used to prevent browsers from caching requests\n * and to remove cookies and explicit content-length headers.\n */\nexport function storageBrowserPolicy(): PipelinePolicy {\n  return {\n    name: storageBrowserPolicyName,\n    async sendRequest(request: PipelineRequest, next: SendRequest): Promise<PipelineResponse> {\n      if (isNode) {\n        return next(request);\n      }\n\n      if (request.method === \"GET\" || request.method === \"HEAD\") {\n        request.url = setURLParameter(\n          request.url,\n          URLConstants.Parameters.FORCE_BROWSER_NO_CACHE,\n          new Date().getTime().toString(),\n        );\n      }\n\n      request.headers.delete(HeaderConstants.COOKIE);\n\n      // According to XHR standards, content-length should be fully controlled by browsers\n      request.headers.delete(HeaderConstants.CONTENT_LENGTH);\n      return next(request);\n    },\n  };\n}\n"]}
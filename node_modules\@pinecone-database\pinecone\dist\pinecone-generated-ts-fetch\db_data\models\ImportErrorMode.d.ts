/**
 * Pinecone Data Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
/**
 * Indicates how to respond to errors during the import process.
 * @export
 * @interface ImportErrorMode
 */
export interface ImportErrorMode {
    /**
     * Indicates how to respond to errors during the import process.
     * @type {string}
     * @memberof ImportErrorMode
     */
    onError?: ImportErrorModeOnErrorEnum;
}
/**
 * @export
 */
export declare const ImportErrorModeOnErrorEnum: {
    readonly Abort: "abort";
    readonly Continue: "continue";
};
export type ImportErrorModeOnErrorEnum = typeof ImportErrorModeOnErrorEnum[keyof typeof ImportErrorModeOnErrorEnum];
/**
 * Check if a given object implements the ImportErrorMode interface.
 */
export declare function instanceOfImportErrorMode(value: object): boolean;
export declare function ImportErrorModeFromJSON(json: any): ImportErrorMode;
export declare function ImportErrorModeFromJSONTyped(json: any, ignoreDiscriminator: boolean): ImportErrorMode;
export declare function ImportErrorModeToJSON(value?: ImportErrorMode | null): any;

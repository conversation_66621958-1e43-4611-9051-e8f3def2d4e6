"use strict";
/* tslint:disable */
/* eslint-disable */
/**
 * Pinecone Data Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProtobufNullValueToJSON = exports.ProtobufNullValueFromJSONTyped = exports.ProtobufNullValueFromJSON = exports.ProtobufNullValue = void 0;
/**
 * `NullValue` is a singleton enumeration to represent the null value for the `Value` type union.
 * The JSON representation for `NullValue` is JSON `null`.
 * @export
 */
exports.ProtobufNullValue = {
    NullValue: 'NULL_VALUE'
};
function ProtobufNullValueFromJSON(json) {
    return ProtobufNullValueFromJSONTyped(json, false);
}
exports.ProtobufNullValueFromJSON = ProtobufNullValueFromJSON;
function ProtobufNullValueFromJSONTyped(json, ignoreDiscriminator) {
    return json;
}
exports.ProtobufNullValueFromJSONTyped = ProtobufNullValueFromJSONTyped;
function ProtobufNullValueToJSON(value) {
    return value;
}
exports.ProtobufNullValueToJSON = ProtobufNullValueToJSON;
//# sourceMappingURL=ProtobufNullValue.js.map
"use strict";
/* tslint:disable */
/* eslint-disable */
/**
 * Pinecone Inference API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.RerankResultToJSON = exports.RerankResultFromJSONTyped = exports.RerankResultFromJSON = exports.instanceOfRerankResult = void 0;
var RankedDocument_1 = require("./RankedDocument");
var RerankResultUsage_1 = require("./RerankResultUsage");
/**
 * Check if a given object implements the RerankResult interface.
 */
function instanceOfRerankResult(value) {
    var isInstance = true;
    isInstance = isInstance && "model" in value;
    isInstance = isInstance && "data" in value;
    isInstance = isInstance && "usage" in value;
    return isInstance;
}
exports.instanceOfRerankResult = instanceOfRerankResult;
function RerankResultFromJSON(json) {
    return RerankResultFromJSONTyped(json, false);
}
exports.RerankResultFromJSON = RerankResultFromJSON;
function RerankResultFromJSONTyped(json, ignoreDiscriminator) {
    if ((json === undefined) || (json === null)) {
        return json;
    }
    return {
        'model': json['model'],
        'data': (json['data'].map(RankedDocument_1.RankedDocumentFromJSON)),
        'usage': (0, RerankResultUsage_1.RerankResultUsageFromJSON)(json['usage']),
    };
}
exports.RerankResultFromJSONTyped = RerankResultFromJSONTyped;
function RerankResultToJSON(value) {
    if (value === undefined) {
        return undefined;
    }
    if (value === null) {
        return null;
    }
    return {
        'model': value.model,
        'data': (value.data.map(RankedDocument_1.RankedDocumentToJSON)),
        'usage': (0, RerankResultUsage_1.RerankResultUsageToJSON)(value.usage),
    };
}
exports.RerankResultToJSON = RerankResultToJSON;
//# sourceMappingURL=RerankResult.js.map
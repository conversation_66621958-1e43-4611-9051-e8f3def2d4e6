<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🤖 Your AI Agent Interface</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
            border: 1px solid rgba(255, 255, 255, 0.18);
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .status {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid #4CAF50;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            text-align: center;
        }
        .chat-container {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            min-height: 300px;
            max-height: 400px;
            overflow-y: auto;
        }
        .input-container {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }
        input[type="text"] {
            flex: 1;
            padding: 15px;
            border: none;
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            font-size: 16px;
        }
        button {
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            background: #4CAF50;
            color: white;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        button:hover {
            background: #45a049;
            transform: translateY(-2px);
        }
        .message {
            margin: 10px 0;
            padding: 10px 15px;
            border-radius: 15px;
            max-width: 80%;
        }
        .user-message {
            background: rgba(33, 150, 243, 0.3);
            margin-left: auto;
            text-align: right;
        }
        .ai-message {
            background: rgba(76, 175, 80, 0.3);
            margin-right: auto;
        }
        .examples {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
        }
        .example-btn {
            display: inline-block;
            margin: 5px;
            padding: 8px 15px;
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        .example-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 Your AI Agent is Ready!</h1>
        
        <div class="status">
            ✅ <strong>Status:</strong> AI Agent Active and Ready to Chat!<br>
            🔗 <strong>Webhook:</strong> http://localhost:2410/webhook/a889d2ae-2159-402f-b326-5f61e90f602e
        </div>

        <div class="chat-container" id="chatContainer">
            <div class="message ai-message">
                👋 Hello! I'm your AI agent. I can help you with various tasks and answer questions. What would you like to know or do today?
            </div>
        </div>

        <div class="input-container">
            <input type="text" id="messageInput" placeholder="Type your message or instruction here..." onkeypress="handleKeyPress(event)">
            <button onclick="sendMessage()">Send 🚀</button>
        </div>

        <div class="examples">
            <h3>💡 Try these examples:</h3>
            <div class="example-btn" onclick="setMessage('Create a chatbot for customer support')">Create a chatbot for customer support</div>
            <div class="example-btn" onclick="setMessage('Build an email automation workflow')">Build an email automation workflow</div>
            <div class="example-btn" onclick="setMessage('Generate social media content')">Generate social media content</div>
            <div class="example-btn" onclick="setMessage('Analyze customer feedback data')">Analyze customer feedback data</div>
            <div class="example-btn" onclick="setMessage('What can you help me with?')">What can you help me with?</div>
        </div>
    </div>

    <script>
        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }

        function setMessage(text) {
            document.getElementById('messageInput').value = text;
        }

        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message) return;

            // Add user message to chat
            addMessage(message, 'user');
            input.value = '';

            // Add loading message
            const loadingDiv = addMessage('🤔 Thinking...', 'ai');

            try {
                // Send to AI agent
                const response = await fetch('http://localhost:2410/webhook/a889d2ae-2159-402f-b326-5f61e90f602e', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        chatInput: message,
                        instruction: message
                    })
                });

                const result = await response.text();
                
                // Remove loading message
                loadingDiv.remove();
                
                // Add AI response
                addMessage(result || '✅ Message received! Your AI agent is processing your request.', 'ai');
                
            } catch (error) {
                loadingDiv.remove();
                addMessage('❌ Error connecting to AI agent. Make sure n8n is running on localhost:2410', 'ai');
            }
        }

        function addMessage(text, type) {
            const chatContainer = document.getElementById('chatContainer');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}-message`;
            messageDiv.textContent = text;
            chatContainer.appendChild(messageDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;
            return messageDiv;
        }
    </script>
</body>
</html>

# Import Meta AI Agent Builder workflow to n8n
$apiKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI0NDgyZmY2Yi0xNzcwLTRlZGQtOTY2Yy1hNTVjZTNkZDZmZGQiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzQ4NTAyNTMyfQ.Si6LRA-uLi8HIdWZIX6tKNXzrSJDd6gBqiRHwtf8K8M"
$url = "http://localhost:2410/api/v1/workflows"

# Read the workflow JSON
$workflowJson = Get-Content "meta-ai-agent-builder.json" -Raw

# Set headers
$headers = @{
    "Content-Type" = "application/json"
    "X-N8N-API-KEY" = $apiKey
}

try {
    Write-Host "Importing Meta AI Agent Builder workflow..."
    $response = Invoke-RestMethod -Uri $url -Method POST -Headers $headers -Body $workflowJson
    
    Write-Host "✅ SUCCESS! Workflow imported successfully!" -ForegroundColor Green
    Write-Host "Workflow ID: $($response.id)" -ForegroundColor Cyan
    Write-Host "Workflow Name: $($response.name)" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "🎉 Your Meta AI Agent Builder is now available in n8n!"
    Write-Host "Next steps:"
    Write-Host "1. Go to http://localhost:2410"
    Write-Host "2. Find 'Meta AI Agent Builder' in your workflows"
    Write-Host "3. Set up OpenAI credentials"
    Write-Host "4. Activate the workflow"
    Write-Host "5. Start creating AI agents with simple prompts!"
}
catch {
    Write-Host "❌ Error importing workflow:" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
    Write-Host ""
    Write-Host "💡 Alternative: Import manually through n8n interface"
    Write-Host "1. Go to http://localhost:2410"
    Write-Host "2. Click 'Add workflow'"
    Write-Host "3. Click '...' menu → 'Import from File'"
    Write-Host "4. Select 'meta-ai-agent-builder.json'"
}

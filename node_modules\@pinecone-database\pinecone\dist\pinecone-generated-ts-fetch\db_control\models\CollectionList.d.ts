/**
 * Pinecone Control Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import type { CollectionModel } from './CollectionModel';
/**
 * The list of collections that exist in the project.
 * @export
 * @interface CollectionList
 */
export interface CollectionList {
    /**
     *
     * @type {Array<CollectionModel>}
     * @memberof CollectionList
     */
    collections?: Array<CollectionModel>;
}
/**
 * Check if a given object implements the CollectionList interface.
 */
export declare function instanceOfCollectionList(value: object): boolean;
export declare function CollectionListFromJSON(json: any): CollectionList;
export declare function CollectionListFromJSONTyped(json: any, ignoreDiscriminator: boolean): CollectionList;
export declare function CollectionListToJSON(value?: CollectionList | null): any;

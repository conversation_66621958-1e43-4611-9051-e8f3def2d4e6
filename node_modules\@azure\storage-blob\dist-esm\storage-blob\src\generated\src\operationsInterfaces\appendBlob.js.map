{"version": 3, "file": "appendBlob.js", "sourceRoot": "", "sources": ["../../../../../../src/generated/src/operationsInterfaces/appendBlob.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG", "sourcesContent": ["/*\n * Copyright (c) Microsoft Corporation.\n * Licensed under the MIT License.\n *\n * Code generated by Microsoft (R) AutoRest Code Generator.\n * Changes may cause incorrect behavior and will be lost if the code is regenerated.\n */\n\nimport * as coreRestPipeline from \"@azure/core-rest-pipeline\";\nimport {\n  AppendBlobCreateOptionalParams,\n  AppendBlobCreateResponse,\n  AppendBlobAppendBlockOptionalParams,\n  AppendBlobAppendBlockResponse,\n  AppendBlobAppendBlockFromUrlOptionalParams,\n  AppendBlobAppendBlockFromUrlResponse,\n  AppendBlobSealOptionalParams,\n  AppendBlobSealResponse,\n} from \"../models\";\n\n/** Interface representing a AppendBlob. */\nexport interface AppendBlob {\n  /**\n   * The Create Append Blob operation creates a new append blob.\n   * @param contentLength The length of the request.\n   * @param options The options parameters.\n   */\n  create(\n    contentLength: number,\n    options?: AppendBlobCreateOptionalParams,\n  ): Promise<AppendBlobCreateResponse>;\n  /**\n   * The Append Block operation commits a new block of data to the end of an existing append blob. The\n   * Append Block operation is permitted only if the blob was created with x-ms-blob-type set to\n   * AppendBlob. Append Block is supported only on version 2015-02-21 version or later.\n   * @param contentLength The length of the request.\n   * @param body Initial data\n   * @param options The options parameters.\n   */\n  appendBlock(\n    contentLength: number,\n    body: coreRestPipeline.RequestBodyType,\n    options?: AppendBlobAppendBlockOptionalParams,\n  ): Promise<AppendBlobAppendBlockResponse>;\n  /**\n   * The Append Block operation commits a new block of data to the end of an existing append blob where\n   * the contents are read from a source url. The Append Block operation is permitted only if the blob\n   * was created with x-ms-blob-type set to AppendBlob. Append Block is supported only on version\n   * 2015-02-21 version or later.\n   * @param sourceUrl Specify a URL to the copy source.\n   * @param contentLength The length of the request.\n   * @param options The options parameters.\n   */\n  appendBlockFromUrl(\n    sourceUrl: string,\n    contentLength: number,\n    options?: AppendBlobAppendBlockFromUrlOptionalParams,\n  ): Promise<AppendBlobAppendBlockFromUrlResponse>;\n  /**\n   * The Seal operation seals the Append Blob to make it read-only. Seal is supported only on version\n   * 2019-12-12 version or later.\n   * @param options The options parameters.\n   */\n  seal(options?: AppendBlobSealOptionalParams): Promise<AppendBlobSealResponse>;\n}\n"]}
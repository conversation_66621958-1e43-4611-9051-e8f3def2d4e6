"use strict";
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.logs = exports.NoopLoggerProvider = exports.NOOP_LOGGER_PROVIDER = exports.NoopLogger = exports.NOOP_LOGGER = exports.SeverityNumber = void 0;
var LogRecord_1 = require("./types/LogRecord");
Object.defineProperty(exports, "SeverityNumber", { enumerable: true, get: function () { return LogRecord_1.SeverityNumber; } });
var NoopLogger_1 = require("./NoopLogger");
Object.defineProperty(exports, "NOOP_LOGGER", { enumerable: true, get: function () { return NoopLogger_1.NOOP_LOGGER; } });
Object.defineProperty(exports, "NoopLogger", { enumerable: true, get: function () { return NoopLogger_1.NoopLogger; } });
var NoopLoggerProvider_1 = require("./NoopLoggerProvider");
Object.defineProperty(exports, "NOOP_LOGGER_PROVIDER", { enumerable: true, get: function () { return NoopLoggerProvider_1.NOOP_LOGGER_PROVIDER; } });
Object.defineProperty(exports, "NoopLoggerProvider", { enumerable: true, get: function () { return NoopLoggerProvider_1.NoopLoggerProvider; } });
const logs_1 = require("./api/logs");
exports.logs = logs_1.LogsAPI.getInstance();
//# sourceMappingURL=index.js.map
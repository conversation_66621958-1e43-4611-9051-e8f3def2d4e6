/**
 * Pinecone Control Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import type { PodSpec } from './PodSpec';
import type { ServerlessSpec } from './ServerlessSpec';
/**
 * The spec object defines how the index should be deployed.
 *
 * For serverless indexes, you define only the [cloud and region](http://docs.pinecone.io/guides/indexes/understanding-indexes#cloud-regions) where the index should be hosted. For pod-based indexes, you define the [environment](http://docs.pinecone.io/guides/indexes/understanding-indexes#pod-environments) where the index should be hosted, the [pod type and size](http://docs.pinecone.io/guides/indexes/understanding-indexes#pod-types) to use, and other index characteristics.
 * @export
 * @interface IndexSpec
 */
export interface IndexSpec {
    /**
     *
     * @type {ServerlessSpec}
     * @memberof IndexSpec
     */
    serverless?: ServerlessSpec;
    /**
     *
     * @type {PodSpec}
     * @memberof IndexSpec
     */
    pod?: PodSpec;
}
/**
 * Check if a given object implements the IndexSpec interface.
 */
export declare function instanceOfIndexSpec(value: object): boolean;
export declare function IndexSpecFromJSON(json: any): IndexSpec;
export declare function IndexSpecFromJSONTyped(json: any, ignoreDiscriminator: boolean): IndexSpec;
export declare function IndexSpecToJSON(value?: IndexSpec | null): any;

/**
 * Pinecone Control Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import type { ErrorResponseError } from './ErrorResponseError';
/**
 * The response shape used for all error responses.
 * @export
 * @interface ErrorResponse
 */
export interface ErrorResponse {
    /**
     * The HTTP status code of the error.
     * @type {number}
     * @memberof ErrorResponse
     */
    status: number;
    /**
     *
     * @type {ErrorResponseError}
     * @memberof ErrorResponse
     */
    error: ErrorResponseError;
}
/**
 * Check if a given object implements the ErrorResponse interface.
 */
export declare function instanceOfErrorResponse(value: object): boolean;
export declare function ErrorResponseFromJSON(json: any): ErrorResponse;
export declare function ErrorResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): ErrorResponse;
export declare function ErrorResponseToJSON(value?: ErrorResponse | null): any;

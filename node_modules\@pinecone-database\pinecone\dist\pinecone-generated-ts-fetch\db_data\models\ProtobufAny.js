"use strict";
/* tslint:disable */
/* eslint-disable */
/**
 * Pinecone Data Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProtobufAnyToJSON = exports.ProtobufAnyFromJSONTyped = exports.ProtobufAnyFromJSON = exports.instanceOfProtobufAny = void 0;
var runtime_1 = require("../runtime");
/**
 * Check if a given object implements the ProtobufAny interface.
 */
function instanceOfProtobufAny(value) {
    var isInstance = true;
    return isInstance;
}
exports.instanceOfProtobufAny = instanceOfProtobufAny;
function ProtobufAnyFromJSON(json) {
    return ProtobufAnyFromJSONTyped(json, false);
}
exports.ProtobufAnyFromJSON = ProtobufAnyFromJSON;
function ProtobufAnyFromJSONTyped(json, ignoreDiscriminator) {
    if ((json === undefined) || (json === null)) {
        return json;
    }
    return {
        'typeUrl': !(0, runtime_1.exists)(json, 'typeUrl') ? undefined : json['typeUrl'],
        'value': !(0, runtime_1.exists)(json, 'value') ? undefined : json['value'],
    };
}
exports.ProtobufAnyFromJSONTyped = ProtobufAnyFromJSONTyped;
function ProtobufAnyToJSON(value) {
    if (value === undefined) {
        return undefined;
    }
    if (value === null) {
        return null;
    }
    return {
        'typeUrl': value.typeUrl,
        'value': value.value,
    };
}
exports.ProtobufAnyToJSON = ProtobufAnyToJSON;
//# sourceMappingURL=ProtobufAny.js.map
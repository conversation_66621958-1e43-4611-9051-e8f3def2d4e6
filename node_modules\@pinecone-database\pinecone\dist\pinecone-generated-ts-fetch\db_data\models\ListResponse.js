"use strict";
/* tslint:disable */
/* eslint-disable */
/**
 * Pinecone Data Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ListResponseToJSON = exports.ListResponseFromJSONTyped = exports.ListResponseFromJSON = exports.instanceOfListResponse = void 0;
var runtime_1 = require("../runtime");
var ListItem_1 = require("./ListItem");
var Pagination_1 = require("./Pagination");
var Usage_1 = require("./Usage");
/**
 * Check if a given object implements the ListResponse interface.
 */
function instanceOfListResponse(value) {
    var isInstance = true;
    return isInstance;
}
exports.instanceOfListResponse = instanceOfListResponse;
function ListResponseFromJSON(json) {
    return ListResponseFromJSONTyped(json, false);
}
exports.ListResponseFromJSON = ListResponseFromJSON;
function ListResponseFromJSONTyped(json, ignoreDiscriminator) {
    if ((json === undefined) || (json === null)) {
        return json;
    }
    return {
        'vectors': !(0, runtime_1.exists)(json, 'vectors') ? undefined : (json['vectors'].map(ListItem_1.ListItemFromJSON)),
        'pagination': !(0, runtime_1.exists)(json, 'pagination') ? undefined : (0, Pagination_1.PaginationFromJSON)(json['pagination']),
        'namespace': !(0, runtime_1.exists)(json, 'namespace') ? undefined : json['namespace'],
        'usage': !(0, runtime_1.exists)(json, 'usage') ? undefined : (0, Usage_1.UsageFromJSON)(json['usage']),
    };
}
exports.ListResponseFromJSONTyped = ListResponseFromJSONTyped;
function ListResponseToJSON(value) {
    if (value === undefined) {
        return undefined;
    }
    if (value === null) {
        return null;
    }
    return {
        'vectors': value.vectors === undefined ? undefined : (value.vectors.map(ListItem_1.ListItemToJSON)),
        'pagination': (0, Pagination_1.PaginationToJSON)(value.pagination),
        'namespace': value.namespace,
        'usage': (0, Usage_1.UsageToJSON)(value.usage),
    };
}
exports.ListResponseToJSON = ListResponseToJSON;
//# sourceMappingURL=ListResponse.js.map
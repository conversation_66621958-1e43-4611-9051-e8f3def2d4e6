import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { ListFlowExecutionEventsResponseFilterSensitiveLog, } from "../models/models_0";
import { de_ListFlowExecutionEventsCommand, se_ListFlowExecutionEventsCommand } from "../protocols/Aws_restJson1";
export { $Command };
export class ListFlowExecutionEventsCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("AmazonBedrockAgentRunTimeService", "ListFlowExecutionEvents", {})
    .n("BedrockAgentRuntimeClient", "ListFlowExecutionEventsCommand")
    .f(void 0, ListFlowExecutionEventsResponseFilterSensitiveLog)
    .ser(se_ListFlowExecutionEventsCommand)
    .de(de_ListFlowExecutionEventsCommand)
    .build() {
}

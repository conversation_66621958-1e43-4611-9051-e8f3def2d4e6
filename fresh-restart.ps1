# FRESH START - Clean n8n and create working workflow
Write-Host "🔄 FRESH START - Cleaning and creating working workflow..." -ForegroundColor Cyan

$apiKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI0NDgyZmY2Yi0xNzcwLTRlZGQtOTY2Yy1hNTVjZTNkZDZmZGQiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzQ4NTAyNTMyfQ.Si6LRA-uLi8HIdWZIX6tKNXzrSJDd6gBqiRHwtf8K8M"
$headers = @{
    "Content-Type" = "application/json"
    "X-N8N-API-KEY" = $apiKey
}

# Step 1: Restart n8n
Write-Host "1. Restarting n8n..." -ForegroundColor Yellow
try {
    docker restart n8n
    Start-Sleep 15
    Write-Host "✅ n8n restarted" -ForegroundColor Green
} catch {
    Write-Host "⚠️  Manual restart needed: docker restart n8n" -ForegroundColor Yellow
}

# Step 2: Clean up workflows
Write-Host "2. Cleaning up corrupted workflows..." -ForegroundColor Yellow
try {
    $workflows = Invoke-RestMethod -Uri "http://localhost:2410/api/v1/workflows" -Headers @{"X-N8N-API-KEY" = $apiKey}
    
    foreach ($workflow in $workflows.data) {
        if (-not $workflow.active) {
            try {
                Invoke-RestMethod -Uri "http://localhost:2410/api/v1/workflows/$($workflow.id)" -Method DELETE -Headers @{"X-N8N-API-KEY" = $apiKey}
                Write-Host "   Deleted: $($workflow.name)" -ForegroundColor Gray
            } catch {
                Write-Host "   Could not delete: $($workflow.name)" -ForegroundColor Yellow
            }
        }
    }
    Write-Host "✅ Cleanup completed" -ForegroundColor Green
} catch {
    Write-Host "⚠️  Cleanup failed, continuing..." -ForegroundColor Yellow
}

# Step 3: Create ultra-simple working workflow
Write-Host "3. Creating fresh working workflow..." -ForegroundColor Yellow

$freshWorkflow = @{
    name = "Fresh Working Bot"
    nodes = @(
        @{
            parameters = @{
                httpMethod = "POST"
                path = "fresh-bot"
            }
            id = "webhook"
            name = "Webhook"
            type = "n8n-nodes-base.webhook"
            typeVersion = 2
            position = @(300, 300)
        },
        @{
            parameters = @{
                respondWith = "json"
                responseBody = '{"success": true, "message": "Fresh bot working!", "input": "{{ $json.body.message }}"}'
            }
            id = "respond"
            name = "Respond to Webhook"
            type = "n8n-nodes-base.respondToWebhook"
            typeVersion = 1
            position = @(500, 300)
        }
    )
    connections = @{
        "Webhook" = @{
            main = @(@(@{node = "Respond to Webhook"; type = "main"; index = 0}))
        }
    }
} | ConvertTo-Json -Depth 10

try {
    $result = Invoke-RestMethod -Uri "http://localhost:2410/api/v1/workflows" -Method POST -Headers $headers -Body $freshWorkflow
    Write-Host "✅ Fresh workflow created! ID: $($result.id)" -ForegroundColor Green
    
    # Try to activate
    Write-Host "4. Activating fresh workflow..." -ForegroundColor Yellow
    Invoke-RestMethod -Uri "http://localhost:2410/api/v1/workflows/$($result.id)/activate" -Method POST -Headers @{"X-N8N-API-KEY" = $apiKey}
    Write-Host "✅ Activated successfully!" -ForegroundColor Green
    
    # Test it
    Write-Host "5. Testing fresh workflow..." -ForegroundColor Yellow
    Start-Sleep 3
    
    $testHeaders = @{"Content-Type" = "application/json"}
    $testBody = @{message = "Hello fresh bot!"} | ConvertTo-Json
    
    $response = Invoke-RestMethod -Uri "http://localhost:2410/webhook/fresh-bot" -Method POST -Headers $testHeaders -Body $testBody
    
    Write-Host ""
    Write-Host "🎉 SUCCESS! Fresh bot is working!" -ForegroundColor Green
    Write-Host "Response: $($response.message)" -ForegroundColor Cyan
    Write-Host "Webhook: http://localhost:2410/webhook/fresh-bot" -ForegroundColor White
    
} catch {
    Write-Host "❌ Fresh workflow failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "💡 MANUAL SOLUTION:" -ForegroundColor Yellow
    Write-Host "1. Go to http://localhost:2410" -ForegroundColor White
    Write-Host "2. Delete ALL existing workflows" -ForegroundColor White
    Write-Host "3. Create new workflow manually with just Webhook + Respond nodes" -ForegroundColor White
    Write-Host "4. Use simple JSON response: {`"message`": `"working`"}" -ForegroundColor White
}

/**
 * Pinecone Data Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
/**
 * The request for the `delete` operation.
 * @export
 * @interface DeleteRequest
 */
export interface DeleteRequest {
    /**
     * Vectors to delete.
     * @type {Array<string>}
     * @memberof DeleteRequest
     */
    ids?: Array<string>;
    /**
     * This indicates that all vectors in the index namespace should be deleted.
     * @type {boolean}
     * @memberof DeleteRequest
     */
    deleteAll?: boolean;
    /**
     * The namespace to delete vectors from, if applicable.
     * @type {string}
     * @memberof DeleteRequest
     */
    namespace?: string;
    /**
     * If specified, the metadata filter here will be used to select the vectors to delete. This is mutually exclusive with specifying ids to delete in the ids param or using delete_all=True. See [Filter with metadata](https://docs.pinecone.io/guides/data/filter-with-metadata).
     * Serverless indexes do not support delete by metadata. Instead, you can use the `list` operation to fetch the vector IDs based on their common ID prefix and then delete the records by ID.
     * @type {object}
     * @memberof DeleteRequest
     */
    filter?: object;
}
/**
 * Check if a given object implements the DeleteRequest interface.
 */
export declare function instanceOfDeleteRequest(value: object): boolean;
export declare function DeleteRequestFromJSON(json: any): DeleteRequest;
export declare function DeleteRequestFromJSONTyped(json: any, ignoreDiscriminator: boolean): DeleteRequest;
export declare function DeleteRequestToJSON(value?: DeleteRequest | null): any;

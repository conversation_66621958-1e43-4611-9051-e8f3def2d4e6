{"version": 3, "file": "CacheManager.d.ts", "sourceRoot": "", "sources": ["../../../src/cache/CacheManager.ts"], "names": [], "mappings": "AAKA,OAAO,EACH,aAAa,EACb,gBAAgB,EAChB,mBAAmB,EACnB,iBAAiB,EACjB,gBAAgB,EAChB,SAAS,EAEZ,MAAM,uBAAuB,CAAC;AAC/B,OAAO,EAAE,WAAW,EAAE,MAAM,2BAA2B,CAAC;AAYxD,OAAO,EAAE,aAAa,EAAE,MAAM,6BAA6B,CAAC;AAC5D,OAAO,EAAE,iBAAiB,EAAE,MAAM,iCAAiC,CAAC;AACpE,OAAO,EAAE,aAAa,EAAE,MAAM,6BAA6B,CAAC;AAC5D,OAAO,EAAE,kBAAkB,EAAE,MAAM,kCAAkC,CAAC;AACtE,OAAO,EAAE,aAAa,EAAE,MAAM,8BAA8B,CAAC;AAK7D,OAAO,EACH,WAAW,EAId,MAAM,2BAA2B,CAAC;AACnC,OAAO,EAAE,iBAAiB,EAAE,MAAM,iCAAiC,CAAC;AACpE,OAAO,EAAE,qBAAqB,EAAE,MAAM,qCAAqC,CAAC;AAC5E,OAAO,EAAE,gBAAgB,EAAE,MAAM,gCAAgC,CAAC;AAElE,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAC/C,OAAO,EAAE,uBAAuB,EAAE,MAAM,uCAAuC,CAAC;AAChF,OAAO,EAAE,eAAe,EAAE,MAAM,+BAA+B,CAAC;AAChE,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAE7C,OAAO,EAAE,YAAY,EAAE,MAAM,4BAA4B,CAAC;AAE1D,OAAO,EAAE,sBAAsB,EAAE,MAAM,kCAAkC,CAAC;AAE1E,OAAO,EAAE,kBAAkB,EAAE,MAAM,gDAAgD,CAAC;AAGpF;;;GAGG;AACH,8BAAsB,YAAa,YAAW,aAAa;IACvD,SAAS,CAAC,QAAQ,EAAE,MAAM,CAAC;IAC3B,SAAS,CAAC,UAAU,EAAE,OAAO,CAAC;IAE9B,OAAO,CAAC,YAAY,CAAS;IAC7B,OAAO,CAAC,sBAAsB,CAAC,CAAyB;gBAGpD,QAAQ,EAAE,MAAM,EAChB,UAAU,EAAE,OAAO,EACnB,MAAM,EAAE,MAAM,EACd,sBAAsB,CAAC,EAAE,sBAAsB;IAQnD;;;OAGG;IACH,QAAQ,CAAC,UAAU,CACf,UAAU,EAAE,MAAM,EAClB,MAAM,CAAC,EAAE,MAAM,GAChB,aAAa,GAAG,IAAI;IAEvB;;OAEG;IACH,QAAQ,CAAC,sBAAsB,CAAC,UAAU,EAAE,MAAM,GAAG,aAAa,GAAG,IAAI;IAEzE;;;OAGG;IACH,QAAQ,CAAC,UAAU,CAAC,OAAO,EAAE,aAAa,GAAG,IAAI;IAEjD;;OAEG;IACH,QAAQ,CAAC,qBAAqB,CAAC,UAAU,EAAE,MAAM,GAAG,IAAI;IAExD;;;OAGG;IACH,QAAQ,CAAC,oBAAoB,CAAC,UAAU,EAAE,MAAM,GAAG,aAAa,GAAG,IAAI;IAEvE;;;OAGG;IACH,QAAQ,CAAC,oBAAoB,CAAC,OAAO,EAAE,aAAa,GAAG,IAAI;IAE3D;;;OAGG;IACH,QAAQ,CAAC,wBAAwB,CAC7B,cAAc,EAAE,MAAM,GACvB,iBAAiB,GAAG,IAAI;IAE3B;;;OAGG;IACH,QAAQ,CAAC,wBAAwB,CAAC,WAAW,EAAE,iBAAiB,GAAG,IAAI;IAEvE;;;OAGG;IACH,QAAQ,CAAC,yBAAyB,CAC9B,eAAe,EAAE,MAAM,GACxB,kBAAkB,GAAG,IAAI;IAE5B;;;OAGG;IACH,QAAQ,CAAC,yBAAyB,CAAC,YAAY,EAAE,kBAAkB,GAAG,IAAI;IAE1E;;;OAGG;IACH,QAAQ,CAAC,cAAc,CAAC,cAAc,EAAE,MAAM,GAAG,iBAAiB,GAAG,IAAI;IAEzE;;;OAGG;IACH,QAAQ,CAAC,cAAc,CAAC,WAAW,EAAE,iBAAiB,GAAG,IAAI;IAE7D;;;OAGG;IACH,QAAQ,CAAC,kBAAkB,CACvB,kBAAkB,EAAE,MAAM,GAC3B,qBAAqB,GAAG,IAAI;IAE/B;;;;OAIG;IACH,QAAQ,CAAC,kBAAkB,CACvB,kBAAkB,EAAE,MAAM,EAC1B,eAAe,EAAE,qBAAqB,GACvC,IAAI;IAEP;;;OAGG;IACH,QAAQ,CAAC,oBAAoB,CAAC,GAAG,EAAE,MAAM,GAAG,uBAAuB,GAAG,IAAI;IAE1E;;OAEG;IACH,QAAQ,CAAC,wBAAwB,IAAI,KAAK,CAAC,MAAM,CAAC;IAElD;;;;OAIG;IACH,QAAQ,CAAC,oBAAoB,CACzB,GAAG,EAAE,MAAM,EACX,KAAK,EAAE,uBAAuB,GAC/B,IAAI;IAEP;;;OAGG;IACH,QAAQ,CAAC,kBAAkB,CACvB,kBAAkB,EAAE,MAAM,GAC3B,gBAAgB,GAAG,IAAI;IAE1B;;;;OAIG;IACH,QAAQ,CAAC,kBAAkB,CACvB,kBAAkB,EAAE,MAAM,EAC1B,eAAe,EAAE,gBAAgB,GAClC,IAAI;IAEP;;;OAGG;IACH,QAAQ,CAAC,UAAU,CAAC,GAAG,EAAE,MAAM,GAAG,IAAI;IAEtC;;OAEG;IACH,QAAQ,CAAC,OAAO,IAAI,MAAM,EAAE;IAE5B;;OAEG;IACH,QAAQ,CAAC,cAAc,IAAI,MAAM,EAAE;IAEnC;;OAEG;IACH,QAAQ,CAAC,YAAY,IAAI,SAAS;IAElC;;OAEG;IACH,QAAQ,CAAC,wBAAwB,CAC7B,eAAe,EAAE,MAAM,EACvB,UAAU,EAAE,mBAAmB,GAChC,MAAM;IAET;;;;OAIG;IACH,cAAc,CAAC,aAAa,CAAC,EAAE,aAAa,GAAG,WAAW,EAAE;IAO5D;;OAEG;IACH,wBAAwB,CAAC,aAAa,EAAE,aAAa,GAAG,WAAW,GAAG,IAAI;IAgB1E;;;;OAIG;IACH,kBAAkB,CAAC,aAAa,EAAE,aAAa,GAAG,WAAW,GAAG,IAAI;IASpE;;;;;;OAMG;IACH,OAAO,CAAC,mBAAmB;IAa3B,OAAO,CAAC,8BAA8B;IAsDtC,OAAO,CAAC,kCAAkC;IAwC1C,OAAO,CAAC,0BAA0B;IA+BlC,OAAO,CAAC,qCAAqC;IAsD7C;;;;;OAKG;IACG,eAAe,CACjB,WAAW,EAAE,WAAW,EACxB,YAAY,CAAC,EAAE,YAAY,EAC3B,aAAa,CAAC,EAAE,MAAM,GACvB,OAAO,CAAC,IAAI,CAAC;IAkEhB;;;OAGG;YACW,eAAe;IAwC7B;;;;OAIG;IACH,qBAAqB,CAAC,aAAa,EAAE,aAAa,GAAG,aAAa,EAAE;IA2FpE;;;;;;OAMG;IACH,YAAY,CACR,GAAG,EAAE,MAAM,EACX,aAAa,CAAC,EAAE,MAAM,EACtB,QAAQ,CAAC,EAAE,MAAM,GAClB,OAAO;IAsBV;;;OAGG;IACH,eAAe,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO;IA2CrC;;;;;OAKG;IACH,uBAAuB,CACnB,MAAM,EAAE,mBAAmB,EAC3B,MAAM,EAAE,gBAAgB,GACzB,OAAO;IAoFV;;;OAGG;IACH,wBAAwB,CAAC,MAAM,EAAE,iBAAiB,GAAG,gBAAgB;IAqCrE;;;OAGG;IACH,2BAA2B,CAAC,IAAI,EAAE,MAAM,GAAG,uBAAuB,GAAG,IAAI;IA8BzE;;OAEG;IACG,iBAAiB,IAAI,OAAO,CAAC,IAAI,CAAC;IAWxC;;;OAGG;IACG,aAAa,CAAC,UAAU,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAStD;;;OAGG;IACG,oBAAoB,CAAC,OAAO,EAAE,aAAa,GAAG,OAAO,CAAC,IAAI,CAAC;IA0BjE;;;;;;;OAOG;IACH,SAAS,CAAC,2BAA2B,CACjC,UAAU,EAAE,MAAM,EAClB,aAAa,EAAE,aAAa,GAAG,IAAI,EACnC,MAAM,CAAC,EAAE,MAAM,GAChB,aAAa,GAAG,IAAI;IAsEvB;;;OAGG;IACG,iBAAiB,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IA+BnD;;OAEG;IACH,iBAAiB,IAAI,OAAO;IAW5B;;;OAGG;IACH,oBAAoB,CAAC,OAAO,EAAE,WAAW,GAAG,aAAa,GAAG,IAAI;IAMhE;;;;;;;OAOG;IACH,UAAU,CACN,OAAO,EAAE,WAAW,EACpB,SAAS,CAAC,EAAE,SAAS,EACrB,WAAW,CAAC,EAAE,MAAM,EACpB,iBAAiB,CAAC,EAAE,kBAAkB,EACtC,aAAa,CAAC,EAAE,MAAM,GACvB,aAAa,GAAG,IAAI;IAqEvB;;;;OAIG;IACH,mBAAmB,CACf,MAAM,EAAE,gBAAgB,EACxB,SAAS,CAAC,EAAE,SAAS,GACtB,GAAG,CAAC,MAAM,EAAE,aAAa,CAAC;IA0B7B;;;;;OAKG;IACH,uBAAuB,CACnB,QAAQ,EAAE,MAAM,EAChB,MAAM,EAAE,gBAAgB,GACzB,OAAO;IAmBV;;;OAGG;IACH,aAAa,CAAC,GAAG,EAAE,MAAM,GAAG,IAAI;IAIhC;;;OAGG;IACH,kBAAkB,CAAC,GAAG,EAAE,MAAM,GAAG,IAAI;IAIrC;;;;;;;OAOG;IACH,cAAc,CACV,OAAO,EAAE,WAAW,EACpB,OAAO,EAAE,eAAe,EACxB,SAAS,CAAC,EAAE,SAAS,EACrB,WAAW,CAAC,EAAE,MAAM,EACpB,iBAAiB,CAAC,EAAE,kBAAkB,EACtC,aAAa,CAAC,EAAE,MAAM,GACvB,iBAAiB,GAAG,IAAI;IA8E3B;;;;;;OAMG;IACH,2BAA2B,CACvB,QAAQ,EAAE,MAAM,EAChB,MAAM,EAAE,gBAAgB,EACxB,uBAAuB,EAAE,OAAO,GACjC,OAAO;IAiDV;;;;OAIG;IACH,uBAAuB,CAAC,MAAM,EAAE,gBAAgB,GAAG,iBAAiB,EAAE;IAqBtE;;;;;;;OAOG;IACH,eAAe,CACX,OAAO,EAAE,WAAW,EACpB,QAAQ,EAAE,OAAO,EACjB,SAAS,CAAC,EAAE,SAAS,EACrB,iBAAiB,CAAC,EAAE,kBAAkB,EACtC,aAAa,CAAC,EAAE,MAAM,GACvB,kBAAkB,GAAG,IAAI;IAuD5B;;;;OAIG;IACH,4BAA4B,CACxB,QAAQ,EAAE,MAAM,EAChB,MAAM,EAAE,gBAAgB,GACzB,OAAO;IA4BV;;OAEG;IACH,wBAAwB,CAAC,WAAW,EAAE,MAAM,GAAG,iBAAiB,GAAG,IAAI;IAwBvE;;;;OAIG;IACH,iBAAiB,CAAC,WAAW,EAAE,MAAM,GAAG,OAAO;IAK/C;;;;OAIG;IACH,OAAO,CAAC,kBAAkB;IAU1B;;;;;OAKG;IACH,OAAO,CAAC,kCAAkC;IAQ1C,OAAO,CAAC,oCAAoC;IAO5C;;;;;OAKG;IACH,OAAO,CAAC,SAAS;IAIjB;;;;;OAKG;IACH,OAAO,CAAC,aAAa;IAWrB;;;;OAIG;IACH,OAAO,CAAC,sBAAsB;IAU9B;;;;OAIG;IACH,OAAO,CAAC,gBAAgB;IA6BxB;;;;OAIG;IACH,OAAO,CAAC,mBAAmB;IAU3B;;;;OAIG;IACH,OAAO,CAAC,aAAa;IAOrB;;;;OAIG;IACH,OAAO,CAAC,aAAa;IAOrB;;;;OAIG;IACH,OAAO,CAAC,UAAU;IAOlB;;;;;OAKG;IACH,OAAO,CAAC,oBAAoB;IAS5B;;;;;;;;OAQG;IACH,OAAO,CAAC,6BAA6B;IAmBrC;;;;;OAKG;IACH,OAAO,CAAC,QAAQ;IAIhB,OAAO,CAAC,kBAAkB;IAU1B;;;;OAIG;IACH,OAAO,CAAC,WAAW;IAenB;;;;OAIG;IACH,OAAO,CAAC,cAAc;IAOtB;;;;OAIG;IACH,OAAO,CAAC,UAAU;IAIlB;;;OAGG;IACH,OAAO,CAAC,aAAa;IAIrB;;;OAGG;IACH,SAAS,CAAC,mBAAmB,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO;IAInD;;OAEG;IACH,iCAAiC,CAAC,SAAS,EAAE,MAAM,GAAG,MAAM;IAI5D;;;;OAIG;IACH,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,GAAG,CAAC;CAM9C;AAED,gBAAgB;AAChB,qBAAa,mBAAoB,SAAQ,YAAY;IACjD,UAAU,IAAI,IAAI;IAGlB,UAAU,IAAI,aAAa;IAG3B,sBAAsB,IAAI,aAAa,GAAG,IAAI;IAG9C,oBAAoB,IAAI,IAAI;IAG5B,oBAAoB,IAAI,aAAa;IAGrC,wBAAwB,IAAI,IAAI;IAGhC,wBAAwB,IAAI,iBAAiB;IAG7C,yBAAyB,IAAI,IAAI;IAGjC,yBAAyB,IAAI,kBAAkB;IAG/C,cAAc,IAAI,IAAI;IAGtB,cAAc,IAAI,iBAAiB;IAGnC,kBAAkB,IAAI,IAAI;IAG1B,kBAAkB,IAAI,qBAAqB;IAG3C,oBAAoB,IAAI,IAAI;IAG5B,oBAAoB,IAAI,uBAAuB,GAAG,IAAI;IAGtD,wBAAwB,IAAI,KAAK,CAAC,MAAM,CAAC;IAGzC,kBAAkB,IAAI,IAAI;IAG1B,kBAAkB,IAAI,gBAAgB;IAGtC,UAAU,IAAI,OAAO;IAGrB,OAAO,IAAI,MAAM,EAAE;IAGnB,cAAc,IAAI,MAAM,EAAE;IAG1B,YAAY,IAAI,SAAS;IAGzB,wBAAwB,IAAI,MAAM;IAGlC,qBAAqB,IAAI,IAAI;CAGhC"}
{"name": "Simple AI Agent Creator", "nodes": [{"parameters": {"httpMethod": "POST", "path": "build-ai-agent", "options": {}}, "id": "webhook-trigger", "name": "Webhook Trigger", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [300, 400]}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"success\": true,\n  \"message\": \"🎉 AI Agent Builder is working!\",\n  \"instruction\": {{ JSON.stringify($json.body.instruction || $json.instruction || \"No instruction provided\") }},\n  \"timestamp\": \"{{ new Date().toISOString() }}\",\n  \"nextSteps\": [\n    \"Your instruction has been received\",\n    \"AI agent creation process started\",\n    \"Check back soon for your custom workflow\"\n  ],\n  \"webhook\": \"http://localhost:2410/webhook/build-ai-agent\",\n  \"status\": \"Processing your request...\"\n}"}, "id": "response-node", "name": "Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [520, 400]}], "connections": {"Webhook Trigger": {"main": [[{"node": "Response", "type": "main", "index": 0}]]}}, "active": false, "settings": {"timezone": "UTC"}, "tags": ["ai-agent", "simple"]}
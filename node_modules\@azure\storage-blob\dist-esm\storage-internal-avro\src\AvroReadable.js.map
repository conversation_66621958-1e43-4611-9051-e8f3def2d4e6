{"version": 3, "file": "AvroReadable.js", "sourceRoot": "", "sources": ["../../../../storage-internal-avro/src/AvroReadable.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAelC,MAAM,OAAgB,YAAY;CAGjC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { AbortSignalLike } from \"@azure/abort-controller\";\n\n/**\n * Options to configure the {@link AvroReadable.read} operation.\n */\nexport interface AvroReadableReadOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n}\n\nexport abstract class AvroReadable {\n  public abstract get position(): number;\n  public abstract read(size: number, options?: AvroReadableReadOptions): Promise<Uint8Array>;\n}\n"]}
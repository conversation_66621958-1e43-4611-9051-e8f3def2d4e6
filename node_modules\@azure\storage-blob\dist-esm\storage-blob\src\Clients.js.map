{"version": 3, "file": "Clients.js", "sourceRoot": "", "sources": ["../../../src/Clients.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;;AAOlC,OAAO,EAAE,uBAAuB,EAAE,MAAM,2BAA2B,CAAC;AAEpE,OAAO,EAAE,iBAAiB,EAAE,MAAM,kBAAkB,CAAC;AACrD,OAAO,EAAE,MAAM,EAAE,MAAM,kBAAkB,CAAC;AAE1C,OAAO,EAAE,UAAU,EAAE,MAAM,kBAAkB,CAAC;AAG9C,OAAO,EAAE,oBAAoB,EAAE,MAAM,wBAAwB,CAAC;AAC9D,OAAO,EAAE,iBAAiB,EAAE,MAAM,qBAAqB,CAAC;AACxD,OAAO,EAAE,mBAAmB,EAAE,MAAM,mCAAmC,CAAC;AACxE,OAAO,EAAE,0BAA0B,EAAE,MAAM,0CAA0C,CAAC;AAwHtF,OAAO,EAAE,oBAAoB,EAAE,YAAY,EAAE,MAAM,UAAU,CAAC;AAK9D,OAAO,EAAE,sBAAsB,EAAE,MAAM,yBAAyB,CAAC;AAEjE,OAAO,EAAE,WAAW,EAAE,cAAc,EAAE,MAAM,YAAY,CAAC;AAKzD,OAAO,EAAE,0BAA0B,EAAE,MAAM,sCAAsC,CAAC;AAElF,OAAO,EAAE,aAAa,EAAE,MAAM,SAAS,CAAC;AAExC,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAChD,OAAO,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AACtC,OAAO,EAAE,eAAe,EAAE,MAAM,0BAA0B,CAAC;AAC3D,OAAO,EACL,yCAAyC,EACzC,sCAAsC,EACtC,qBAAqB,EACrB,gCAAgC,EAChC,gCAAgC,EAChC,iCAAiC,EACjC,+BAA+B,EAC/B,mCAAmC,EACnC,OAAO,EACP,YAAY,GACb,MAAM,mBAAmB,CAAC;AAC3B,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAEhD,OAAO,EACL,eAAe,EACf,gBAAgB,EAChB,cAAc,EACd,4BAA4B,EAC5B,yBAAyB,EACzB,eAAe,EACf,eAAe,EACf,yBAAyB,EACzB,iBAAiB,EACjB,4BAA4B,EAC5B,eAAe,EACf,UAAU,EACV,gBAAgB,EAChB,oBAAoB,EACpB,MAAM,GACP,MAAM,sBAAsB,CAAC;AAC9B,OAAO,EACL,kBAAkB,EAClB,MAAM,EACN,qBAAqB,EACrB,cAAc,GACf,MAAM,oBAAoB,CAAC;AAG5B,OAAO,EACL,8BAA8B,EAC9B,sCAAsC,GACvC,MAAM,8BAA8B,CAAC;AAEtC,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;AAksBpD;;;GAGG;AACH,MAAM,OAAO,UAAW,SAAQ,aAAa;IAY3C;;OAEG;IACH,IAAW,IAAI;QACb,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;IAED;;OAEG;IACH,IAAW,aAAa;QACtB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B,CAAC;IA6DD,YACE,qBAA6B,EAC7B,mCAKgB,EAChB,iBAAmD;IACnD,mFAAmF;IACnF,gEAAgE;IAChE,OAAgC;QAEhC,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;QACxB,IAAI,QAAsB,CAAC;QAC3B,IAAI,GAAW,CAAC;QAChB,IAAI,cAAc,CAAC,mCAAmC,CAAC,EAAE,CAAC;YACxD,oCAAoC;YACpC,GAAG,GAAG,qBAAqB,CAAC;YAC5B,QAAQ,GAAG,mCAAmC,CAAC;QACjD,CAAC;aAAM,IACL,CAAC,MAAM,IAAI,mCAAmC,YAAY,0BAA0B,CAAC;YACrF,mCAAmC,YAAY,mBAAmB;YAClE,iBAAiB,CAAC,mCAAmC,CAAC,EACtD,CAAC;YACD,mIAAmI;YACnI,GAAG,GAAG,qBAAqB,CAAC;YAC5B,OAAO,GAAG,iBAA2C,CAAC;YACtD,QAAQ,GAAG,WAAW,CAAC,mCAAmC,EAAE,OAAO,CAAC,CAAC;QACvE,CAAC;aAAM,IACL,CAAC,mCAAmC;YACpC,OAAO,mCAAmC,KAAK,QAAQ,EACvD,CAAC;YACD,mIAAmI;YACnI,+DAA+D;YAC/D,GAAG,GAAG,qBAAqB,CAAC;YAC5B,IAAI,iBAAiB,IAAI,OAAO,iBAAiB,KAAK,QAAQ,EAAE,CAAC;gBAC/D,OAAO,GAAG,iBAA2C,CAAC;YACxD,CAAC;YACD,QAAQ,GAAG,WAAW,CAAC,IAAI,mBAAmB,EAAE,EAAE,OAAO,CAAC,CAAC;QAC7D,CAAC;aAAM,IACL,mCAAmC;YACnC,OAAO,mCAAmC,KAAK,QAAQ;YACvD,iBAAiB;YACjB,OAAO,iBAAiB,KAAK,QAAQ,EACrC,CAAC;YACD,wGAAwG;YACxG,MAAM,aAAa,GAAG,mCAAmC,CAAC;YAC1D,MAAM,QAAQ,GAAG,iBAAiB,CAAC;YAEnC,MAAM,cAAc,GAAG,4BAA4B,CAAC,qBAAqB,CAAC,CAAC;YAC3E,IAAI,cAAc,CAAC,IAAI,KAAK,mBAAmB,EAAE,CAAC;gBAChD,IAAI,MAAM,EAAE,CAAC;oBACX,MAAM,mBAAmB,GAAG,IAAI,0BAA0B,CACxD,cAAc,CAAC,WAAY,EAC3B,cAAc,CAAC,UAAU,CAC1B,CAAC;oBACF,GAAG,GAAG,eAAe,CACnB,eAAe,CAAC,cAAc,CAAC,GAAG,EAAE,kBAAkB,CAAC,aAAa,CAAC,CAAC,EACtE,kBAAkB,CAAC,QAAQ,CAAC,CAC7B,CAAC;oBAEF,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC;wBAC1B,OAAO,CAAC,YAAY,GAAG,uBAAuB,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;oBAC1E,CAAC;oBAED,QAAQ,GAAG,WAAW,CAAC,mBAAmB,EAAE,OAAO,CAAC,CAAC;gBACvD,CAAC;qBAAM,CAAC;oBACN,MAAM,IAAI,KAAK,CAAC,oEAAoE,CAAC,CAAC;gBACxF,CAAC;YACH,CAAC;iBAAM,IAAI,cAAc,CAAC,IAAI,KAAK,eAAe,EAAE,CAAC;gBACnD,GAAG;oBACD,eAAe,CACb,eAAe,CAAC,cAAc,CAAC,GAAG,EAAE,kBAAkB,CAAC,aAAa,CAAC,CAAC,EACtE,kBAAkB,CAAC,QAAQ,CAAC,CAC7B;wBACD,GAAG;wBACH,cAAc,CAAC,UAAU,CAAC;gBAC5B,QAAQ,GAAG,WAAW,CAAC,IAAI,mBAAmB,EAAE,EAAE,OAAO,CAAC,CAAC;YAC7D,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,KAAK,CACb,0FAA0F,CAC3F,CAAC;YACJ,CAAC;QACH,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,KAAK,CAAC,uEAAuE,CAAC,CAAC;QAC3F,CAAC;QAED,KAAK,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;QACrB,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,KAAK,EAAE,aAAa,EAAE,IAAI,CAAC,cAAc,EAAE;YAC3D,IAAI,CAAC,+BAA+B,EAAE,CAAC,CAAC;QAC1C,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC;QAElD,IAAI,CAAC,SAAS,GAAG,eAAe,CAAC,IAAI,CAAC,GAAG,EAAE,YAAY,CAAC,UAAU,CAAC,QAAQ,CAAW,CAAC;QACvF,IAAI,CAAC,UAAU,GAAG,eAAe,CAAC,IAAI,CAAC,GAAG,EAAE,YAAY,CAAC,UAAU,CAAC,SAAS,CAAW,CAAC;IAC3F,CAAC;IAED;;;;;;OAMG;IACI,YAAY,CAAC,QAAgB;QAClC,OAAO,IAAI,UAAU,CACnB,eAAe,CACb,IAAI,CAAC,GAAG,EACR,YAAY,CAAC,UAAU,CAAC,QAAQ,EAChC,QAAQ,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,CAC7C,EACD,IAAI,CAAC,QAAQ,CACd,CAAC;IACJ,CAAC;IAED;;;;;;OAMG;IACI,WAAW,CAAC,SAAiB;QAClC,OAAO,IAAI,UAAU,CACnB,eAAe,CACb,IAAI,CAAC,GAAG,EACR,YAAY,CAAC,UAAU,CAAC,SAAS,EACjC,SAAS,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAC/C,EACD,IAAI,CAAC,QAAQ,CACd,CAAC;IACJ,CAAC;IAED;;;OAGG;IACI,mBAAmB;QACxB,OAAO,IAAI,gBAAgB,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;IACvD,CAAC;IAED;;;OAGG;IACI,kBAAkB;QACvB,OAAO,IAAI,eAAe,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;IACtD,CAAC;IAED;;;OAGG;IACI,iBAAiB;QACtB,OAAO,IAAI,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;IACrD,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA0DG;IACI,KAAK,CAAC,QAAQ,CACnB,SAAiB,CAAC,EAClB,KAAc,EACd,UAA+B,EAAE;QAEjC,OAAO,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,EAAE,CAAC;QAC9C,OAAO,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,EAAE,CAAC;QAC9C,oBAAoB,CAAC,OAAO,CAAC,mBAAmB,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAEhE,OAAO,aAAa,CAAC,QAAQ,CAAC,qBAAqB,EAAE,OAAO,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE;;YACrF,MAAM,GAAG,GAAG,cAAc,CACxB,MAAM,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC;gBAC9B,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,qBAAqB,EAAE,OAAO,CAAC,UAAU;gBACzC,wBAAwB,kCACnB,OAAO,CAAC,UAAU,KACrB,MAAM,EAAE,MAAA,OAAO,CAAC,UAAU,0CAAE,aAAa,GAC1C;gBACD,cAAc,EAAE;oBACd,kBAAkB,EAAE,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,UAAU,EAAE,+DAA+D;iBAC7H;gBACD,KAAK,EAAE,MAAM,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,aAAa,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC;gBAC5E,kBAAkB,EAAE,OAAO,CAAC,kBAAkB;gBAC9C,oBAAoB,EAAE,OAAO,CAAC,oBAAoB;gBAClD,QAAQ,EAAE,OAAO,CAAC,QAAQ;gBAC1B,OAAO,EAAE,OAAO,CAAC,mBAAmB;gBACpC,cAAc,EAAE,cAAc,CAAC,cAAc;aAC9C,CAAC,CACH,CAAC;YAEF,MAAM,UAAU,mCACX,GAAG,KACN,SAAS,EAAE,GAAG,CAAC,SAAS,EACxB,oCAAoC,EAAE,GAAG,CAAC,yBAAyB,EACnE,iCAAiC,EAAE,4BAA4B,CAAC,GAAG,CAAC,sBAAsB,CAAC,GAC5F,CAAC;YACF,sCAAsC;YACtC,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,OAAO,UAAU,CAAC;YACpB,CAAC;YAED,8EAA8E;YAC9E,uEAAuE;YACvE,uEAAuE;YACvE,sGAAsG;YACtG,gDAAgD;YAChD,IAAI,OAAO,CAAC,gBAAgB,KAAK,SAAS,IAAI,OAAO,CAAC,gBAAgB,GAAG,CAAC,EAAE,CAAC;gBAC3E,uDAAuD;gBACvD,OAAO,CAAC,gBAAgB,GAAG,mCAAmC,CAAC;YACjE,CAAC;YAED,IAAI,GAAG,CAAC,aAAa,KAAK,SAAS,EAAE,CAAC;gBACpC,MAAM,IAAI,UAAU,CAAC,oEAAoE,CAAC,CAAC;YAC7F,CAAC;YAED,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;gBACd,MAAM,IAAI,UAAU,CAAC,0DAA0D,CAAC,CAAC;YACnF,CAAC;YAED,OAAO,IAAI,oBAAoB,CAC7B,UAAU,EACV,KAAK,EAAE,KAAa,EAAkC,EAAE;;gBACtD,MAAM,sBAAsB,GAA+B;oBACzD,qBAAqB,EAAE,OAAO,CAAC,UAAU;oBACzC,wBAAwB,EAAE;wBACxB,OAAO,EAAE,OAAO,CAAC,UAAW,CAAC,OAAO,IAAI,GAAG,CAAC,IAAI;wBAChD,eAAe,EAAE,OAAO,CAAC,UAAW,CAAC,eAAe;wBACpD,WAAW,EAAE,OAAO,CAAC,UAAW,CAAC,WAAW;wBAC5C,iBAAiB,EAAE,OAAO,CAAC,UAAW,CAAC,iBAAiB;wBACxD,MAAM,EAAE,MAAA,OAAO,CAAC,UAAU,0CAAE,aAAa;qBAC1C;oBACD,KAAK,EAAE,aAAa,CAAC;wBACnB,KAAK,EAAE,MAAM,GAAG,GAAG,CAAC,aAAc,GAAG,KAAK;wBAC1C,MAAM,EAAE,KAAK;qBACd,CAAC;oBACF,kBAAkB,EAAE,OAAO,CAAC,kBAAkB;oBAC9C,oBAAoB,EAAE,OAAO,CAAC,oBAAoB;oBAClD,QAAQ,EAAE,OAAO,CAAC,QAAQ;oBAC1B,OAAO,EAAE,OAAO,CAAC,mBAAmB;iBACrC,CAAC;gBAEF,qBAAqB;gBACrB,eAAe;gBACf,0CAA0C;gBAC1C,2BAA2B;gBAC3B,mDAAmD;gBACnD,KAAK;gBAEL,OAAO,CACL,MAAM,IAAI,CAAC,WAAW,CAAC,QAAQ,iBAC7B,WAAW,EAAE,OAAO,CAAC,WAAW,IAC7B,sBAAsB,EACzB,CACH,CAAC,kBAAmB,CAAC;YACxB,CAAC,EACD,MAAM,EACN,GAAG,CAAC,aAAc,EAClB;gBACE,gBAAgB,EAAE,OAAO,CAAC,gBAAgB;gBAC1C,UAAU,EAAE,OAAO,CAAC,UAAU;aAC/B,CACF,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;OAQG;IACI,KAAK,CAAC,MAAM,CAAC,UAA6B,EAAE;QACjD,OAAO,aAAa,CAAC,QAAQ,CAAC,mBAAmB,EAAE,OAAO,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE;YACnF,IAAI,CAAC;gBACH,oBAAoB,CAAC,OAAO,CAAC,mBAAmB,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;gBAChE,MAAM,IAAI,CAAC,aAAa,CAAC;oBACvB,WAAW,EAAE,OAAO,CAAC,WAAW;oBAChC,mBAAmB,EAAE,OAAO,CAAC,mBAAmB;oBAChD,UAAU,EAAE,OAAO,CAAC,UAAU;oBAC9B,cAAc,EAAE,cAAc,CAAC,cAAc;iBAC9C,CAAC,CAAC;gBACH,OAAO,IAAI,CAAC;YACd,CAAC;YAAC,OAAO,CAAM,EAAE,CAAC;gBAChB,IAAI,CAAC,CAAC,UAAU,KAAK,GAAG,EAAE,CAAC;oBACzB,kDAAkD;oBAClD,OAAO,KAAK,CAAC;gBACf,CAAC;qBAAM,IACL,CAAC,CAAC,UAAU,KAAK,GAAG;oBACpB,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,KAAK,sCAAsC;wBAC7D,CAAC,CAAC,OAAO,CAAC,SAAS,KAAK,yCAAyC,CAAC,EACpE,CAAC;oBACD,kDAAkD;oBAClD,OAAO,IAAI,CAAC;gBACd,CAAC;gBACD,MAAM,CAAC,CAAC;YACV,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;OAWG;IACI,KAAK,CAAC,aAAa,CACxB,UAAoC,EAAE;QAEtC,OAAO,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,EAAE,CAAC;QAC9C,oBAAoB,CAAC,OAAO,CAAC,mBAAmB,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAChE,OAAO,aAAa,CAAC,QAAQ,CAAC,0BAA0B,EAAE,OAAO,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE;;YAC1F,MAAM,GAAG,GAAG,cAAc,CACxB,MAAM,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC;gBACnC,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,qBAAqB,EAAE,OAAO,CAAC,UAAU;gBACzC,wBAAwB,kCACnB,OAAO,CAAC,UAAU,KACrB,MAAM,EAAE,MAAA,OAAO,CAAC,UAAU,0CAAE,aAAa,GAC1C;gBACD,OAAO,EAAE,OAAO,CAAC,mBAAmB;gBACpC,cAAc,EAAE,cAAc,CAAC,cAAc;aAC9C,CAAC,CACH,CAAC;YAEF,uCACK,GAAG,KACN,SAAS,EAAE,GAAG,CAAC,SAAS,EACxB,oCAAoC,EAAE,GAAG,CAAC,yBAAyB,EACnE,iCAAiC,EAAE,4BAA4B,CAAC,GAAG,CAAC,sBAAsB,CAAC,IAC3F;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;OAQG;IACI,KAAK,CAAC,MAAM,CAAC,UAA6B,EAAE;QACjD,OAAO,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,EAAE,CAAC;QAC9C,OAAO,aAAa,CAAC,QAAQ,CAAC,mBAAmB,EAAE,OAAO,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE;;YACnF,OAAO,cAAc,CACnB,MAAM,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBAC5B,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,eAAe,EAAE,OAAO,CAAC,eAAe;gBACxC,qBAAqB,EAAE,OAAO,CAAC,UAAU;gBACzC,wBAAwB,kCACnB,OAAO,CAAC,UAAU,KACrB,MAAM,EAAE,MAAA,OAAO,CAAC,UAAU,0CAAE,aAAa,GAC1C;gBACD,cAAc,EAAE,cAAc,CAAC,cAAc;aAC9C,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;OAQG;IACI,KAAK,CAAC,cAAc,CACzB,UAA6B,EAAE;QAE/B,OAAO,aAAa,CAAC,QAAQ,CAAC,2BAA2B,EAAE,OAAO,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE;;YAC3F,IAAI,CAAC;gBACH,MAAM,GAAG,GAAG,cAAc,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC;gBAC9D,qCACE,SAAS,EAAE,IAAI,IACZ,GAAG,KACN,SAAS,EAAE,GAAG,CAAC,SAAS,IACxB;YACJ,CAAC;YAAC,OAAO,CAAM,EAAE,CAAC;gBAChB,IAAI,CAAA,MAAA,CAAC,CAAC,OAAO,0CAAE,SAAS,MAAK,cAAc,EAAE,CAAC;oBAC5C,qCACE,SAAS,EAAE,KAAK,IACb,MAAA,CAAC,CAAC,QAAQ,0CAAE,aAAa,KAC5B,SAAS,EAAE,CAAC,CAAC,QAAQ,IACrB;gBACJ,CAAC;gBACD,MAAM,CAAC,CAAC;YACV,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;OAOG;IACI,KAAK,CAAC,QAAQ,CAAC,UAA+B,EAAE;QACrD,OAAO,aAAa,CAAC,QAAQ,CAAC,qBAAqB,EAAE,OAAO,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE;YACrF,OAAO,cAAc,CACnB,MAAM,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC;gBAC9B,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,cAAc,EAAE,cAAc,CAAC,cAAc;aAC9C,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;OAcG;IACI,KAAK,CAAC,cAAc,CACzB,eAAiC,EACjC,UAAqC,EAAE;QAEvC,OAAO,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,EAAE,CAAC;QAC9C,oBAAoB,CAAC,OAAO,CAAC,mBAAmB,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAChE,OAAO,aAAa,CAAC,QAAQ,CAAC,2BAA2B,EAAE,OAAO,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE;;YAC3F,OAAO,cAAc,CACnB,MAAM,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC;gBACpC,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,eAAe,EAAE,eAAe;gBAChC,qBAAqB,EAAE,OAAO,CAAC,UAAU;gBACzC,wBAAwB,kCACnB,OAAO,CAAC,UAAU,KACrB,MAAM,EAAE,MAAA,OAAO,CAAC,UAAU,0CAAE,aAAa,GAC1C;gBACD,wIAAwI;gBACxI,cAAc,EAAE,cAAc,CAAC,cAAc;aAC9C,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;OAUG;IACI,KAAK,CAAC,WAAW,CACtB,QAAmB,EACnB,UAAkC,EAAE;QAEpC,OAAO,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,EAAE,CAAC;QAC9C,oBAAoB,CAAC,OAAO,CAAC,mBAAmB,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAChE,OAAO,aAAa,CAAC,QAAQ,CAAC,wBAAwB,EAAE,OAAO,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE;;YACxF,OAAO,cAAc,CACnB,MAAM,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC;gBACjC,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,qBAAqB,EAAE,OAAO,CAAC,UAAU;gBACzC,QAAQ;gBACR,wBAAwB,kCACnB,OAAO,CAAC,UAAU,KACrB,MAAM,EAAE,MAAA,OAAO,CAAC,UAAU,0CAAE,aAAa,GAC1C;gBACD,OAAO,EAAE,OAAO,CAAC,mBAAmB;gBACpC,eAAe,EAAE,OAAO,CAAC,eAAe;gBACxC,cAAc,EAAE,cAAc,CAAC,cAAc;aAC9C,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;OAQG;IACI,KAAK,CAAC,OAAO,CAAC,IAAU,EAAE,UAA8B,EAAE;QAC/D,OAAO,aAAa,CAAC,QAAQ,CAAC,oBAAoB,EAAE,OAAO,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE;;YACpF,OAAO,cAAc,CACnB,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;gBAC7B,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,qBAAqB,EAAE,OAAO,CAAC,UAAU;gBACzC,wBAAwB,kCACnB,OAAO,CAAC,UAAU,KACrB,MAAM,EAAE,MAAA,OAAO,CAAC,UAAU,0CAAE,aAAa,GAC1C;gBACD,cAAc,EAAE,cAAc,CAAC,cAAc;gBAC7C,IAAI,EAAE,UAAU,CAAC,IAAI,CAAC;aACvB,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;OAIG;IACI,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE;QACnD,OAAO,aAAa,CAAC,QAAQ,CAAC,oBAAoB,EAAE,OAAO,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE;;YACpF,MAAM,QAAQ,GAAG,cAAc,CAC7B,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;gBAC7B,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,qBAAqB,EAAE,OAAO,CAAC,UAAU;gBACzC,wBAAwB,kCACnB,OAAO,CAAC,UAAU,KACrB,MAAM,EAAE,MAAA,OAAO,CAAC,UAAU,0CAAE,aAAa,GAC1C;gBACD,cAAc,EAAE,cAAc,CAAC,cAAc;aAC9C,CAAC,CACH,CAAC;YACF,MAAM,eAAe,mCAChB,QAAQ,KACX,SAAS,EAAE,QAAQ,CAAC,SAAS,EAC7B,IAAI,EAAE,MAAM,CAAC,EAAE,UAAU,EAAE,QAAQ,CAAC,UAAU,EAAE,CAAC,IAAI,EAAE,GACxD,CAAC;YACF,OAAO,eAAe,CAAC;QACzB,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;OAKG;IACI,kBAAkB,CAAC,cAAuB;QAC/C,OAAO,IAAI,eAAe,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;IACnD,CAAC;IAED;;;;;OAKG;IACI,KAAK,CAAC,cAAc,CACzB,UAAqC,EAAE;QAEvC,OAAO,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,EAAE,CAAC;QAC9C,oBAAoB,CAAC,OAAO,CAAC,mBAAmB,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAChE,OAAO,aAAa,CAAC,QAAQ,CAAC,2BAA2B,EAAE,OAAO,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE;;YAC3F,OAAO,cAAc,CACnB,MAAM,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC;gBACpC,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,qBAAqB,EAAE,OAAO,CAAC,UAAU;gBACzC,QAAQ,EAAE,OAAO,CAAC,QAAQ;gBAC1B,wBAAwB,kCACnB,OAAO,CAAC,UAAU,KACrB,MAAM,EAAE,MAAA,OAAO,CAAC,UAAU,0CAAE,aAAa,GAC1C;gBACD,OAAO,EAAE,OAAO,CAAC,mBAAmB;gBACpC,eAAe,EAAE,OAAO,CAAC,eAAe;gBACxC,cAAc,EAAE,cAAc,CAAC,cAAc;aAC9C,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAuEG;IACI,KAAK,CAAC,gBAAgB,CAC3B,UAAkB,EAClB,UAAuC,EAAE;QAOzC,MAAM,MAAM,GAAyB;YACnC,gBAAgB,EAAE,CAAC,GAAG,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,gBAAgB,CAAC,GAAG,IAAI,CAAC;YAC7D,aAAa,EAAE,CAAC,GAAG,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,IAAI,CAAC;YACvD,gBAAgB,EAAE,CAAC,GAAG,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,gBAAgB,CAAC,GAAG,IAAI,CAAC;SAC9D,CAAC;QACF,MAAM,MAAM,GAAG,IAAI,0BAA0B,CAAC;YAC5C,UAAU,EAAE,MAAM;YAClB,UAAU;YACV,YAAY,EAAE,OAAO,CAAC,YAAY;YAClC,UAAU,EAAE,OAAO,CAAC,UAAU;YAC9B,UAAU,EAAE,OAAO,CAAC,UAAU;YAC9B,uBAAuB,EAAE,OAAO;SACjC,CAAC,CAAC;QAEH,qDAAqD;QACrD,8DAA8D;QAC9D,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC;QAEpB,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;;;;;;OAOG;IACI,KAAK,CAAC,gBAAgB,CAC3B,MAAc,EACd,UAAuC,EAAE;QAEzC,OAAO,aAAa,CAAC,QAAQ,CAC3B,6BAA6B,EAC7B,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,OAAO,cAAc,CACnB,MAAM,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,MAAM,EAAE;gBAC9C,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,qBAAqB,EAAE,OAAO,CAAC,UAAU;gBACzC,cAAc,EAAE,cAAc,CAAC,cAAc;aAC9C,CAAC,CACH,CAAC;QACJ,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;OAOG;IACI,KAAK,CAAC,eAAe,CAC1B,UAAkB,EAClB,UAAsC,EAAE;QAExC,OAAO,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,EAAE,CAAC;QAC9C,OAAO,CAAC,gBAAgB,GAAG,OAAO,CAAC,gBAAgB,IAAI,EAAE,CAAC;QAC1D,OAAO,aAAa,CAAC,QAAQ,CAAC,4BAA4B,EAAE,OAAO,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE;;YAC5F,OAAO,cAAc,CACnB,MAAM,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,UAAU,EAAE;gBAC7C,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,QAAQ,EAAE,OAAO,CAAC,QAAQ;gBAC1B,qBAAqB,EAAE,OAAO,CAAC,UAAU;gBACzC,wBAAwB,kCACnB,OAAO,CAAC,UAAU,KACrB,MAAM,EAAE,MAAA,OAAO,CAAC,UAAU,0CAAE,aAAa,GAC1C;gBACD,8BAA8B,EAAE;oBAC9B,aAAa,EAAE,MAAA,OAAO,CAAC,gBAAgB,0CAAE,OAAO;oBAChD,qBAAqB,EAAE,MAAA,OAAO,CAAC,gBAAgB,0CAAE,eAAe;oBAChE,iBAAiB,EAAE,MAAA,OAAO,CAAC,gBAAgB,0CAAE,WAAW;oBACxD,uBAAuB,EAAE,MAAA,OAAO,CAAC,gBAAgB,0CAAE,iBAAiB;iBACrE;gBACD,gBAAgB,EAAE,OAAO,CAAC,gBAAgB;gBAC1C,uBAAuB,EAAE,yBAAyB,CAAC,OAAO,CAAC,mBAAmB,CAAC;gBAC/E,IAAI,EAAE,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC;gBAChC,cAAc,EAAE,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC;gBAC9C,wBAAwB,EAAE,MAAA,OAAO,CAAC,kBAAkB,0CAAE,UAAU;gBAChE,sBAAsB,EAAE,MAAA,OAAO,CAAC,kBAAkB,0CAAE,UAAU;gBAC9D,SAAS,EAAE,OAAO,CAAC,SAAS;gBAC5B,eAAe,EAAE,OAAO,CAAC,eAAe;gBACxC,cAAc,EAAE,OAAO,CAAC,cAAc;gBACtC,cAAc,EAAE,cAAc,CAAC,cAAc;aAC9C,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;OAUG;IACI,KAAK,CAAC,aAAa,CACxB,IAAkD,EAClD,UAA8B,EAAE;QAEhC,OAAO,aAAa,CAAC,QAAQ,CAAC,0BAA0B,EAAE,OAAO,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE;;YAC1F,OAAO,cAAc,CACnB,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAE,EAAE;gBAClD,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,qBAAqB,EAAE,OAAO,CAAC,UAAU;gBACzC,wBAAwB,kCACnB,OAAO,CAAC,UAAU,KACrB,MAAM,EAAE,MAAA,OAAO,CAAC,UAAU,0CAAE,aAAa,GAC1C;gBACD,iBAAiB,EAAE,OAAO,CAAC,iBAAiB;gBAC5C,cAAc,EAAE,cAAc,CAAC,cAAc;aAC9C,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IA8CM,KAAK,CAAC,gBAAgB,CAC3B,MAAwB,EACxB,MAAe,EACf,MAA6C,EAC7C,SAAsC,EAAE;;QAExC,IAAI,MAA0B,CAAC;QAC/B,IAAI,MAAM,GAAG,CAAC,CAAC;QACf,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,IAAI,OAAO,GAAG,MAAM,CAAC;QACrB,IAAI,MAAM,YAAY,MAAM,EAAE,CAAC;YAC7B,MAAM,GAAG,MAAM,CAAC;YAChB,MAAM,GAAG,MAAM,IAAI,CAAC,CAAC;YACrB,KAAK,GAAG,OAAO,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAClD,CAAC;aAAM,CAAC;YACN,MAAM,GAAG,OAAO,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YACjD,KAAK,GAAG,OAAO,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YAChD,OAAO,GAAI,MAAsC,IAAI,EAAE,CAAC;QAC1D,CAAC;QAED,IAAI,SAAS,GAAG,MAAA,OAAO,CAAC,SAAS,mCAAI,CAAC,CAAC;QAEvC,IAAI,SAAS,GAAG,CAAC,EAAE,CAAC;YAClB,MAAM,IAAI,UAAU,CAAC,+BAA+B,CAAC,CAAC;QACxD,CAAC;QACD,IAAI,SAAS,KAAK,CAAC,EAAE,CAAC;YACpB,SAAS,GAAG,iCAAiC,CAAC;QAChD,CAAC;QAED,IAAI,MAAM,GAAG,CAAC,EAAE,CAAC;YACf,MAAM,IAAI,UAAU,CAAC,4BAA4B,CAAC,CAAC;QACrD,CAAC;QAED,IAAI,KAAK,IAAI,KAAK,IAAI,CAAC,EAAE,CAAC;YACxB,MAAM,IAAI,UAAU,CAAC,qCAAqC,CAAC,CAAC;QAC9D,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;YACxB,OAAO,CAAC,UAAU,GAAG,EAAE,CAAC;QAC1B,CAAC;QAED,OAAO,aAAa,CAAC,QAAQ,CAC3B,6BAA6B,EAC7B,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,0CAA0C;YAC1C,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,aAAa,iCACpC,OAAO,KACV,cAAc,EAAE,cAAc,CAAC,cAAc,IAC7C,CAAC;gBACH,KAAK,GAAG,QAAQ,CAAC,aAAc,GAAG,MAAM,CAAC;gBACzC,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC;oBACd,MAAM,IAAI,UAAU,CAClB,UAAU,MAAM,uCAAuC,QAAQ,CAAC,aAAc,EAAE,CACjF,CAAC;gBACJ,CAAC;YACH,CAAC;YAED,oEAAoE;YACpE,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,IAAI,CAAC;oBACH,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;gBAC/B,CAAC;gBAAC,OAAO,KAAU,EAAE,CAAC;oBACpB,MAAM,IAAI,KAAK,CACb,0CAA0C,KAAK,qJAAqJ,KAAK,CAAC,OAAO,EAAE,CACpN,CAAC;gBACJ,CAAC;YACH,CAAC;YAED,IAAI,MAAM,CAAC,MAAM,GAAG,KAAK,EAAE,CAAC;gBAC1B,MAAM,IAAI,UAAU,CAClB,mFAAmF,KAAK,EAAE,CAC3F,CAAC;YACJ,CAAC;YAED,IAAI,gBAAgB,GAAW,CAAC,CAAC;YACjC,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;YAC7C,KAAK,IAAI,GAAG,GAAG,MAAM,EAAE,GAAG,GAAG,MAAM,GAAG,KAAK,EAAE,GAAG,GAAG,GAAG,GAAG,SAAS,EAAE,CAAC;gBACnE,KAAK,CAAC,YAAY,CAAC,KAAK,IAAI,EAAE;oBAC5B,+BAA+B;oBAC/B,IAAI,QAAQ,GAAG,MAAM,GAAG,KAAM,CAAC;oBAC/B,IAAI,GAAG,GAAG,SAAS,GAAG,QAAQ,EAAE,CAAC;wBAC/B,QAAQ,GAAG,GAAG,GAAG,SAAS,CAAC;oBAC7B,CAAC;oBACD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,QAAQ,GAAG,GAAG,EAAE;wBACxD,WAAW,EAAE,OAAO,CAAC,WAAW;wBAChC,UAAU,EAAE,OAAO,CAAC,UAAU;wBAC9B,gBAAgB,EAAE,OAAO,CAAC,wBAAwB;wBAClD,mBAAmB,EAAE,OAAO,CAAC,mBAAmB;wBAChD,cAAc,EAAE,cAAc,CAAC,cAAc;qBAC9C,CAAC,CAAC;oBACH,MAAM,MAAM,GAAG,QAAQ,CAAC,kBAAmB,CAAC;oBAC5C,MAAM,cAAc,CAAC,MAAM,EAAE,MAAO,EAAE,GAAG,GAAG,MAAM,EAAE,QAAQ,GAAG,MAAM,CAAC,CAAC;oBACvE,qEAAqE;oBACrE,sEAAsE;oBACtE,oDAAoD;oBACpD,gBAAgB,IAAI,QAAQ,GAAG,GAAG,CAAC;oBACnC,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;wBACvB,OAAO,CAAC,UAAU,CAAC,EAAE,WAAW,EAAE,gBAAgB,EAAE,CAAC,CAAC;oBACxD,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;YACD,MAAM,KAAK,CAAC,EAAE,EAAE,CAAC;YACjB,OAAO,MAAM,CAAC;QAChB,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;;;;OAeG;IACI,KAAK,CAAC,cAAc,CACzB,QAAgB,EAChB,SAAiB,CAAC,EAClB,KAAc,EACd,UAA+B,EAAE;QAEjC,OAAO,aAAa,CAAC,QAAQ,CAAC,2BAA2B,EAAE,OAAO,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE;YAC3F,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,KAAK,kCAC7C,OAAO,KACV,cAAc,EAAE,cAAc,CAAC,cAAc,IAC7C,CAAC;YACH,IAAI,QAAQ,CAAC,kBAAkB,EAAE,CAAC;gBAChC,MAAM,qBAAqB,CAAC,QAAQ,CAAC,kBAAkB,EAAE,QAAQ,CAAC,CAAC;YACrE,CAAC;YAED,iEAAiE;YAChE,QAAgB,CAAC,kBAAkB,GAAG,SAAS,CAAC;YACjD,OAAO,QAAQ,CAAC;QAClB,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,+BAA+B;QACrC,IAAI,aAAa,CAAC;QAClB,IAAI,QAAQ,CAAC;QACb,IAAI,CAAC;YACH,mCAAmC;YACnC,wEAAwE;YACxE,8DAA8D;YAC9D,8EAA8E;YAC9E,oEAAoE;YACpE,oGAAoG;YACpG,6DAA6D;YAE7D,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAEpC,IAAI,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,MAAM,EAAE,CAAC;gBAC5C,gEAAgE;gBAChE,oCAAoC;gBACpC,MAAM,cAAc,GAAG,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC;gBACpE,aAAa,GAAG,cAAe,CAAC,CAAC,CAAC,CAAC;gBACnC,QAAQ,GAAG,cAAe,CAAC,CAAC,CAAC,CAAC;YAChC,CAAC;iBAAM,IAAI,iBAAiB,CAAC,SAAS,CAAC,EAAE,CAAC;gBACxC,mGAAmG;gBACnG,6HAA6H;gBAC7H,qDAAqD;gBACrD,MAAM,cAAc,GAAG,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,0BAA0B,CAAC,CAAC;gBAC5E,aAAa,GAAG,cAAe,CAAC,CAAC,CAAC,CAAC;gBACnC,QAAQ,GAAG,cAAe,CAAC,CAAC,CAAC,CAAC;YAChC,CAAC;iBAAM,CAAC;gBACN,iDAAiD;gBACjD,oCAAoC;gBACpC,MAAM,cAAc,GAAG,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC;gBACpE,aAAa,GAAG,cAAe,CAAC,CAAC,CAAC,CAAC;gBACnC,QAAQ,GAAG,cAAe,CAAC,CAAC,CAAC,CAAC;YAChC,CAAC;YAED,+GAA+G;YAC/G,aAAa,GAAG,kBAAkB,CAAC,aAAa,CAAC,CAAC;YAClD,QAAQ,GAAG,kBAAkB,CAAC,QAAQ,CAAC,CAAC;YAExC,mEAAmE;YACnE,0GAA0G;YAC1G,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;YAExC,IAAI,CAAC,aAAa,EAAE,CAAC;gBACnB,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;YACxD,CAAC;YAED,OAAO,EAAE,QAAQ,EAAE,aAAa,EAAE,CAAC;QACrC,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,MAAM,IAAI,KAAK,CAAC,yEAAyE,CAAC,CAAC;QAC7F,CAAC;IACH,CAAC;IAED;;;;;;;;;;;;OAYG;IACK,KAAK,CAAC,gBAAgB,CAC5B,UAAkB,EAClB,UAAuC,EAAE;QAEzC,OAAO,aAAa,CAAC,QAAQ,CAC3B,6BAA6B,EAC7B,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;;YACvB,OAAO,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,EAAE,CAAC;YAC9C,OAAO,CAAC,gBAAgB,GAAG,OAAO,CAAC,gBAAgB,IAAI,EAAE,CAAC;YAC1D,OAAO,cAAc,CACnB,MAAM,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,UAAU,EAAE;gBAClD,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,qBAAqB,EAAE,OAAO,CAAC,UAAU;gBACzC,QAAQ,EAAE,OAAO,CAAC,QAAQ;gBAC1B,wBAAwB,kCACnB,OAAO,CAAC,UAAU,KACrB,MAAM,EAAE,MAAA,OAAO,CAAC,UAAU,0CAAE,aAAa,GAC1C;gBACD,8BAA8B,EAAE;oBAC9B,aAAa,EAAE,OAAO,CAAC,gBAAgB,CAAC,OAAO;oBAC/C,qBAAqB,EAAE,OAAO,CAAC,gBAAgB,CAAC,eAAe;oBAC/D,iBAAiB,EAAE,OAAO,CAAC,gBAAgB,CAAC,WAAW;oBACvD,uBAAuB,EAAE,OAAO,CAAC,gBAAgB,CAAC,iBAAiB;oBACnE,YAAY,EAAE,OAAO,CAAC,gBAAgB,CAAC,aAAa;iBACrD;gBACD,wBAAwB,EAAE,MAAA,OAAO,CAAC,kBAAkB,0CAAE,UAAU;gBAChE,sBAAsB,EAAE,MAAA,OAAO,CAAC,kBAAkB,0CAAE,UAAU;gBAC9D,SAAS,EAAE,OAAO,CAAC,SAAS;gBAC5B,iBAAiB,EAAE,OAAO,CAAC,iBAAiB;gBAC5C,IAAI,EAAE,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC;gBAChC,cAAc,EAAE,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC;gBAC9C,QAAQ,EAAE,OAAO,CAAC,QAAQ;gBAC1B,cAAc,EAAE,cAAc,CAAC,cAAc;aAC9C,CAAC,CACH,CAAC;QACJ,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;;;;OAUG;IACI,cAAc,CAAC,OAAkC;QACtD,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC7B,IAAI,CAAC,CAAC,IAAI,CAAC,UAAU,YAAY,0BAA0B,CAAC,EAAE,CAAC;gBAC7D,MAAM,IAAI,UAAU,CAClB,uFAAuF,CACxF,CAAC;YACJ,CAAC;YAED,MAAM,GAAG,GAAG,8BAA8B,iBAEtC,aAAa,EAAE,IAAI,CAAC,cAAc,EAClC,QAAQ,EAAE,IAAI,CAAC,KAAK,EACpB,YAAY,EAAE,IAAI,CAAC,SAAS,EAC5B,SAAS,EAAE,IAAI,CAAC,UAAU,IACvB,OAAO,GAEZ,IAAI,CAAC,UAAU,CAChB,CAAC,QAAQ,EAAE,CAAC;YAEb,OAAO,CAAC,gBAAgB,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;OAUG;IACH,gEAAgE;IACzD,uBAAuB,CAAC,OAAkC;QAC/D,IAAI,CAAC,CAAC,IAAI,CAAC,UAAU,YAAY,0BAA0B,CAAC,EAAE,CAAC;YAC7D,MAAM,IAAI,UAAU,CAClB,uFAAuF,CACxF,CAAC;QACJ,CAAC;QAED,OAAO,sCAAsC,iBAEzC,aAAa,EAAE,IAAI,CAAC,cAAc,EAClC,QAAQ,EAAE,IAAI,CAAC,KAAK,EACpB,YAAY,EAAE,IAAI,CAAC,SAAS,EAC5B,SAAS,EAAE,IAAI,CAAC,UAAU,IACvB,OAAO,GAEZ,IAAI,CAAC,UAAU,CAChB,CAAC,YAAY,CAAC;IACjB,CAAC;IAED;;;;;;;;;;OAUG;IACI,4BAA4B,CACjC,OAAkC,EAClC,iBAAoC;QAEpC,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC7B,MAAM,GAAG,GAAG,8BAA8B,iBAEtC,aAAa,EAAE,IAAI,CAAC,cAAc,EAClC,QAAQ,EAAE,IAAI,CAAC,KAAK,EACpB,YAAY,EAAE,IAAI,CAAC,SAAS,EAC5B,SAAS,EAAE,IAAI,CAAC,UAAU,IACvB,OAAO,GAEZ,iBAAiB,EACjB,IAAI,CAAC,WAAW,CACjB,CAAC,QAAQ,EAAE,CAAC;YAEb,OAAO,CAAC,gBAAgB,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;OAWG;IAEI,qCAAqC,CAC1C,OAAkC,EAClC,iBAAoC;QAEpC,OAAO,sCAAsC,iBAEzC,aAAa,EAAE,IAAI,CAAC,cAAc,EAClC,QAAQ,EAAE,IAAI,CAAC,KAAK,EACpB,YAAY,EAAE,IAAI,CAAC,SAAS,EAC5B,SAAS,EAAE,IAAI,CAAC,UAAU,IACvB,OAAO,GAEZ,iBAAiB,EACjB,IAAI,CAAC,WAAW,CACjB,CAAC,YAAY,CAAC;IACjB,CAAC;IAED;;;;OAIG;IACI,KAAK,CAAC,wBAAwB,CACnC,UAA+C,EAAE;QAEjD,OAAO,aAAa,CAAC,QAAQ,CAC3B,qCAAqC,EACrC,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,OAAO,cAAc,CAInB,MAAM,IAAI,CAAC,WAAW,CAAC,wBAAwB,CAAC;gBAC9C,cAAc,EAAE,cAAc,CAAC,cAAc;aAC9C,CAAC,CACH,CAAC;QACJ,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACI,KAAK,CAAC,qBAAqB,CAChC,kBAA0C,EAC1C,UAA4C,EAAE;QAE9C,OAAO,aAAa,CAAC,QAAQ,CAC3B,kCAAkC,EAClC,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,OAAO,cAAc,CACnB,MAAM,IAAI,CAAC,WAAW,CAAC,qBAAqB,CAAC;gBAC3C,wBAAwB,EAAE,kBAAkB,CAAC,UAAU;gBACvD,sBAAsB,EAAE,kBAAkB,CAAC,UAAU;gBACrD,cAAc,EAAE,cAAc,CAAC,cAAc;aAC9C,CAAC,CACH,CAAC;QACJ,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACI,KAAK,CAAC,YAAY,CACvB,gBAAyB,EACzB,UAAmC,EAAE;QAErC,OAAO,aAAa,CAAC,QAAQ,CAAC,yBAAyB,EAAE,OAAO,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE;YACzF,OAAO,cAAc,CACnB,MAAM,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,gBAAgB,EAAE;gBACpD,cAAc,EAAE,cAAc,CAAC,cAAc;aAC9C,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;OASG;IACI,KAAK,CAAC,cAAc,CACzB,UAAqC,EAAE;QAEvC,OAAO,aAAa,CAAC,QAAQ,CAAC,2BAA2B,EAAE,OAAO,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE;YAC3F,OAAO,cAAc,CACnB,MAAM,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC;gBACpC,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,cAAc,EAAE,cAAc,CAAC,cAAc;aAC9C,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;CACF;AA4ND;;GAEG;AACH,MAAM,OAAO,gBAAiB,SAAQ,UAAU;IAsE9C,YACE,qBAA6B,EAC7B,mCAKgB,EAChB,iBAAmD;IACnD,mFAAmF;IACnF,gEAAgE;IAChE,OAAgC;QAEhC,kHAAkH;QAClH,wFAAwF;QACxF,IAAI,QAAsB,CAAC;QAC3B,IAAI,GAAW,CAAC;QAChB,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;QACxB,IAAI,cAAc,CAAC,mCAAmC,CAAC,EAAE,CAAC;YACxD,oCAAoC;YACpC,GAAG,GAAG,qBAAqB,CAAC;YAC5B,QAAQ,GAAG,mCAAmC,CAAC;QACjD,CAAC;aAAM,IACL,CAAC,MAAM,IAAI,mCAAmC,YAAY,0BAA0B,CAAC;YACrF,mCAAmC,YAAY,mBAAmB;YAClE,iBAAiB,CAAC,mCAAmC,CAAC,EACtD,CAAC;YACD,qKAAqK;YACrK,GAAG,GAAG,qBAAqB,CAAC;YAC5B,OAAO,GAAG,iBAA2C,CAAC;YACtD,QAAQ,GAAG,WAAW,CAAC,mCAAmC,EAAE,OAAO,CAAC,CAAC;QACvE,CAAC;aAAM,IACL,CAAC,mCAAmC;YACpC,OAAO,mCAAmC,KAAK,QAAQ,EACvD,CAAC;YACD,mIAAmI;YACnI,GAAG,GAAG,qBAAqB,CAAC;YAC5B,+DAA+D;YAC/D,QAAQ,GAAG,WAAW,CAAC,IAAI,mBAAmB,EAAE,EAAE,OAAO,CAAC,CAAC;QAC7D,CAAC;aAAM,IACL,mCAAmC;YACnC,OAAO,mCAAmC,KAAK,QAAQ;YACvD,iBAAiB;YACjB,OAAO,iBAAiB,KAAK,QAAQ,EACrC,CAAC;YACD,wGAAwG;YACxG,MAAM,aAAa,GAAG,mCAAmC,CAAC;YAC1D,MAAM,QAAQ,GAAG,iBAAiB,CAAC;YAEnC,MAAM,cAAc,GAAG,4BAA4B,CAAC,qBAAqB,CAAC,CAAC;YAC3E,IAAI,cAAc,CAAC,IAAI,KAAK,mBAAmB,EAAE,CAAC;gBAChD,IAAI,MAAM,EAAE,CAAC;oBACX,MAAM,mBAAmB,GAAG,IAAI,0BAA0B,CACxD,cAAc,CAAC,WAAY,EAC3B,cAAc,CAAC,UAAU,CAC1B,CAAC;oBACF,GAAG,GAAG,eAAe,CACnB,eAAe,CAAC,cAAc,CAAC,GAAG,EAAE,kBAAkB,CAAC,aAAa,CAAC,CAAC,EACtE,kBAAkB,CAAC,QAAQ,CAAC,CAC7B,CAAC;oBAEF,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC;wBAC1B,OAAO,CAAC,YAAY,GAAG,uBAAuB,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;oBAC1E,CAAC;oBAED,QAAQ,GAAG,WAAW,CAAC,mBAAmB,EAAE,OAAO,CAAC,CAAC;gBACvD,CAAC;qBAAM,CAAC;oBACN,MAAM,IAAI,KAAK,CAAC,oEAAoE,CAAC,CAAC;gBACxF,CAAC;YACH,CAAC;iBAAM,IAAI,cAAc,CAAC,IAAI,KAAK,eAAe,EAAE,CAAC;gBACnD,GAAG;oBACD,eAAe,CACb,eAAe,CAAC,cAAc,CAAC,GAAG,EAAE,kBAAkB,CAAC,aAAa,CAAC,CAAC,EACtE,kBAAkB,CAAC,QAAQ,CAAC,CAC7B;wBACD,GAAG;wBACH,cAAc,CAAC,UAAU,CAAC;gBAC5B,QAAQ,GAAG,WAAW,CAAC,IAAI,mBAAmB,EAAE,EAAE,OAAO,CAAC,CAAC;YAC7D,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,KAAK,CACb,0FAA0F,CAC3F,CAAC;YACJ,CAAC;QACH,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,KAAK,CAAC,uEAAuE,CAAC,CAAC;QAC3F,CAAC;QACD,KAAK,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;QACrB,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,oBAAoB,CAAC,UAAU,CAAC;IAChE,CAAC;IAED;;;;;;;OAOG;IACI,YAAY,CAAC,QAAgB;QAClC,OAAO,IAAI,gBAAgB,CACzB,eAAe,CACb,IAAI,CAAC,GAAG,EACR,YAAY,CAAC,UAAU,CAAC,QAAQ,EAChC,QAAQ,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,CAC7C,EACD,IAAI,CAAC,QAAQ,CACd,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;;OAaG;IACI,KAAK,CAAC,MAAM,CAAC,UAAmC,EAAE;QACvD,OAAO,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,EAAE,CAAC;QAC9C,oBAAoB,CAAC,OAAO,CAAC,mBAAmB,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAChE,OAAO,aAAa,CAAC,QAAQ,CAAC,yBAAyB,EAAE,OAAO,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE;;YACzF,OAAO,cAAc,CACnB,MAAM,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,EAAE;gBACrC,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,eAAe,EAAE,OAAO,CAAC,eAAe;gBACxC,qBAAqB,EAAE,OAAO,CAAC,UAAU;gBACzC,QAAQ,EAAE,OAAO,CAAC,QAAQ;gBAC1B,wBAAwB,kCACnB,OAAO,CAAC,UAAU,KACrB,MAAM,EAAE,MAAA,OAAO,CAAC,UAAU,0CAAE,aAAa,GAC1C;gBACD,OAAO,EAAE,OAAO,CAAC,mBAAmB;gBACpC,eAAe,EAAE,OAAO,CAAC,eAAe;gBACxC,wBAAwB,EAAE,MAAA,OAAO,CAAC,kBAAkB,0CAAE,UAAU;gBAChE,sBAAsB,EAAE,MAAA,OAAO,CAAC,kBAAkB,0CAAE,UAAU;gBAC9D,SAAS,EAAE,OAAO,CAAC,SAAS;gBAC5B,cAAc,EAAE,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC;gBAC9C,cAAc,EAAE,cAAc,CAAC,cAAc;aAC9C,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;OAMG;IACI,KAAK,CAAC,iBAAiB,CAC5B,UAA8C,EAAE;QAEhD,MAAM,UAAU,GAAG,EAAE,WAAW,EAAE,OAAO,EAAE,CAAC;QAC5C,OAAO,aAAa,CAAC,QAAQ,CAC3B,oCAAoC,EACpC,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;;YACvB,IAAI,CAAC;gBACH,MAAM,GAAG,GAAG,cAAc,CACxB,MAAM,IAAI,CAAC,MAAM,iCACZ,cAAc,KACjB,UAAU,IACV,CACH,CAAC;gBACF,qCACE,SAAS,EAAE,IAAI,IACZ,GAAG,KACN,SAAS,EAAE,GAAG,CAAC,SAAS,IACxB;YACJ,CAAC;YAAC,OAAO,CAAM,EAAE,CAAC;gBAChB,IAAI,CAAA,MAAA,CAAC,CAAC,OAAO,0CAAE,SAAS,MAAK,mBAAmB,EAAE,CAAC;oBACjD,qCACE,SAAS,EAAE,KAAK,IACb,MAAA,CAAC,CAAC,QAAQ,0CAAE,aAAa,KAC5B,SAAS,EAAE,CAAC,CAAC,QAAQ,IACrB;gBACJ,CAAC;gBACD,MAAM,CAAC,CAAC;YACV,CAAC;QACH,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACI,KAAK,CAAC,IAAI,CAAC,UAAiC,EAAE;QACnD,OAAO,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,EAAE,CAAC;QAC9C,OAAO,aAAa,CAAC,QAAQ,CAAC,uBAAuB,EAAE,OAAO,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE;;YACvF,OAAO,cAAc,CACnB,MAAM,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC;gBAChC,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,8BAA8B,EAAE,OAAO,CAAC,UAAU;gBAClD,qBAAqB,EAAE,OAAO,CAAC,UAAU;gBACzC,wBAAwB,kCACnB,OAAO,CAAC,UAAU,KACrB,MAAM,EAAE,MAAA,OAAO,CAAC,UAAU,0CAAE,aAAa,GAC1C;gBACD,cAAc,EAAE,cAAc,CAAC,cAAc;aAC9C,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;OAuBG;IACI,KAAK,CAAC,WAAW,CACtB,IAAqB,EACrB,aAAqB,EACrB,UAAwC,EAAE;QAE1C,OAAO,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,EAAE,CAAC;QAC9C,oBAAoB,CAAC,OAAO,CAAC,mBAAmB,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAChE,OAAO,aAAa,CAAC,QAAQ,CAC3B,8BAA8B,EAC9B,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;;YACvB,OAAO,cAAc,CACnB,MAAM,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,aAAa,EAAE,IAAI,EAAE;gBAC5D,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,8BAA8B,EAAE,OAAO,CAAC,UAAU;gBAClD,qBAAqB,EAAE,OAAO,CAAC,UAAU;gBACzC,wBAAwB,kCACnB,OAAO,CAAC,UAAU,KACrB,MAAM,EAAE,MAAA,OAAO,CAAC,UAAU,0CAAE,aAAa,GAC1C;gBACD,cAAc,EAAE;oBACd,gBAAgB,EAAE,OAAO,CAAC,UAAU;iBACrC;gBACD,uBAAuB,EAAE,OAAO,CAAC,uBAAuB;gBACxD,yBAAyB,EAAE,OAAO,CAAC,yBAAyB;gBAC5D,OAAO,EAAE,OAAO,CAAC,mBAAmB;gBACpC,eAAe,EAAE,OAAO,CAAC,eAAe;gBACxC,cAAc,EAAE,cAAc,CAAC,cAAc;aAC9C,CAAC,CACH,CAAC;QACJ,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;;OAaG;IACI,KAAK,CAAC,kBAAkB,CAC7B,SAAiB,EACjB,YAAoB,EACpB,KAAa,EACb,UAA+C,EAAE;QAEjD,OAAO,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,EAAE,CAAC;QAC9C,OAAO,CAAC,gBAAgB,GAAG,OAAO,CAAC,gBAAgB,IAAI,EAAE,CAAC;QAE1D,oBAAoB,CAAC,OAAO,CAAC,mBAAmB,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAChE,OAAO,aAAa,CAAC,QAAQ,CAC3B,qCAAqC,EACrC,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;;YACvB,OAAO,cAAc,CAInB,MAAM,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,SAAS,EAAE,CAAC,EAAE;gBAC5D,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,WAAW,EAAE,aAAa,CAAC,EAAE,MAAM,EAAE,YAAY,EAAE,KAAK,EAAE,CAAC;gBAC3D,gBAAgB,EAAE,OAAO,CAAC,gBAAgB;gBAC1C,kBAAkB,EAAE,OAAO,CAAC,kBAAkB;gBAC9C,qBAAqB,EAAE,OAAO,CAAC,UAAU;gBACzC,8BAA8B,EAAE,OAAO,CAAC,UAAU;gBAClD,wBAAwB,kCACnB,OAAO,CAAC,UAAU,KACrB,MAAM,EAAE,MAAA,OAAO,CAAC,UAAU,0CAAE,aAAa,GAC1C;gBACD,8BAA8B,EAAE;oBAC9B,aAAa,EAAE,MAAA,OAAO,CAAC,gBAAgB,0CAAE,OAAO;oBAChD,qBAAqB,EAAE,MAAA,OAAO,CAAC,gBAAgB,0CAAE,eAAe;oBAChE,iBAAiB,EAAE,MAAA,OAAO,CAAC,gBAAgB,0CAAE,WAAW;oBACxD,uBAAuB,EAAE,MAAA,OAAO,CAAC,gBAAgB,0CAAE,iBAAiB;iBACrE;gBACD,uBAAuB,EAAE,yBAAyB,CAAC,OAAO,CAAC,mBAAmB,CAAC;gBAC/E,OAAO,EAAE,OAAO,CAAC,mBAAmB;gBACpC,eAAe,EAAE,OAAO,CAAC,eAAe;gBACxC,cAAc,EAAE,cAAc,CAAC,cAAc;aAC9C,CAAC,CACH,CAAC;QACJ,CAAC,CACF,CAAC;IACJ,CAAC;CACF;AAmkBD;;GAEG;AACH,MAAM,OAAO,eAAgB,SAAQ,UAAU;IA8E7C,YACE,qBAA6B,EAC7B,mCAKgB,EAChB,iBAAmD;IACnD,mFAAmF;IACnF,gEAAgE;IAChE,OAAgC;QAEhC,kHAAkH;QAClH,wFAAwF;QACxF,IAAI,QAAsB,CAAC;QAC3B,IAAI,GAAW,CAAC;QAChB,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;QACxB,IAAI,cAAc,CAAC,mCAAmC,CAAC,EAAE,CAAC;YACxD,oCAAoC;YACpC,GAAG,GAAG,qBAAqB,CAAC;YAC5B,QAAQ,GAAG,mCAAmC,CAAC;QACjD,CAAC;aAAM,IACL,CAAC,MAAM,IAAI,mCAAmC,YAAY,0BAA0B,CAAC;YACrF,mCAAmC,YAAY,mBAAmB;YAClE,iBAAiB,CAAC,mCAAmC,CAAC,EACtD,CAAC;YACD,mIAAmI;YACnI,GAAG,GAAG,qBAAqB,CAAC;YAC5B,OAAO,GAAG,iBAA2C,CAAC;YACtD,QAAQ,GAAG,WAAW,CAAC,mCAAmC,EAAE,OAAO,CAAC,CAAC;QACvE,CAAC;aAAM,IACL,CAAC,mCAAmC;YACpC,OAAO,mCAAmC,KAAK,QAAQ,EACvD,CAAC;YACD,mIAAmI;YACnI,+DAA+D;YAC/D,GAAG,GAAG,qBAAqB,CAAC;YAC5B,IAAI,iBAAiB,IAAI,OAAO,iBAAiB,KAAK,QAAQ,EAAE,CAAC;gBAC/D,OAAO,GAAG,iBAA2C,CAAC;YACxD,CAAC;YACD,QAAQ,GAAG,WAAW,CAAC,IAAI,mBAAmB,EAAE,EAAE,OAAO,CAAC,CAAC;QAC7D,CAAC;aAAM,IACL,mCAAmC;YACnC,OAAO,mCAAmC,KAAK,QAAQ;YACvD,iBAAiB;YACjB,OAAO,iBAAiB,KAAK,QAAQ,EACrC,CAAC;YACD,wGAAwG;YACxG,MAAM,aAAa,GAAG,mCAAmC,CAAC;YAC1D,MAAM,QAAQ,GAAG,iBAAiB,CAAC;YAEnC,MAAM,cAAc,GAAG,4BAA4B,CAAC,qBAAqB,CAAC,CAAC;YAC3E,IAAI,cAAc,CAAC,IAAI,KAAK,mBAAmB,EAAE,CAAC;gBAChD,IAAI,MAAM,EAAE,CAAC;oBACX,MAAM,mBAAmB,GAAG,IAAI,0BAA0B,CACxD,cAAc,CAAC,WAAY,EAC3B,cAAc,CAAC,UAAU,CAC1B,CAAC;oBACF,GAAG,GAAG,eAAe,CACnB,eAAe,CAAC,cAAc,CAAC,GAAG,EAAE,kBAAkB,CAAC,aAAa,CAAC,CAAC,EACtE,kBAAkB,CAAC,QAAQ,CAAC,CAC7B,CAAC;oBAEF,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC;wBAC1B,OAAO,CAAC,YAAY,GAAG,uBAAuB,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;oBAC1E,CAAC;oBAED,QAAQ,GAAG,WAAW,CAAC,mBAAmB,EAAE,OAAO,CAAC,CAAC;gBACvD,CAAC;qBAAM,CAAC;oBACN,MAAM,IAAI,KAAK,CAAC,oEAAoE,CAAC,CAAC;gBACxF,CAAC;YACH,CAAC;iBAAM,IAAI,cAAc,CAAC,IAAI,KAAK,eAAe,EAAE,CAAC;gBACnD,GAAG;oBACD,eAAe,CACb,eAAe,CAAC,cAAc,CAAC,GAAG,EAAE,kBAAkB,CAAC,aAAa,CAAC,CAAC,EACtE,kBAAkB,CAAC,QAAQ,CAAC,CAC7B;wBACD,GAAG;wBACH,cAAc,CAAC,UAAU,CAAC;gBAC5B,QAAQ,GAAG,WAAW,CAAC,IAAI,mBAAmB,EAAE,EAAE,OAAO,CAAC,CAAC;YAC7D,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,KAAK,CACb,0FAA0F,CAC3F,CAAC;YACJ,CAAC;QACH,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,KAAK,CAAC,uEAAuE,CAAC,CAAC;QAC3F,CAAC;QACD,KAAK,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;QACrB,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,oBAAoB,CAAC,SAAS,CAAC;QAC5D,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC;IACrD,CAAC;IAED;;;;;;;OAOG;IACI,YAAY,CAAC,QAAgB;QAClC,OAAO,IAAI,eAAe,CACxB,eAAe,CACb,IAAI,CAAC,GAAG,EACR,YAAY,CAAC,UAAU,CAAC,QAAQ,EAChC,QAAQ,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,CAC7C,EACD,IAAI,CAAC,QAAQ,CACd,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA6BG;IACI,KAAK,CAAC,KAAK,CAChB,KAAa,EACb,UAAiC,EAAE;QAEnC,oBAAoB,CAAC,OAAO,CAAC,mBAAmB,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAChE,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,IAAI,KAAK,CAAC,wDAAwD,CAAC,CAAC;QAC5E,CAAC;QAED,OAAO,aAAa,CAAC,QAAQ,CAAC,uBAAuB,EAAE,OAAO,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE;;YACvF,MAAM,QAAQ,GAAG,cAAc,CAC7B,MAAM,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC;gBAC5B,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,YAAY,EAAE;oBACZ,SAAS,EAAE,KAAK;oBAChB,UAAU,EAAE,KAAK;oBACjB,kBAAkB,EAAE,oBAAoB,CAAC,OAAO,CAAC,sBAAsB,CAAC;oBACxE,mBAAmB,EAAE,oBAAoB,CAAC,OAAO,CAAC,uBAAuB,CAAC;iBAC3E;gBACD,qBAAqB,EAAE,OAAO,CAAC,UAAU;gBACzC,wBAAwB,kCACnB,OAAO,CAAC,UAAU,KACrB,MAAM,EAAE,MAAA,OAAO,CAAC,UAAU,0CAAE,aAAa,GAC1C;gBACD,OAAO,EAAE,OAAO,CAAC,mBAAmB;gBACpC,cAAc,EAAE,cAAc,CAAC,cAAc;aAC9C,CAAC,CACH,CAAC;YACF,OAAO,IAAI,iBAAiB,CAAC,QAAQ,EAAE;gBACrC,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,UAAU,EAAE,OAAO,CAAC,UAAU;gBAC9B,OAAO,EAAE,OAAO,CAAC,OAAO;aACzB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;OA0BG;IACI,KAAK,CAAC,MAAM,CACjB,IAAqB,EACrB,aAAqB,EACrB,UAAkC,EAAE;QAEpC,OAAO,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,EAAE,CAAC;QAC9C,oBAAoB,CAAC,OAAO,CAAC,mBAAmB,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAChE,OAAO,aAAa,CAAC,QAAQ,CAAC,wBAAwB,EAAE,OAAO,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE;;YACxF,OAAO,cAAc,CACnB,MAAM,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,aAAa,EAAE,IAAI,EAAE;gBACtD,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,eAAe,EAAE,OAAO,CAAC,eAAe;gBACxC,qBAAqB,EAAE,OAAO,CAAC,UAAU;gBACzC,QAAQ,EAAE,OAAO,CAAC,QAAQ;gBAC1B,wBAAwB,kCACnB,OAAO,CAAC,UAAU,KACrB,MAAM,EAAE,MAAA,OAAO,CAAC,UAAU,0CAAE,aAAa,GAC1C;gBACD,cAAc,EAAE;oBACd,gBAAgB,EAAE,OAAO,CAAC,UAAU;iBACrC;gBACD,OAAO,EAAE,OAAO,CAAC,mBAAmB;gBACpC,eAAe,EAAE,OAAO,CAAC,eAAe;gBACxC,wBAAwB,EAAE,MAAA,OAAO,CAAC,kBAAkB,0CAAE,UAAU;gBAChE,sBAAsB,EAAE,MAAA,OAAO,CAAC,kBAAkB,0CAAE,UAAU;gBAC9D,SAAS,EAAE,OAAO,CAAC,SAAS;gBAC5B,IAAI,EAAE,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC;gBAChC,cAAc,EAAE,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC;gBAC9C,cAAc,EAAE,cAAc,CAAC,cAAc;aAC9C,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;;;;OAiBG;IAEI,KAAK,CAAC,iBAAiB,CAC5B,SAAiB,EACjB,UAA6C,EAAE;QAE/C,OAAO,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,EAAE,CAAC;QAC9C,oBAAoB,CAAC,OAAO,CAAC,mBAAmB,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAChE,OAAO,aAAa,CAAC,QAAQ,CAC3B,mCAAmC,EACnC,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;;YACvB,OAAO,cAAc,CACnB,MAAM,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC,EAAE,SAAS,kCAClD,OAAO,KACV,eAAe,EAAE,OAAO,CAAC,eAAe,EACxC,qBAAqB,EAAE,OAAO,CAAC,UAAU,EACzC,wBAAwB,kCACnB,OAAO,CAAC,UAAU,KACrB,MAAM,EAAE,MAAA,OAAO,CAAC,UAAU,0CAAE,aAAa,KAE3C,8BAA8B,EAAE;oBAC9B,aAAa,EAAE,MAAA,OAAO,CAAC,gBAAgB,0CAAE,OAAO;oBAChD,qBAAqB,EAAE,MAAA,OAAO,CAAC,gBAAgB,0CAAE,eAAe;oBAChE,iBAAiB,EAAE,MAAA,OAAO,CAAC,gBAAgB,0CAAE,WAAW;oBACxD,uBAAuB,EAAE,MAAA,OAAO,CAAC,gBAAgB,0CAAE,iBAAiB;oBACpE,YAAY,EAAE,MAAA,OAAO,CAAC,gBAAgB,0CAAE,aAAa;iBACtD,EACD,OAAO,EAAE,OAAO,CAAC,mBAAmB,EACpC,uBAAuB,EAAE,yBAAyB,CAAC,OAAO,CAAC,mBAAmB,CAAC,EAC/E,IAAI,EAAE,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,EAChC,cAAc,EAAE,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,EAC9C,cAAc,EAAE,OAAO,CAAC,cAAc,EACtC,cAAc,EAAE,cAAc,CAAC,cAAc,IAC7C,CACH,CAAC;QACJ,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;;;;OAUG;IACI,KAAK,CAAC,UAAU,CACrB,OAAe,EACf,IAAqB,EACrB,aAAqB,EACrB,UAAsC,EAAE;QAExC,oBAAoB,CAAC,OAAO,CAAC,mBAAmB,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAChE,OAAO,aAAa,CAAC,QAAQ,CAAC,4BAA4B,EAAE,OAAO,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE;YAC5F,OAAO,cAAc,CACnB,MAAM,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,OAAO,EAAE,aAAa,EAAE,IAAI,EAAE;gBACnE,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,qBAAqB,EAAE,OAAO,CAAC,UAAU;gBACzC,cAAc,EAAE;oBACd,gBAAgB,EAAE,OAAO,CAAC,UAAU;iBACrC;gBACD,uBAAuB,EAAE,OAAO,CAAC,uBAAuB;gBACxD,yBAAyB,EAAE,OAAO,CAAC,yBAAyB;gBAC5D,OAAO,EAAE,OAAO,CAAC,mBAAmB;gBACpC,eAAe,EAAE,OAAO,CAAC,eAAe;gBACxC,cAAc,EAAE,cAAc,CAAC,cAAc;aAC9C,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;;;;;;;OAoBG;IACI,KAAK,CAAC,iBAAiB,CAC5B,OAAe,EACf,SAAiB,EACjB,SAAiB,CAAC,EAClB,KAAc,EACd,UAA6C,EAAE;QAE/C,oBAAoB,CAAC,OAAO,CAAC,mBAAmB,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAChE,OAAO,aAAa,CAAC,QAAQ,CAC3B,mCAAmC,EACnC,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,OAAO,cAAc,CACnB,MAAM,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC,EAAE,SAAS,EAAE;gBACnE,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,qBAAqB,EAAE,OAAO,CAAC,UAAU;gBACzC,gBAAgB,EAAE,OAAO,CAAC,gBAAgB;gBAC1C,kBAAkB,EAAE,OAAO,CAAC,kBAAkB;gBAC9C,WAAW,EAAE,MAAM,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,aAAa,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC;gBAClF,OAAO,EAAE,OAAO,CAAC,mBAAmB;gBACpC,eAAe,EAAE,OAAO,CAAC,eAAe;gBACxC,uBAAuB,EAAE,yBAAyB,CAAC,OAAO,CAAC,mBAAmB,CAAC;gBAC/E,cAAc,EAAE,cAAc,CAAC,cAAc;aAC9C,CAAC,CACH,CAAC;QACJ,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;OAWG;IACI,KAAK,CAAC,eAAe,CAC1B,MAAgB,EAChB,UAA2C,EAAE;QAE7C,OAAO,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,EAAE,CAAC;QAC9C,oBAAoB,CAAC,OAAO,CAAC,mBAAmB,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAChE,OAAO,aAAa,CAAC,QAAQ,CAC3B,iCAAiC,EACjC,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;;YACvB,OAAO,cAAc,CACnB,MAAM,IAAI,CAAC,gBAAgB,CAAC,eAAe,CACzC,EAAE,MAAM,EAAE,MAAM,EAAE,EAClB;gBACE,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,eAAe,EAAE,OAAO,CAAC,eAAe;gBACxC,qBAAqB,EAAE,OAAO,CAAC,UAAU;gBACzC,QAAQ,EAAE,OAAO,CAAC,QAAQ;gBAC1B,wBAAwB,kCACnB,OAAO,CAAC,UAAU,KACrB,MAAM,EAAE,MAAA,OAAO,CAAC,UAAU,0CAAE,aAAa,GAC1C;gBACD,OAAO,EAAE,OAAO,CAAC,mBAAmB;gBACpC,eAAe,EAAE,OAAO,CAAC,eAAe;gBACxC,wBAAwB,EAAE,MAAA,OAAO,CAAC,kBAAkB,0CAAE,UAAU;gBAChE,sBAAsB,EAAE,MAAA,OAAO,CAAC,kBAAkB,0CAAE,UAAU;gBAC9D,SAAS,EAAE,OAAO,CAAC,SAAS;gBAC5B,IAAI,EAAE,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC;gBAChC,cAAc,EAAE,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC;gBAC9C,cAAc,EAAE,cAAc,CAAC,cAAc;aAC9C,CACF,CACF,CAAC;QACJ,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;;;OASG;IACI,KAAK,CAAC,YAAY,CACvB,QAAuB,EACvB,UAAwC,EAAE;QAE1C,OAAO,aAAa,CAAC,QAAQ,CAC3B,8BAA8B,EAC9B,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;;YACvB,MAAM,GAAG,GAAG,cAAc,CAIxB,MAAM,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,QAAQ,EAAE;gBACjD,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,qBAAqB,EAAE,OAAO,CAAC,UAAU;gBACzC,wBAAwB,kCACnB,OAAO,CAAC,UAAU,KACrB,MAAM,EAAE,MAAA,OAAO,CAAC,UAAU,0CAAE,aAAa,GAC1C;gBACD,cAAc,EAAE,cAAc,CAAC,cAAc;aAC9C,CAAC,CACH,CAAC;YAEF,IAAI,CAAC,GAAG,CAAC,eAAe,EAAE,CAAC;gBACzB,GAAG,CAAC,eAAe,GAAG,EAAE,CAAC;YAC3B,CAAC;YAED,IAAI,CAAC,GAAG,CAAC,iBAAiB,EAAE,CAAC;gBAC3B,GAAG,CAAC,iBAAiB,GAAG,EAAE,CAAC;YAC7B,CAAC;YAED,OAAO,GAAG,CAAC;QACb,CAAC,CACF,CAAC;IACJ,CAAC;IAED,uBAAuB;IAEvB;;;;;;;;;;;;;;OAcG;IACI,KAAK,CAAC,UAAU,CACrB,IAAmD,EACnD,UAA0C,EAAE;QAE5C,OAAO,aAAa,CAAC,QAAQ,CAAC,4BAA4B,EAAE,OAAO,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE;YAC5F,IAAI,MAAM,EAAE,CAAC;gBACX,IAAI,MAAc,CAAC;gBACnB,IAAI,IAAI,YAAY,MAAM,EAAE,CAAC;oBAC3B,MAAM,GAAG,IAAI,CAAC;gBAChB,CAAC;qBAAM,IAAI,IAAI,YAAY,WAAW,EAAE,CAAC;oBACvC,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC7B,CAAC;qBAAM,CAAC;oBACN,IAAI,GAAG,IAAuB,CAAC;oBAC/B,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;gBACtE,CAAC;gBAED,OAAO,IAAI,CAAC,sBAAsB,CAChC,CAAC,MAAc,EAAE,IAAY,EAAU,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI,CAAC,EAC7E,MAAM,CAAC,UAAU,EACjB,cAAc,CACf,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,MAAM,WAAW,GAAG,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;gBACrC,OAAO,IAAI,CAAC,sBAAsB,CAChC,CAAC,MAAc,EAAE,IAAY,EAAQ,EAAE,CAAC,WAAW,CAAC,KAAK,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI,CAAC,EAChF,WAAW,CAAC,IAAI,EAChB,cAAc,CACf,CAAC;YACJ,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;;;;;OAkBG;IACI,KAAK,CAAC,iBAAiB,CAC5B,WAAiD,EACjD,UAA0C,EAAE;QAE5C,OAAO,aAAa,CAAC,QAAQ,CAC3B,mCAAmC,EACnC,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,MAAM,WAAW,GAAG,IAAI,IAAI,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC;YAC5C,OAAO,IAAI,CAAC,sBAAsB,CAChC,CAAC,MAAc,EAAE,IAAY,EAAQ,EAAE,CAAC,WAAW,CAAC,KAAK,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI,CAAC,EAChF,WAAW,CAAC,IAAI,EAChB,cAAc,CACf,CAAC;QACJ,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;;;OAcG;IACK,KAAK,CAAC,sBAAsB,CAClC,WAA8D,EAC9D,IAAY,EACZ,UAA0C,EAAE;;QAE5C,IAAI,SAAS,GAAG,MAAA,OAAO,CAAC,SAAS,mCAAI,CAAC,CAAC;QACvC,IAAI,SAAS,GAAG,CAAC,IAAI,SAAS,GAAG,gCAAgC,EAAE,CAAC;YAClE,MAAM,IAAI,UAAU,CAClB,wCAAwC,gCAAgC,EAAE,CAC3E,CAAC;QACJ,CAAC;QAED,MAAM,iBAAiB,GAAG,MAAA,OAAO,CAAC,iBAAiB,mCAAI,gCAAgC,CAAC;QAExF,IAAI,iBAAiB,GAAG,CAAC,IAAI,iBAAiB,GAAG,gCAAgC,EAAE,CAAC;YAClF,MAAM,IAAI,UAAU,CAClB,gDAAgD,gCAAgC,EAAE,CACnF,CAAC;QACJ,CAAC;QAED,IAAI,SAAS,KAAK,CAAC,EAAE,CAAC;YACpB,IAAI,IAAI,GAAG,gCAAgC,GAAG,qBAAqB,EAAE,CAAC;gBACpE,MAAM,IAAI,UAAU,CAAC,GAAG,IAAI,2CAA2C,CAAC,CAAC;YAC3E,CAAC;YACD,IAAI,IAAI,GAAG,iBAAiB,EAAE,CAAC;gBAC7B,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,qBAAqB,CAAC,CAAC;gBACpD,IAAI,SAAS,GAAG,iCAAiC,EAAE,CAAC;oBAClD,SAAS,GAAG,iCAAiC,CAAC;gBAChD,CAAC;YACH,CAAC;QACH,CAAC;QACD,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC;YAC7B,OAAO,CAAC,eAAe,GAAG,EAAE,CAAC;QAC/B,CAAC;QACD,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;YACxB,OAAO,CAAC,UAAU,GAAG,EAAE,CAAC;QAC1B,CAAC;QAED,OAAO,aAAa,CAAC,QAAQ,CAC3B,wCAAwC,EACxC,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,IAAI,IAAI,IAAI,iBAAiB,EAAE,CAAC;gBAC9B,OAAO,cAAc,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,IAAI,EAAE,cAAc,CAAC,CAAC,CAAC;YACvF,CAAC;YAED,MAAM,SAAS,GAAW,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC;YACjE,IAAI,SAAS,GAAG,qBAAqB,EAAE,CAAC;gBACtC,MAAM,IAAI,UAAU,CAClB,6DAA6D;oBAC3D,mCAAmC,qBAAqB,EAAE,CAC7D,CAAC;YACJ,CAAC;YAED,MAAM,SAAS,GAAa,EAAE,CAAC;YAC/B,MAAM,aAAa,GAAG,UAAU,EAAE,CAAC;YACnC,IAAI,gBAAgB,GAAW,CAAC,CAAC;YAEjC,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;YAC7C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE,CAAC;gBACnC,KAAK,CAAC,YAAY,CAAC,KAAK,IAAkB,EAAE;oBAC1C,MAAM,OAAO,GAAG,eAAe,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;oBAClD,MAAM,KAAK,GAAG,SAAS,GAAG,CAAC,CAAC;oBAC5B,MAAM,GAAG,GAAG,CAAC,KAAK,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,SAAS,CAAC;oBAC3D,MAAM,aAAa,GAAG,GAAG,GAAG,KAAK,CAAC;oBAClC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;oBACxB,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,WAAW,CAAC,KAAK,EAAE,aAAa,CAAC,EAAE,aAAa,EAAE;wBAC/E,WAAW,EAAE,OAAO,CAAC,WAAW;wBAChC,UAAU,EAAE,OAAO,CAAC,UAAU;wBAC9B,eAAe,EAAE,OAAO,CAAC,eAAe;wBACxC,cAAc,EAAE,cAAc,CAAC,cAAc;qBAC9C,CAAC,CAAC;oBACH,0FAA0F;oBAC1F,kEAAkE;oBAClE,gBAAgB,IAAI,aAAa,CAAC;oBAClC,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;wBACvB,OAAO,CAAC,UAAW,CAAC;4BAClB,WAAW,EAAE,gBAAgB;yBAC9B,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;YACD,MAAM,KAAK,CAAC,EAAE,EAAE,CAAC;YAEjB,OAAO,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;QACzD,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;OAYG;IACI,KAAK,CAAC,UAAU,CACrB,QAAgB,EAChB,UAA0C,EAAE;QAE5C,OAAO,aAAa,CAAC,QAAQ,CAAC,4BAA4B,EAAE,OAAO,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE;YAC5F,MAAM,IAAI,GAAG,CAAC,MAAM,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC;YAC3C,OAAO,IAAI,CAAC,sBAAsB,CAChC,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;gBAChB,OAAO,GAAG,EAAE,CACV,kBAAkB,CAAC,QAAQ,EAAE;oBAC3B,SAAS,EAAE,IAAI;oBACf,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC,MAAM,GAAG,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ;oBAC1C,KAAK,EAAE,MAAM;iBACd,CAAC,CAAC;YACP,CAAC,EACD,IAAI,kCAEC,OAAO,KACV,cAAc,EAAE,cAAc,CAAC,cAAc,IAEhD,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;;OAeG;IACI,KAAK,CAAC,YAAY,CACvB,MAAgB,EAChB,aAAqB,+BAA+B,EACpD,iBAAyB,CAAC,EAC1B,UAAwC,EAAE;QAE1C,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC;YAC7B,OAAO,CAAC,eAAe,GAAG,EAAE,CAAC;QAC/B,CAAC;QACD,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;YACxB,OAAO,CAAC,UAAU,GAAG,EAAE,CAAC;QAC1B,CAAC;QAED,OAAO,aAAa,CAAC,QAAQ,CAC3B,8BAA8B,EAC9B,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,IAAI,QAAQ,GAAG,CAAC,CAAC;YACjB,MAAM,aAAa,GAAG,UAAU,EAAE,CAAC;YACnC,IAAI,gBAAgB,GAAW,CAAC,CAAC;YACjC,MAAM,SAAS,GAAa,EAAE,CAAC;YAE/B,MAAM,SAAS,GAAG,IAAI,eAAe,CACnC,MAAM,EACN,UAAU,EACV,cAAc,EACd,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE;gBACrB,MAAM,OAAO,GAAG,eAAe,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;gBACzD,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBACxB,QAAQ,EAAE,CAAC;gBAEX,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE;oBAC3C,mBAAmB,EAAE,OAAO,CAAC,mBAAmB;oBAChD,UAAU,EAAE,OAAO,CAAC,UAAU;oBAC9B,eAAe,EAAE,OAAO,CAAC,eAAe;oBACxC,cAAc,EAAE,cAAc,CAAC,cAAc;iBAC9C,CAAC,CAAC;gBAEH,0FAA0F;gBAC1F,gBAAgB,IAAI,MAAM,CAAC;gBAC3B,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;oBACvB,OAAO,CAAC,UAAU,CAAC,EAAE,WAAW,EAAE,gBAAgB,EAAE,CAAC,CAAC;gBACxD,CAAC;YACH,CAAC;YACD,kFAAkF;YAClF,2EAA2E;YAC3E,iDAAiD;YACjD,qCAAqC;YACrC,IAAI,CAAC,IAAI,CAAC,CAAC,cAAc,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CACpC,CAAC;YACF,MAAM,SAAS,CAAC,EAAE,EAAE,CAAC;YAErB,OAAO,cAAc,CACnB,MAAM,IAAI,CAAC,eAAe,CAAC,SAAS,kCAC/B,OAAO,KACV,cAAc,EAAE,cAAc,CAAC,cAAc,IAC7C,CACH,CAAC;QACJ,CAAC,CACF,CAAC;IACJ,CAAC;CACF;AAoaD;;GAEG;AACH,MAAM,OAAO,cAAe,SAAQ,UAAU;IA8D5C,YACE,qBAA6B,EAC7B,mCAKgB,EAChB,iBAAmD;IACnD,mFAAmF;IACnF,gEAAgE;IAChE,OAAgC;QAEhC,kHAAkH;QAClH,wFAAwF;QACxF,IAAI,QAAsB,CAAC;QAC3B,IAAI,GAAW,CAAC;QAChB,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;QACxB,IAAI,cAAc,CAAC,mCAAmC,CAAC,EAAE,CAAC;YACxD,oCAAoC;YACpC,GAAG,GAAG,qBAAqB,CAAC;YAC5B,QAAQ,GAAG,mCAAmC,CAAC;QACjD,CAAC;aAAM,IACL,CAAC,MAAM,IAAI,mCAAmC,YAAY,0BAA0B,CAAC;YACrF,mCAAmC,YAAY,mBAAmB;YAClE,iBAAiB,CAAC,mCAAmC,CAAC,EACtD,CAAC;YACD,mIAAmI;YACnI,GAAG,GAAG,qBAAqB,CAAC;YAC5B,OAAO,GAAG,iBAA2C,CAAC;YACtD,QAAQ,GAAG,WAAW,CAAC,mCAAmC,EAAE,OAAO,CAAC,CAAC;QACvE,CAAC;aAAM,IACL,CAAC,mCAAmC;YACpC,OAAO,mCAAmC,KAAK,QAAQ,EACvD,CAAC;YACD,mIAAmI;YACnI,+DAA+D;YAC/D,GAAG,GAAG,qBAAqB,CAAC;YAC5B,QAAQ,GAAG,WAAW,CAAC,IAAI,mBAAmB,EAAE,EAAE,OAAO,CAAC,CAAC;QAC7D,CAAC;aAAM,IACL,mCAAmC;YACnC,OAAO,mCAAmC,KAAK,QAAQ;YACvD,iBAAiB;YACjB,OAAO,iBAAiB,KAAK,QAAQ,EACrC,CAAC;YACD,wGAAwG;YACxG,MAAM,aAAa,GAAG,mCAAmC,CAAC;YAC1D,MAAM,QAAQ,GAAG,iBAAiB,CAAC;YAEnC,MAAM,cAAc,GAAG,4BAA4B,CAAC,qBAAqB,CAAC,CAAC;YAC3E,IAAI,cAAc,CAAC,IAAI,KAAK,mBAAmB,EAAE,CAAC;gBAChD,IAAI,MAAM,EAAE,CAAC;oBACX,MAAM,mBAAmB,GAAG,IAAI,0BAA0B,CACxD,cAAc,CAAC,WAAY,EAC3B,cAAc,CAAC,UAAU,CAC1B,CAAC;oBACF,GAAG,GAAG,eAAe,CACnB,eAAe,CAAC,cAAc,CAAC,GAAG,EAAE,kBAAkB,CAAC,aAAa,CAAC,CAAC,EACtE,kBAAkB,CAAC,QAAQ,CAAC,CAC7B,CAAC;oBAEF,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC;wBAC1B,OAAO,CAAC,YAAY,GAAG,uBAAuB,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;oBAC1E,CAAC;oBAED,QAAQ,GAAG,WAAW,CAAC,mBAAmB,EAAE,OAAO,CAAC,CAAC;gBACvD,CAAC;qBAAM,CAAC;oBACN,MAAM,IAAI,KAAK,CAAC,oEAAoE,CAAC,CAAC;gBACxF,CAAC;YACH,CAAC;iBAAM,IAAI,cAAc,CAAC,IAAI,KAAK,eAAe,EAAE,CAAC;gBACnD,GAAG;oBACD,eAAe,CACb,eAAe,CAAC,cAAc,CAAC,GAAG,EAAE,kBAAkB,CAAC,aAAa,CAAC,CAAC,EACtE,kBAAkB,CAAC,QAAQ,CAAC,CAC7B;wBACD,GAAG;wBACH,cAAc,CAAC,UAAU,CAAC;gBAC5B,QAAQ,GAAG,WAAW,CAAC,IAAI,mBAAmB,EAAE,EAAE,OAAO,CAAC,CAAC;YAC7D,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,KAAK,CACb,0FAA0F,CAC3F,CAAC;YACJ,CAAC;QACH,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,KAAK,CAAC,uEAAuE,CAAC,CAAC;QAC3F,CAAC;QACD,KAAK,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;QACrB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC;IAC5D,CAAC;IAED;;;;;;;OAOG;IACI,YAAY,CAAC,QAAgB;QAClC,OAAO,IAAI,cAAc,CACvB,eAAe,CACb,IAAI,CAAC,GAAG,EACR,YAAY,CAAC,UAAU,CAAC,QAAQ,EAChC,QAAQ,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,CAC7C,EACD,IAAI,CAAC,QAAQ,CACd,CAAC;IACJ,CAAC;IAED;;;;;;;;OAQG;IACI,KAAK,CAAC,MAAM,CACjB,IAAY,EACZ,UAAiC,EAAE;QAEnC,OAAO,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,EAAE,CAAC;QAC9C,oBAAoB,CAAC,OAAO,CAAC,mBAAmB,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAChE,OAAO,aAAa,CAAC,QAAQ,CAAC,uBAAuB,EAAE,OAAO,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE;;YACvF,OAAO,cAAc,CACnB,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,EAAE;gBACzC,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,eAAe,EAAE,OAAO,CAAC,eAAe;gBACxC,kBAAkB,EAAE,OAAO,CAAC,kBAAkB;gBAC9C,qBAAqB,EAAE,OAAO,CAAC,UAAU;gBACzC,QAAQ,EAAE,OAAO,CAAC,QAAQ;gBAC1B,wBAAwB,kCACnB,OAAO,CAAC,UAAU,KACrB,MAAM,EAAE,MAAA,OAAO,CAAC,UAAU,0CAAE,aAAa,GAC1C;gBACD,OAAO,EAAE,OAAO,CAAC,mBAAmB;gBACpC,eAAe,EAAE,OAAO,CAAC,eAAe;gBACxC,wBAAwB,EAAE,MAAA,OAAO,CAAC,kBAAkB,0CAAE,UAAU;gBAChE,sBAAsB,EAAE,MAAA,OAAO,CAAC,kBAAkB,0CAAE,UAAU;gBAC9D,SAAS,EAAE,OAAO,CAAC,SAAS;gBAC5B,IAAI,EAAE,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC;gBAChC,cAAc,EAAE,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC;gBAC9C,cAAc,EAAE,cAAc,CAAC,cAAc;aAC9C,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;OAQG;IACI,KAAK,CAAC,iBAAiB,CAC5B,IAAY,EACZ,UAA4C,EAAE;QAE9C,OAAO,aAAa,CAAC,QAAQ,CAC3B,kCAAkC,EAClC,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;;YACvB,IAAI,CAAC;gBACH,MAAM,UAAU,GAAG,EAAE,WAAW,EAAE,OAAO,EAAE,CAAC;gBAC5C,MAAM,GAAG,GAAG,cAAc,CACxB,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,kCACjB,OAAO,KACV,UAAU,EACV,cAAc,EAAE,cAAc,CAAC,cAAc,IAC7C,CACH,CAAC;gBACF,qCACE,SAAS,EAAE,IAAI,IACZ,GAAG,KACN,SAAS,EAAE,GAAG,CAAC,SAAS,IACxB;YACJ,CAAC;YAAC,OAAO,CAAM,EAAE,CAAC;gBAChB,IAAI,CAAA,MAAA,CAAC,CAAC,OAAO,0CAAE,SAAS,MAAK,mBAAmB,EAAE,CAAC;oBACjD,qCACE,SAAS,EAAE,KAAK,IACb,MAAA,CAAC,CAAC,QAAQ,0CAAE,aAAa,KAC5B,SAAS,EAAE,CAAC,CAAC,QAAQ,IACrB;gBACJ,CAAC;gBAED,MAAM,CAAC,CAAC;YACV,CAAC;QACH,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;;;OASG;IACI,KAAK,CAAC,WAAW,CACtB,IAAqB,EACrB,MAAc,EACd,KAAa,EACb,UAAsC,EAAE;QAExC,OAAO,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,EAAE,CAAC;QAC9C,oBAAoB,CAAC,OAAO,CAAC,mBAAmB,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAChE,OAAO,aAAa,CAAC,QAAQ,CAAC,4BAA4B,EAAE,OAAO,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE;;YAC5F,OAAO,cAAc,CACnB,MAAM,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,KAAK,EAAE,IAAI,EAAE;gBAClD,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,qBAAqB,EAAE,OAAO,CAAC,UAAU;gBACzC,wBAAwB,kCACnB,OAAO,CAAC,UAAU,KACrB,MAAM,EAAE,MAAA,OAAO,CAAC,UAAU,0CAAE,aAAa,GAC1C;gBACD,cAAc,EAAE;oBACd,gBAAgB,EAAE,OAAO,CAAC,UAAU;iBACrC;gBACD,KAAK,EAAE,aAAa,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC;gBACvC,8BAA8B,EAAE,OAAO,CAAC,UAAU;gBAClD,uBAAuB,EAAE,OAAO,CAAC,uBAAuB;gBACxD,yBAAyB,EAAE,OAAO,CAAC,yBAAyB;gBAC5D,OAAO,EAAE,OAAO,CAAC,mBAAmB;gBACpC,eAAe,EAAE,OAAO,CAAC,eAAe;gBACxC,cAAc,EAAE,cAAc,CAAC,cAAc;aAC9C,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;OAUG;IACI,KAAK,CAAC,kBAAkB,CAC7B,SAAiB,EACjB,YAAoB,EACpB,UAAkB,EAClB,KAAa,EACb,UAA6C,EAAE;QAE/C,OAAO,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,EAAE,CAAC;QAC9C,OAAO,CAAC,gBAAgB,GAAG,OAAO,CAAC,gBAAgB,IAAI,EAAE,CAAC;QAC1D,oBAAoB,CAAC,OAAO,CAAC,mBAAmB,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAChE,OAAO,aAAa,CAAC,QAAQ,CAC3B,mCAAmC,EACnC,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;;YACvB,OAAO,cAAc,CACnB,MAAM,IAAI,CAAC,eAAe,CAAC,kBAAkB,CAC3C,SAAS,EACT,aAAa,CAAC,EAAE,MAAM,EAAE,YAAY,EAAE,KAAK,EAAE,CAAC,EAC9C,CAAC,EACD,aAAa,CAAC,EAAE,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE,CAAC,EAC5C;gBACE,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,gBAAgB,EAAE,OAAO,CAAC,gBAAgB;gBAC1C,kBAAkB,EAAE,OAAO,CAAC,kBAAkB;gBAC9C,qBAAqB,EAAE,OAAO,CAAC,UAAU;gBACzC,8BAA8B,EAAE,OAAO,CAAC,UAAU;gBAClD,wBAAwB,kCACnB,OAAO,CAAC,UAAU,KACrB,MAAM,EAAE,MAAA,OAAO,CAAC,UAAU,0CAAE,aAAa,GAC1C;gBACD,8BAA8B,EAAE;oBAC9B,aAAa,EAAE,MAAA,OAAO,CAAC,gBAAgB,0CAAE,OAAO;oBAChD,qBAAqB,EAAE,MAAA,OAAO,CAAC,gBAAgB,0CAAE,eAAe;oBAChE,iBAAiB,EAAE,MAAA,OAAO,CAAC,gBAAgB,0CAAE,WAAW;oBACxD,uBAAuB,EAAE,MAAA,OAAO,CAAC,gBAAgB,0CAAE,iBAAiB;iBACrE;gBACD,OAAO,EAAE,OAAO,CAAC,mBAAmB;gBACpC,eAAe,EAAE,OAAO,CAAC,eAAe;gBACxC,uBAAuB,EAAE,yBAAyB,CAAC,OAAO,CAAC,mBAAmB,CAAC;gBAC/E,cAAc,EAAE,cAAc,CAAC,cAAc;aAC9C,CACF,CACF,CAAC;QACJ,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;;OAQG;IACI,KAAK,CAAC,UAAU,CACrB,SAAiB,CAAC,EAClB,KAAc,EACd,UAAqC,EAAE;QAEvC,OAAO,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,EAAE,CAAC;QAC9C,OAAO,aAAa,CAAC,QAAQ,CAAC,2BAA2B,EAAE,OAAO,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE;;YAC3F,OAAO,cAAc,CACnB,MAAM,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,EAAE;gBACvC,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,qBAAqB,EAAE,OAAO,CAAC,UAAU;gBACzC,wBAAwB,kCACnB,OAAO,CAAC,UAAU,KACrB,MAAM,EAAE,MAAA,OAAO,CAAC,UAAU,0CAAE,aAAa,GAC1C;gBACD,KAAK,EAAE,aAAa,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC;gBACvC,8BAA8B,EAAE,OAAO,CAAC,UAAU;gBAClD,OAAO,EAAE,OAAO,CAAC,mBAAmB;gBACpC,eAAe,EAAE,OAAO,CAAC,eAAe;gBACxC,cAAc,EAAE,cAAc,CAAC,cAAc;aAC9C,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;OAQG;IACI,KAAK,CAAC,aAAa,CACxB,SAAiB,CAAC,EAClB,KAAc,EACd,UAAwC,EAAE;QAE1C,OAAO,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,EAAE,CAAC;QAC9C,OAAO,aAAa,CAAC,QAAQ,CAC3B,8BAA8B,EAC9B,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;;YACvB,MAAM,QAAQ,GAAG,cAAc,CAK7B,MAAM,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC;gBACvC,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,qBAAqB,EAAE,OAAO,CAAC,UAAU;gBACzC,wBAAwB,kCACnB,OAAO,CAAC,UAAU,KACrB,MAAM,EAAE,MAAA,OAAO,CAAC,UAAU,0CAAE,aAAa,GAC1C;gBACD,KAAK,EAAE,aAAa,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC;gBACvC,cAAc,EAAE,cAAc,CAAC,cAAc;aAC9C,CAAC,CACH,CAAC;YACF,OAAO,sBAAsB,CAAC,QAAQ,CAAC,CAAC;QAC1C,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;OAWG;IACK,KAAK,CAAC,qBAAqB,CACjC,SAAiB,CAAC,EAClB,KAAc,EACd,MAAe,EACf,UAAgD,EAAE;QAElD,OAAO,aAAa,CAAC,QAAQ,CAC3B,qCAAqC,EACrC,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;;YACvB,OAAO,cAAc,CAKnB,MAAM,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC;gBACvC,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,qBAAqB,EAAE,OAAO,CAAC,UAAU;gBACzC,wBAAwB,kCACnB,OAAO,CAAC,UAAU,KACrB,MAAM,EAAE,MAAA,OAAO,CAAC,UAAU,0CAAE,aAAa,GAC1C;gBACD,KAAK,EAAE,aAAa,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC;gBACvC,MAAM,EAAE,MAAM;gBACd,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,cAAc,EAAE,cAAc,CAAC,cAAc;aAC9C,CAAC,CACH,CAAC;QACJ,CAAC,CACF,CAAC;IACJ,CAAC;IACD;;;;;;;;;;;;;OAaG;IACY,yBAAyB;uFACtC,SAAiB,CAAC,EAClB,KAAc,EACd,MAAe,EACf,UAAgD,EAAE;YAElD,IAAI,gCAAgC,CAAC;YACrC,IAAI,CAAC,CAAC,MAAM,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;gBACrC,GAAG,CAAC;oBACF,gCAAgC,GAAG,cAAM,IAAI,CAAC,qBAAqB,CACjE,MAAM,EACN,KAAK,EACL,MAAM,EACN,OAAO,CACR,CAAA,CAAC;oBACF,MAAM,GAAG,gCAAgC,CAAC,iBAAiB,CAAC;oBAC5D,oBAAM,cAAM,gCAAgC,CAAA,CAAA,CAAC;gBAC/C,CAAC,QAAQ,MAAM,EAAE;YACnB,CAAC;QACH,CAAC;KAAA;IAED;;;;;;OAMG;IACY,kBAAkB;gFAC/B,SAAiB,CAAC,EAClB,KAAc,EACd,UAAgD,EAAE;;YAElD,IAAI,MAA0B,CAAC;;gBAC/B,KAAyC,eAAA,KAAA,cAAA,IAAI,CAAC,yBAAyB,CACrE,MAAM,EACN,KAAK,EACL,MAAM,EACN,OAAO,CACR,CAAA,IAAA,+DAAE,CAAC;oBALqC,cAKxC;oBALwC,WAKxC;oBALU,MAAM,oBAAoB,KAAA,CAAA;oBAMnC,cAAA,KAAK,CAAC,CAAC,iBAAA,cAAA,yBAAyB,CAAC,oBAAoB,CAAC,CAAA,CAAA,CAAA,CAAC;gBACzD,CAAC;;;;;;;;;QACH,CAAC;KAAA;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAsEG;IACI,cAAc,CACnB,SAAiB,CAAC,EAClB,KAAc,EACd,UAAyC,EAAE;QAE3C,OAAO,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,EAAE,CAAC;QAC9C,8CAA8C;QAC9C,MAAM,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QAC7D,OAAO;YACL;;eAEG;YACH,IAAI;gBACF,OAAO,IAAI,CAAC,IAAI,EAAE,CAAC;YACrB,CAAC;YACD;;eAEG;YACH,CAAC,MAAM,CAAC,aAAa,CAAC;gBACpB,OAAO,IAAI,CAAC;YACd,CAAC;YACD;;eAEG;YACH,MAAM,EAAE,CAAC,WAAyB,EAAE,EAAE,EAAE;gBACtC,OAAO,IAAI,CAAC,yBAAyB,CAAC,MAAM,EAAE,KAAK,EAAE,QAAQ,CAAC,iBAAiB,kBAC7E,WAAW,EAAE,QAAQ,CAAC,WAAW,IAC9B,OAAO,EACV,CAAC;YACL,CAAC;SACF,CAAC;IACJ,CAAC;IAED;;;;;;;;;OASG;IACI,KAAK,CAAC,iBAAiB,CAC5B,MAAc,EACd,KAAa,EACb,YAAoB,EACpB,UAA4C,EAAE;QAE9C,OAAO,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,EAAE,CAAC;QAC9C,OAAO,aAAa,CAAC,QAAQ,CAC3B,kCAAkC,EAClC,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;;YACvB,MAAM,MAAM,GAAG,cAAc,CAK3B,MAAM,IAAI,CAAC,eAAe,CAAC,iBAAiB,CAAC;gBAC3C,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,qBAAqB,EAAE,OAAO,CAAC,UAAU;gBACzC,wBAAwB,kCACnB,OAAO,CAAC,UAAU,KACrB,MAAM,EAAE,MAAA,OAAO,CAAC,UAAU,0CAAE,aAAa,GAC1C;gBACD,YAAY,EAAE,YAAY;gBAC1B,KAAK,EAAE,aAAa,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC;gBACvC,cAAc,EAAE,cAAc,CAAC,cAAc;aAC9C,CAAC,CACH,CAAC;YACF,OAAO,sBAAsB,CAAC,MAAM,CAAC,CAAC;QACxC,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;;OAaG;IACK,KAAK,CAAC,yBAAyB,CACrC,MAAc,EACd,KAAa,EACb,iBAAyB,EACzB,MAAe,EACf,UAAoD,EAAE;QAEtD,OAAO,aAAa,CAAC,QAAQ,CAC3B,yCAAyC,EACzC,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;;YACvB,OAAO,cAAc,CAKnB,MAAM,IAAI,CAAC,eAAe,CAAC,iBAAiB,CAAC;gBAC3C,WAAW,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,WAAW;gBACjC,qBAAqB,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,UAAU;gBAC1C,wBAAwB,kCACnB,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,UAAU,KACtB,MAAM,EAAE,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,UAAU,0CAAE,aAAa,GAC3C;gBACD,YAAY,EAAE,iBAAiB;gBAC/B,KAAK,EAAE,aAAa,CAAC;oBACnB,MAAM,EAAE,MAAM;oBACd,KAAK,EAAE,KAAK;iBACb,CAAC;gBACF,MAAM,EAAE,MAAM;gBACd,WAAW,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,WAAW;gBACjC,cAAc,EAAE,cAAc,CAAC,cAAc;aAC9C,CAAC,CACH,CAAC;QACJ,CAAC,CACF,CAAC;IACJ,CAAC;IACD;;;;;;;;;;;;;;;OAeG;IACY,6BAA6B,CAC1C,MAAc,EACd,KAAa,EACb,iBAAyB,EACzB,MAAe,EACf,OAAkD;;YAElD,IAAI,gCAAoE,CAAC;YACzE,IAAI,CAAC,CAAC,MAAM,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;gBACrC,GAAG,CAAC;oBACF,gCAAgC,GAAG,cAAM,IAAI,CAAC,yBAAyB,CACrE,MAAM,EACN,KAAK,EACL,iBAAiB,EACjB,MAAM,EACN,OAAO,CACR,CAAA,CAAC;oBACF,MAAM,GAAG,gCAAgC,CAAC,iBAAiB,CAAC;oBAC5D,oBAAM,cAAM,gCAAgC,CAAA,CAAA,CAAC;gBAC/C,CAAC,QAAQ,MAAM,EAAE;YACnB,CAAC;QACH,CAAC;KAAA;IAED;;;;;;;OAOG;IACY,sBAAsB,CACnC,MAAc,EACd,KAAa,EACb,iBAAyB,EACzB,OAAkD;;;YAElD,IAAI,MAA0B,CAAC;;gBAC/B,KAAyC,eAAA,KAAA,cAAA,IAAI,CAAC,6BAA6B,CACzE,MAAM,EACN,KAAK,EACL,iBAAiB,EACjB,MAAM,EACN,OAAO,CACR,CAAA,IAAA,+DAAE,CAAC;oBANqC,cAMxC;oBANwC,WAMxC;oBANU,MAAM,oBAAoB,KAAA,CAAA;oBAOnC,cAAA,KAAK,CAAC,CAAC,iBAAA,cAAA,yBAAyB,CAAC,oBAAoB,CAAC,CAAA,CAAA,CAAA,CAAC;gBACzD,CAAC;;;;;;;;;QACH,CAAC;KAAA;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAuEG;IACI,kBAAkB,CACvB,MAAc,EACd,KAAa,EACb,YAAoB,EACpB,UAA6C,EAAE;QAE/C,OAAO,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,EAAE,CAAC;QAE9C,8CAA8C;QAC9C,MAAM,IAAI,GAAG,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,KAAK,EAAE,YAAY,oBAC/D,OAAO,EACV,CAAC;QACH,OAAO;YACL;;eAEG;YACH,IAAI;gBACF,OAAO,IAAI,CAAC,IAAI,EAAE,CAAC;YACrB,CAAC;YACD;;eAEG;YACH,CAAC,MAAM,CAAC,aAAa,CAAC;gBACpB,OAAO,IAAI,CAAC;YACd,CAAC;YACD;;eAEG;YACH,MAAM,EAAE,CAAC,WAAyB,EAAE,EAAE,EAAE;gBACtC,OAAO,IAAI,CAAC,6BAA6B,CACvC,MAAM,EACN,KAAK,EACL,YAAY,EACZ,QAAQ,CAAC,iBAAiB,kBAExB,WAAW,EAAE,QAAQ,CAAC,WAAW,IAC9B,OAAO,EAEb,CAAC;YACJ,CAAC;SACF,CAAC;IACJ,CAAC;IAED;;;;;;;;;OASG;IACI,KAAK,CAAC,gCAAgC,CAC3C,MAAc,EACd,KAAa,EACb,eAAuB,EACvB,UAA4C,EAAE;QAE9C,OAAO,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,EAAE,CAAC;QAC9C,OAAO,aAAa,CAAC,QAAQ,CAC3B,iDAAiD,EACjD,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;;YACvB,MAAM,QAAQ,GAAG,cAAc,CAK7B,MAAM,IAAI,CAAC,eAAe,CAAC,iBAAiB,CAAC;gBAC3C,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,qBAAqB,EAAE,OAAO,CAAC,UAAU;gBACzC,wBAAwB,kCACnB,OAAO,CAAC,UAAU,KACrB,MAAM,EAAE,MAAA,OAAO,CAAC,UAAU,0CAAE,aAAa,GAC1C;gBACD,eAAe;gBACf,KAAK,EAAE,aAAa,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC;gBACvC,cAAc,EAAE,cAAc,CAAC,cAAc;aAC9C,CAAC,CACH,CAAC;YACF,OAAO,sBAAsB,CAAC,QAAQ,CAAC,CAAC;QAC1C,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;OAOG;IACI,KAAK,CAAC,MAAM,CACjB,IAAY,EACZ,UAAiC,EAAE;QAEnC,OAAO,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,EAAE,CAAC;QAC9C,OAAO,aAAa,CAAC,QAAQ,CAAC,uBAAuB,EAAE,OAAO,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE;;YACvF,OAAO,cAAc,CACnB,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,IAAI,EAAE;gBACtC,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,qBAAqB,EAAE,OAAO,CAAC,UAAU;gBACzC,wBAAwB,kCACnB,OAAO,CAAC,UAAU,KACrB,MAAM,EAAE,MAAA,OAAO,CAAC,UAAU,0CAAE,aAAa,GAC1C;gBACD,eAAe,EAAE,OAAO,CAAC,eAAe;gBACxC,cAAc,EAAE,cAAc,CAAC,cAAc;aAC9C,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;OAQG;IACI,KAAK,CAAC,oBAAoB,CAC/B,oBAA8C,EAC9C,cAAuB,EACvB,UAA+C,EAAE;QAEjD,OAAO,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,EAAE,CAAC;QAC9C,OAAO,aAAa,CAAC,QAAQ,CAC3B,qCAAqC,EACrC,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;;YACvB,OAAO,cAAc,CAInB,MAAM,IAAI,CAAC,eAAe,CAAC,oBAAoB,CAAC,oBAAoB,EAAE;gBACpE,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,kBAAkB,EAAE,cAAc;gBAClC,qBAAqB,EAAE,OAAO,CAAC,UAAU;gBACzC,wBAAwB,kCACnB,OAAO,CAAC,UAAU,KACrB,MAAM,EAAE,MAAA,OAAO,CAAC,UAAU,0CAAE,aAAa,GAC1C;gBACD,cAAc,EAAE,cAAc,CAAC,cAAc;aAC9C,CAAC,CACH,CAAC;QACJ,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;OAYG;IACI,KAAK,CAAC,oBAAoB,CAC/B,UAAkB,EAClB,UAA+C,EAAE;QAEjD,OAAO,aAAa,CAAC,QAAQ,CAC3B,qCAAqC,EACrC,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;;YACvB,OAAO,cAAc,CACnB,MAAM,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC,UAAU,EAAE;gBACrD,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,wBAAwB,kCACnB,OAAO,CAAC,UAAU,KACrB,MAAM,EAAE,MAAA,OAAO,CAAC,UAAU,0CAAE,aAAa,GAC1C;gBACD,cAAc,EAAE,cAAc,CAAC,cAAc;aAC9C,CAAC,CACH,CAAC;QACJ,CAAC,CACF,CAAC;IACJ,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { AbortSignalLike } from \"@azure/abort-controller\";\nimport type {\n  RequestBodyType as HttpRequestBody,\n  TransferProgressEvent,\n} from \"@azure/core-rest-pipeline\";\nimport { getDefaultProxySettings } from \"@azure/core-rest-pipeline\";\nimport type { TokenCredential } from \"@azure/core-auth\";\nimport { isTokenCredential } from \"@azure/core-auth\";\nimport { isNode } from \"@azure/core-util\";\nimport type { PollOperationState } from \"@azure/core-lro\";\nimport { randomUUID } from \"@azure/core-util\";\nimport type { Readable } from \"stream\";\n\nimport { BlobDownloadResponse } from \"./BlobDownloadResponse\";\nimport { BlobQueryResponse } from \"./BlobQueryResponse\";\nimport { AnonymousCredential } from \"./credentials/AnonymousCredential\";\nimport { StorageSharedKeyCredential } from \"./credentials/StorageSharedKeyCredential\";\nimport type {\n  Append<PERSON><PERSON><PERSON>,\n  Blob as StorageBlo<PERSON>,\n  BlockBlob,\n  PageBlob,\n} from \"./generated/src/operationsInterfaces\";\nimport type {\n  AppendBlobAppendBlockFromUrlHeaders,\n  AppendBlobAppendBlockHeaders,\n  AppendBlobCreateHeaders,\n  AppendBlobSealHeaders,\n  BlobAbortCopyFromURLHeaders,\n  BlobCopyFromURLHeaders,\n  BlobCreateSnapshotHeaders,\n  BlobDeleteHeaders,\n  BlobDeleteImmutabilityPolicyHeaders,\n  BlobGetAccountInfoHeaders,\n  BlobGetPropertiesResponse as BlobGetPropertiesResponseInternal,\n  BlobGetTagsResponse as BlobGetTagsResponseInternal,\n  BlobSetHttpHeadersHeaders,\n  BlobSetImmutabilityPolicyHeaders,\n  BlobSetLegalHoldHeaders,\n  BlobSetMetadataHeaders,\n  BlobSetTagsHeaders,\n  BlobSetTierHeaders,\n  BlobStartCopyFromURLHeaders,\n  BlobUndeleteHeaders,\n  BlockBlobCommitBlockListHeaders,\n  BlockBlobPutBlobFromUrlHeaders,\n  BlockBlobStageBlockFromURLHeaders,\n  BlockBlobStageBlockHeaders,\n  PageBlobClearPagesHeaders,\n  PageBlobCopyIncrementalHeaders,\n  PageBlobCreateHeaders,\n  PageBlobResizeHeaders,\n  PageBlobUpdateSequenceNumberHeaders,\n  PageBlobUploadPagesFromURLHeaders,\n  PageBlobUploadPagesHeaders,\n} from \"./generated/src\";\nimport type {\n  AppendBlobAppendBlockFromUrlResponse,\n  AppendBlobAppendBlockResponse,\n  AppendBlobCreateResponse,\n  BlobAbortCopyFromURLResponse,\n  BlobCopyFromURLResponse,\n  BlobCreateSnapshotResponse,\n  BlobDeleteResponse,\n  BlobDownloadOptionalParams,\n  BlobDownloadResponseModel,\n  BlobGetAccountInfoResponse,\n  BlobGetPropertiesResponseModel,\n  BlobGetTagsHeaders,\n  BlobSetHTTPHeadersResponse,\n  BlobSetTagsResponse,\n  BlobSetTierResponse,\n  BlobStartCopyFromURLResponse,\n  BlobTags,\n  BlobUndeleteResponse,\n  BlockBlobCommitBlockListResponse,\n  BlockBlobGetBlockListResponse,\n  BlockBlobStageBlockFromURLResponse,\n  BlockBlobStageBlockResponse,\n  BlockBlobUploadHeaders,\n  BlockBlobUploadResponse,\n  BlockListType,\n  CpkInfo,\n  DeleteSnapshotsOptionType,\n  LeaseAccessConditions,\n  PageBlobClearPagesResponse,\n  PageBlobCopyIncrementalResponse,\n  PageBlobCreateResponse,\n  PageBlobResizeResponse,\n  PageBlobUpdateSequenceNumberResponse,\n  PageBlobUploadPagesFromURLResponse,\n  PageBlobUploadPagesResponse,\n  RehydratePriority,\n  SequenceNumberActionType,\n  BlockBlobPutBlobFromUrlResponse,\n  BlobHTTPHeaders,\n  PageBlobGetPageRangesResponseModel,\n  PageRangeInfo,\n  PageBlobGetPageRangesDiffResponseModel,\n  BlobCopySourceTags,\n  BlobDownloadResponseInternal,\n  BlobDownloadHeaders,\n  BlobGetPropertiesHeaders,\n  BlobQueryResponseInternal,\n  BlobQueryHeaders,\n  BlockBlobGetBlockListHeaders,\n  BlockBlobGetBlockListResponseInternal,\n  PageBlobGetPageRangesResponseInternal,\n  PageBlobGetPageRangesHeaders,\n  PageListInternal,\n  PageBlobGetPageRangesDiffResponseInternal,\n  PageBlobGetPageRangesDiffHeaders,\n  BlobDeleteImmutabilityPolicyResponse,\n  BlobSetImmutabilityPolicyResponse,\n  BlobSetLegalHoldResponse,\n  BlobSetMetadataResponse,\n} from \"./generatedModels\";\nimport type {\n  AppendBlobRequestConditions,\n  BlobDownloadResponseParsed,\n  BlobRequestConditions,\n  BlockBlobTier,\n  Metadata,\n  ObjectReplicationPolicy,\n  PageBlobRequestConditions,\n  PremiumPageBlobTier,\n  Tags,\n  TagConditions,\n  MatchConditions,\n  ModificationConditions,\n  ModifiedAccessConditions,\n  BlobQueryArrowField,\n  BlobImmutabilityPolicy,\n  HttpAuthorization,\n  PollerLikeWithCancellation,\n} from \"./models\";\nimport { ensureCpkIfSpecified, toAccessTier } from \"./models\";\nimport type {\n  PageBlobGetPageRangesDiffResponse,\n  PageBlobGetPageRangesResponse,\n} from \"./PageBlobRangeResponse\";\nimport { rangeResponseFromModel } from \"./PageBlobRangeResponse\";\nimport type { PipelineLike, StoragePipelineOptions } from \"./Pipeline\";\nimport { newPipeline, isPipelineLike } from \"./Pipeline\";\nimport type {\n  BlobBeginCopyFromUrlPollState,\n  CopyPollerBlobClient,\n} from \"./pollers/BlobStartCopyFromUrlPoller\";\nimport { BlobBeginCopyFromUrlPoller } from \"./pollers/BlobStartCopyFromUrlPoller\";\nimport type { Range } from \"./Range\";\nimport { rangeToString } from \"./Range\";\nimport type { CommonOptions } from \"./StorageClient\";\nimport { StorageClient } from \"./StorageClient\";\nimport { Batch } from \"./utils/Batch\";\nimport { BufferScheduler } from \"../../storage-common/src\";\nimport {\n  BlobDoesNotUseCustomerSpecifiedEncryption,\n  BlobUsesCustomerSpecifiedEncryptionMsg,\n  BLOCK_BLOB_MAX_BLOCKS,\n  BLOCK_BLOB_MAX_STAGE_BLOCK_BYTES,\n  BLOCK_BLOB_MAX_UPLOAD_BLOB_BYTES,\n  DEFAULT_BLOB_DOWNLOAD_BLOCK_BYTES,\n  DEFAULT_BLOCK_BUFFER_SIZE_BYTES,\n  DEFAULT_MAX_DOWNLOAD_RETRY_REQUESTS,\n  ETagAny,\n  URLConstants,\n} from \"./utils/constants\";\nimport { tracingClient } from \"./utils/tracing\";\nimport type { WithResponse } from \"./utils/utils.common\";\nimport {\n  appendToURLPath,\n  appendToURLQuery,\n  assertResponse,\n  extractConnectionStringParts,\n  ExtractPageRangeInfoItems,\n  generateBlockID,\n  getURLParameter,\n  httpAuthorizationToString,\n  isIpEndpointStyle,\n  parseObjectReplicationRecord,\n  setURLParameter,\n  toBlobTags,\n  toBlobTagsString,\n  toQuerySerialization,\n  toTags,\n} from \"./utils/utils.common\";\nimport {\n  fsCreateReadStream,\n  fsStat,\n  readStreamToLocalFile,\n  streamToBuffer,\n} from \"./utils/utils.node\";\nimport type { SASProtocol } from \"./sas/SASQueryParameters\";\nimport type { SasIPRange } from \"./sas/SasIPRange\";\nimport {\n  generateBlobSASQueryParameters,\n  generateBlobSASQueryParametersInternal,\n} from \"./sas/BlobSASSignatureValues\";\nimport type { BlobSASPermissions } from \"./sas/BlobSASPermissions\";\nimport { BlobLeaseClient } from \"./BlobLeaseClient\";\nimport type { PagedAsyncIterableIterator, PageSettings } from \"@azure/core-paging\";\nimport type { UserDelegationKey } from \"./BlobServiceClient\";\n\n/**\n * Options to configure the {@link BlobClient.beginCopyFromURL} operation.\n */\nexport interface BlobBeginCopyFromURLOptions extends BlobStartCopyFromURLOptions {\n  /**\n   * The amount of time in milliseconds the poller should wait between\n   * calls to the service to determine the status of the Blob copy.\n   * Defaults to 15 seconds.\n   */\n  intervalInMs?: number;\n  /**\n   * Callback to receive the state of the copy progress.\n   */\n  onProgress?: (state: BlobBeginCopyFromUrlPollState) => void;\n  /**\n   * Serialized poller state that can be used to resume polling from.\n   * This may be useful when starting a copy on one process or thread\n   * and you wish to continue polling on another process or thread.\n   *\n   * To get serialized poller state, call `poller.toString()` on an existing\n   * poller.\n   */\n  resumeFrom?: string;\n}\n\n/**\n * Contains response data for the {@link BlobClient.beginCopyFromURL} operation.\n */\nexport interface BlobBeginCopyFromURLResponse extends BlobStartCopyFromURLResponse {}\n\n/**\n * Options to configure the {@link BlobClient.download} operation.\n */\nexport interface BlobDownloadOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * An opaque DateTime string value that, when present, specifies the blob snapshot to retrieve.\n   */\n  snapshot?: string;\n  /**\n   * When this is set to true and download range of blob, the service returns the MD5 hash for the range,\n   * as long as the range is less than or equal to 4 MB in size.\n   *\n   * rangeGetContentCrc64 and rangeGetContentMD5 cannot be set at same time.\n   */\n  rangeGetContentMD5?: boolean;\n  /**\n   * When this is set to true and download range of blob, the service returns the CRC64 hash for the range,\n   * as long as the range is less than or equal to 4 MB in size.\n   *\n   * rangeGetContentCrc64 and rangeGetContentMD5 cannot be set at same time.\n   */\n  rangeGetContentCrc64?: boolean;\n  /**\n   * Conditions to meet when downloading blobs.\n   */\n  conditions?: BlobRequestConditions;\n  /**\n   * Call back to receive events on the progress of download operation.\n   */\n  onProgress?: (progress: TransferProgressEvent) => void;\n\n  /**\n   * Optional. ONLY AVAILABLE IN NODE.JS.\n   *\n   * How many retries will perform when original body download stream unexpected ends.\n   * Above kind of ends will not trigger retry policy defined in a pipeline,\n   * because they doesn't emit network errors.\n   *\n   * With this option, every additional retry means an additional `FileClient.download()` request will be made\n   * from the broken point, until the requested range has been successfully downloaded or maxRetryRequests is reached.\n   *\n   * Default value is 5, please set a larger value when loading large files in poor network.\n   */\n  maxRetryRequests?: number;\n  /**\n   * Customer Provided Key Info.\n   */\n  customerProvidedKey?: CpkInfo;\n}\n\n/**\n * Options to configure the {@link BlobClient.exists} operation.\n */\nexport interface BlobExistsOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * Customer Provided Key Info.\n   */\n  customerProvidedKey?: CpkInfo;\n  /**\n   * Conditions to meet.\n   */\n  conditions?: BlobRequestConditions;\n}\n\n/**\n * Options to configure the {@link BlobClient.getProperties} operation.\n */\nexport interface BlobGetPropertiesOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * Conditions to meet when getting blob properties.\n   */\n  conditions?: BlobRequestConditions;\n  /**\n   * Customer Provided Key Info.\n   */\n  customerProvidedKey?: CpkInfo;\n}\n\n/**\n * Options to configure the {@link BlobClient.delete} operation.\n */\nexport interface BlobDeleteOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * Conditions to meet when deleting blobs.\n   */\n  conditions?: BlobRequestConditions;\n  /**\n   * Specifies options to delete blobs that have associated snapshots.\n   * - `include`: Delete the base blob and all of its snapshots.\n   * - `only`: Delete only the blob's snapshots and not the blob itself.\n   */\n  deleteSnapshots?: DeleteSnapshotsOptionType;\n  /**\n   * Customer Provided Key Info.\n   */\n  customerProvidedKey?: CpkInfo;\n}\n\n/**\n * Options to configure the {@link BlobClient.undelete} operation.\n */\nexport interface BlobUndeleteOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * Customer Provided Key Info.\n   */\n  customerProvidedKey?: CpkInfo;\n}\n\n/**\n * Options to configure the {@link BlobClient.setHTTPHeaders} operation.\n */\nexport interface BlobSetHTTPHeadersOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * Conditions to meet when setting blob HTTP headers.\n   */\n  conditions?: BlobRequestConditions;\n  /**\n   * Customer Provided Key Info.\n   */\n  customerProvidedKey?: CpkInfo;\n}\n\n/**\n * Options to configure the {@link BlobClient.setMetadata} operation.\n */\nexport interface BlobSetMetadataOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * Conditions to meet when setting blob metadata.\n   */\n  conditions?: BlobRequestConditions;\n  /**\n   * Customer Provided Key Info.\n   */\n  customerProvidedKey?: CpkInfo;\n  /**\n   * Optional. Version 2019-07-07 and later.  Specifies the name of the encryption scope to use to\n   * encrypt the data provided in the request. If not specified, encryption is performed with the\n   * default account encryption scope.  For more information, see Encryption at Rest for Azure\n   * Storage Services.\n   */\n  encryptionScope?: string;\n}\n\n/**\n * Options to configure the {@link BlobClient.setTags} operation.\n */\nexport interface BlobSetTagsOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * Conditions to meet for the blob to perform this operation.\n   */\n  conditions?: TagConditions & LeaseAccessConditions;\n}\n\n/**\n * Options to configure the {@link BlobClient.getTags} operation.\n */\nexport interface BlobGetTagsOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * Conditions to meet for the blob to perform this operation.\n   */\n  conditions?: TagConditions & LeaseAccessConditions;\n}\n\n/**\n * Contains response data for the {@link BlobClient.getTags} operation.\n */\nexport type BlobGetTagsResponse = WithResponse<\n  { tags: Tags } & BlobGetTagsHeaders,\n  BlobGetTagsHeaders,\n  BlobTags\n>;\n\n/**\n * Options to configure Blob - Acquire Lease operation.\n */\nexport interface BlobAcquireLeaseOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * Conditions to meet when acquiring the lease of a blob.\n   */\n  conditions?: ModifiedAccessConditions;\n}\n\n/**\n * Options to configure Blob - Release Lease operation.\n */\nexport interface BlobReleaseLeaseOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * Conditions to meet when releasing the lease of a blob.\n   */\n  conditions?: ModifiedAccessConditions;\n}\n\n/**\n * Options to configure Blob - Renew Lease operation.\n */\nexport interface BlobRenewLeaseOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * Conditions to meet when renewing the lease of a blob.\n   */\n  conditions?: ModifiedAccessConditions;\n}\n\n/**\n * Options to configure Blob - Change Lease operation.\n */\nexport interface BlobChangeLeaseOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * Conditions to meet when changing the lease of a blob.\n   */\n  conditions?: ModifiedAccessConditions;\n}\n\n/**\n * Options to configure Blob - Break Lease operation.\n */\nexport interface BlobBreakLeaseOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * Conditions to meet when breaking the lease of a blob.\n   */\n  conditions?: ModifiedAccessConditions;\n}\n\n/**\n * Options to configure the {@link BlobClient.createSnapshot} operation.\n */\nexport interface BlobCreateSnapshotOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * A collection of key-value string pair to associate with the snapshot.\n   */\n  metadata?: Metadata;\n  /**\n   * Conditions to meet when creating blob snapshots.\n   */\n  conditions?: BlobRequestConditions;\n  /**\n   * Customer Provided Key Info.\n   */\n  customerProvidedKey?: CpkInfo;\n  /**\n   * Optional. Version 2019-07-07 and later.  Specifies the name of the encryption scope to use to\n   * encrypt the data provided in the request. If not specified, encryption is performed with the\n   * default account encryption scope.  For more information, see Encryption at Rest for Azure\n   * Storage Services.\n   */\n  encryptionScope?: string;\n}\n\n/**\n * Options to configure the {@link BlobClient.beginCopyFromURL} operation.\n */\nexport interface BlobStartCopyFromURLOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * A collection of key-value string pair to associate with the blob that are being copied.\n   */\n  metadata?: Metadata;\n  /**\n   * Conditions to meet for the destination blob when copying from a URL to the blob.\n   */\n  conditions?: BlobRequestConditions;\n  /**\n   * Conditions to meet for the source Azure Blob/File when copying from a URL to the blob.\n   */\n  sourceConditions?: ModifiedAccessConditions;\n  /**\n   * Access tier.\n   * More Details - https://docs.microsoft.com/en-us/azure/storage/blobs/storage-blob-storage-tiers\n   */\n  tier?: BlockBlobTier | PremiumPageBlobTier | string;\n  /**\n   * Rehydrate Priority - possible values include 'High', 'Standard'.\n   * More Details - https://docs.microsoft.com/en-us/azure/storage/blobs/storage-blob-rehydration#rehydrate-an-archived-blob-to-an-online-tier\n   */\n  rehydratePriority?: RehydratePriority;\n  /**\n   * Optional. Specifies immutability policy for a blob.\n   * Note that is parameter is only applicable to a blob within a container that\n   * has version level worm enabled.\n   */\n  immutabilityPolicy?: BlobImmutabilityPolicy;\n  /**\n   * Optional. Indicates if a legal hold should be placed on the blob.\n   * Note that is parameter is only applicable to a blob within a container that\n   * has version level worm enabled.\n   */\n  legalHold?: boolean;\n  /**\n   * Blob tags.\n   */\n  tags?: Tags;\n  /**\n   * Overrides the sealed state of the destination blob. Default true.\n   */\n  sealBlob?: boolean;\n}\n\n/**\n * Options to configure the {@link BlobClient.abortCopyFromURL} operation.\n */\nexport interface BlobAbortCopyFromURLOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * If specified, contains the lease id that must be matched and lease with this id\n   * must be active in order for the operation to succeed.\n   */\n  conditions?: LeaseAccessConditions;\n}\n\n/**\n * Options to configure the {@link BlobClient.syncCopyFromURL} operation.\n */\nexport interface BlobSyncCopyFromURLOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * A collection of key-value string pair to associate with the snapshot.\n   */\n  metadata?: Metadata;\n  /**\n   * Conditions to meet for the destination blob when copying from a URL to the blob.\n   */\n  conditions?: BlobRequestConditions;\n  /**\n   * Conditions to meet for the source Azure Blob/File when copying from a URL to the blob.\n   */\n  sourceConditions?: MatchConditions & ModificationConditions;\n  /**\n   * Access tier.\n   * More Details - https://docs.microsoft.com/en-us/azure/storage/blobs/storage-blob-storage-tiers\n   */\n  tier?: BlockBlobTier | PremiumPageBlobTier | string;\n  /**\n   * Specify the md5 calculated for the range of bytes that must be read from the copy source.\n   */\n  sourceContentMD5?: Uint8Array;\n  /**\n   * Optional. Specifies immutability policy for a blob.\n   * Note that is parameter is only applicable to a blob within a container that\n   * has version level worm enabled.\n   */\n  immutabilityPolicy?: BlobImmutabilityPolicy;\n  /**\n   * Optional. Indicates if a legal hold should be placed on the blob.\n   * Note that is parameter is only applicable to a blob within a container that\n   * has version level worm enabled.\n   */\n  legalHold?: boolean;\n  /**\n   * Blob tags.\n   */\n  tags?: Tags;\n  /**\n   * Only Bearer type is supported. Credentials should be a valid OAuth access token to copy source.\n   */\n  sourceAuthorization?: HttpAuthorization;\n  /**\n   * Optional. Version 2019-07-07 and later.  Specifies the name of the encryption scope to use to encrypt the data provided in the request. If not specified, encryption is performed with the default account encryption scope.  For more information, see Encryption at Rest for Azure Storage Services.\n   */\n  encryptionScope?: string;\n  /**\n   * Optional. Default 'REPLACE'.  Indicates if source tags should be copied or replaced with the tags specified by {@link tags}.\n   */\n  copySourceTags?: BlobCopySourceTags;\n}\n\n/**\n * Options to configure the {@link BlobClient.setAccessTier} operation.\n */\nexport interface BlobSetTierOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * If specified, contains the lease id that must be matched and lease with this id\n   * must be active in order for the operation to succeed.\n   */\n  conditions?: LeaseAccessConditions & TagConditions;\n  /**\n   * Rehydrate Priority - possible values include 'High', 'Standard'.\n   * More Details - https://docs.microsoft.com/en-us/azure/storage/blobs/storage-blob-rehydration#rehydrate-an-archived-blob-to-an-online-tier\n   */\n  rehydratePriority?: RehydratePriority;\n}\n\n/**\n * Option interface for the {@link BlobClient.downloadToBuffer} operation.\n */\nexport interface BlobDownloadToBufferOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n\n  /**\n   * blockSize is the data every request trying to download.\n   * Must be greater than or equal to 0.\n   * If set to 0 or undefined, blockSize will automatically calculated according to the blob size.\n   */\n  blockSize?: number;\n\n  /**\n   * Optional. ONLY AVAILABLE IN NODE.JS.\n   *\n   * How many retries will perform when original block download stream unexpected ends.\n   * Above kind of ends will not trigger retry policy defined in a pipeline,\n   * because they doesn't emit network errors.\n   *\n   * With this option, every additional retry means an additional FileClient.download() request will be made\n   * from the broken point, until the requested block has been successfully downloaded or\n   * maxRetryRequestsPerBlock is reached.\n   *\n   * Default value is 5, please set a larger value when in poor network.\n   */\n  maxRetryRequestsPerBlock?: number;\n\n  /**\n   * Progress updater.\n   */\n  onProgress?: (progress: TransferProgressEvent) => void;\n\n  /**\n   * Access conditions headers.\n   */\n  conditions?: BlobRequestConditions;\n\n  /**\n   * Concurrency of parallel download.\n   */\n  concurrency?: number;\n  /**\n   * Customer Provided Key Info.\n   */\n  customerProvidedKey?: CpkInfo;\n}\n\n/**\n * Contains response data for the {@link BlobClient.deleteIfExists} operation.\n */\nexport interface BlobDeleteIfExistsResponse extends BlobDeleteResponse {\n  /**\n   * Indicate whether the blob is successfully deleted. Is false if the blob does not exist in the first place.\n   */\n  succeeded: boolean;\n}\n\n/**\n * Contains response data for the {@link BlobClient.getProperties} operation.\n */\nexport interface BlobGetPropertiesResponse extends BlobGetPropertiesResponseModel {\n  /**\n   * Parsed Object Replication Policy Id, Rule Id(s) and status of the source blob.\n   */\n  objectReplicationSourceProperties?: ObjectReplicationPolicy[];\n\n  /**\n   * Object Replication Policy Id of the destination blob.\n   */\n  objectReplicationDestinationPolicyId?: string;\n}\n\n/**\n * Common options of {@link BlobGenerateSasUrlOptions} and {@link ContainerGenerateSasUrlOptions}.\n */\nexport interface CommonGenerateSasUrlOptions {\n  /**\n   * The version of the service this SAS will target. If not specified, it will default to the version targeted by the\n   * library.\n   */\n  version?: string;\n\n  /**\n   * Optional. SAS protocols, HTTPS only or HTTPSandHTTP\n   */\n  protocol?: SASProtocol;\n\n  /**\n   * Optional. When the SAS will take effect.\n   */\n  startsOn?: Date;\n\n  /**\n   * Optional only when identifier is provided. The time after which the SAS will no longer work.\n   */\n  expiresOn?: Date;\n\n  /**\n   * Optional. IP ranges allowed in this SAS.\n   */\n  ipRange?: SasIPRange;\n\n  /**\n   * Optional. The name of the access policy on the container this SAS references if any.\n   *\n   * @see https://docs.microsoft.com/en-us/rest/api/storageservices/establishing-a-stored-access-policy\n   */\n  identifier?: string;\n\n  /**\n   * Optional. Encryption scope to use when sending requests authorized with this SAS URI.\n   */\n  encryptionScope?: string;\n\n  /**\n   * Optional. The cache-control header for the SAS.\n   */\n  cacheControl?: string;\n\n  /**\n   * Optional. The content-disposition header for the SAS.\n   */\n  contentDisposition?: string;\n\n  /**\n   * Optional. The content-encoding header for the SAS.\n   */\n  contentEncoding?: string;\n\n  /**\n   * Optional. The content-language header for the SAS.\n   */\n  contentLanguage?: string;\n\n  /**\n   * Optional. The content-type header for the SAS.\n   */\n  contentType?: string;\n}\n\n/**\n * Options to configure {@link BlobClient.generateSasUrl} operation.\n */\nexport interface BlobGenerateSasUrlOptions extends CommonGenerateSasUrlOptions {\n  /**\n   * Optional only when identifier is provided. Specifies the list of permissions to be associated with the SAS.\n   */\n  permissions?: BlobSASPermissions;\n}\n\n/**\n * Options for deleting immutability policy {@link BlobClient.deleteImmutabilityPolicy} operation.\n */\nexport interface BlobDeleteImmutabilityPolicyOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n}\n\n/**\n * Options for setting immutability policy {@link BlobClient.setImmutabilityPolicy} operation.\n */\nexport interface BlobSetImmutabilityPolicyOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n  modifiedAccessCondition?: ModificationConditions;\n}\n\n/**\n * Options for setting legal hold {@link BlobClient.setLegalHold} operation.\n */\nexport interface BlobSetLegalHoldOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n}\n\n/**\n * Options to configure the {@link BlobClient.getAccountInfo} operation.\n */\nexport interface BlobGetAccountInfoOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n}\n\n/**\n * A BlobClient represents a URL to an Azure Storage blob; the blob may be a block blob,\n * append blob, or page blob.\n */\nexport class BlobClient extends StorageClient {\n  /**\n   * blobContext provided by protocol layer.\n   */\n  private blobContext: StorageBlob;\n\n  private _name: string;\n  private _containerName: string;\n\n  private _versionId?: string;\n  private _snapshot?: string;\n\n  /**\n   * The name of the blob.\n   */\n  public get name(): string {\n    return this._name;\n  }\n\n  /**\n   * The name of the storage container the blob is associated with.\n   */\n  public get containerName(): string {\n    return this._containerName;\n  }\n\n  /**\n   *\n   * Creates an instance of BlobClient from connection string.\n   *\n   * @param connectionString - Account connection string or a SAS connection string of an Azure storage account.\n   *                                  [ Note - Account connection string can only be used in NODE.JS runtime. ]\n   *                                  Account connection string example -\n   *                                  `DefaultEndpointsProtocol=https;AccountName=myaccount;AccountKey=accountKey;EndpointSuffix=core.windows.net`\n   *                                  SAS connection string example -\n   *                                  `BlobEndpoint=https://myaccount.blob.core.windows.net/;QueueEndpoint=https://myaccount.queue.core.windows.net/;FileEndpoint=https://myaccount.file.core.windows.net/;TableEndpoint=https://myaccount.table.core.windows.net/;SharedAccessSignature=sasString`\n   * @param containerName - Container name.\n   * @param blobName - Blob name.\n   * @param options - Optional. Options to configure the HTTP pipeline.\n   */\n  constructor(\n    connectionString: string,\n    containerName: string,\n    blobName: string,\n    // Legacy, no fix for eslint error without breaking. Disable it for this interface.\n    /* eslint-disable-next-line @azure/azure-sdk/ts-naming-options*/\n    options?: StoragePipelineOptions,\n  );\n  /**\n   * Creates an instance of BlobClient.\n   * This method accepts an encoded URL or non-encoded URL pointing to a blob.\n   * Encoded URL string will NOT be escaped twice, only special characters in URL path will be escaped.\n   * If a blob name includes ? or %, blob name must be encoded in the URL.\n   *\n   * @param url - A Client string pointing to Azure Storage blob service, such as\n   *                     \"https://myaccount.blob.core.windows.net\". You can append a SAS\n   *                     if using AnonymousCredential, such as \"https://myaccount.blob.core.windows.net?sasString\".\n   * @param credential -  Such as AnonymousCredential, StorageSharedKeyCredential or any credential from the `@azure/identity` package to authenticate requests to the service. You can also provide an object that implements the TokenCredential interface. If not specified, AnonymousCredential is used.\n   * @param options - Optional. Options to configure the HTTP pipeline.\n   */\n  constructor(\n    url: string,\n    credential?: StorageSharedKeyCredential | AnonymousCredential | TokenCredential,\n    // Legacy, no fix for eslint error without breaking. Disable it for this interface.\n    /* eslint-disable-next-line @azure/azure-sdk/ts-naming-options*/\n    options?: StoragePipelineOptions,\n  );\n  /**\n   * Creates an instance of BlobClient.\n   * This method accepts an encoded URL or non-encoded URL pointing to a blob.\n   * Encoded URL string will NOT be escaped twice, only special characters in URL path will be escaped.\n   * If a blob name includes ? or %, blob name must be encoded in the URL.\n   *\n   * @param url - A URL string pointing to Azure Storage blob, such as\n   *                     \"https://myaccount.blob.core.windows.net/mycontainer/blob\".\n   *                     You can append a SAS if using AnonymousCredential, such as\n   *                     \"https://myaccount.blob.core.windows.net/mycontainer/blob?sasString\".\n   *                     This method accepts an encoded URL or non-encoded URL pointing to a blob.\n   *                     Encoded URL string will NOT be escaped twice, only special characters in URL path will be escaped.\n   *                     However, if a blob name includes ? or %, blob name must be encoded in the URL.\n   *                     Such as a blob named \"my?blob%\", the URL should be \"https://myaccount.blob.core.windows.net/mycontainer/my%3Fblob%25\".\n   * @param pipeline - Call newPipeline() to create a default\n   *                            pipeline, or provide a customized pipeline.\n   */\n  constructor(url: string, pipeline: PipelineLike);\n  constructor(\n    urlOrConnectionString: string,\n    credentialOrPipelineOrContainerName?:\n      | string\n      | StorageSharedKeyCredential\n      | AnonymousCredential\n      | TokenCredential\n      | PipelineLike,\n    blobNameOrOptions?: string | StoragePipelineOptions,\n    // Legacy, no fix for eslint error without breaking. Disable it for this interface.\n    /* eslint-disable-next-line @azure/azure-sdk/ts-naming-options*/\n    options?: StoragePipelineOptions,\n  ) {\n    options = options || {};\n    let pipeline: PipelineLike;\n    let url: string;\n    if (isPipelineLike(credentialOrPipelineOrContainerName)) {\n      // (url: string, pipeline: Pipeline)\n      url = urlOrConnectionString;\n      pipeline = credentialOrPipelineOrContainerName;\n    } else if (\n      (isNode && credentialOrPipelineOrContainerName instanceof StorageSharedKeyCredential) ||\n      credentialOrPipelineOrContainerName instanceof AnonymousCredential ||\n      isTokenCredential(credentialOrPipelineOrContainerName)\n    ) {\n      // (url: string, credential?: StorageSharedKeyCredential | AnonymousCredential | TokenCredential, options?: StoragePipelineOptions)\n      url = urlOrConnectionString;\n      options = blobNameOrOptions as StoragePipelineOptions;\n      pipeline = newPipeline(credentialOrPipelineOrContainerName, options);\n    } else if (\n      !credentialOrPipelineOrContainerName &&\n      typeof credentialOrPipelineOrContainerName !== \"string\"\n    ) {\n      // (url: string, credential?: StorageSharedKeyCredential | AnonymousCredential | TokenCredential, options?: StoragePipelineOptions)\n      // The second parameter is undefined. Use anonymous credential.\n      url = urlOrConnectionString;\n      if (blobNameOrOptions && typeof blobNameOrOptions !== \"string\") {\n        options = blobNameOrOptions as StoragePipelineOptions;\n      }\n      pipeline = newPipeline(new AnonymousCredential(), options);\n    } else if (\n      credentialOrPipelineOrContainerName &&\n      typeof credentialOrPipelineOrContainerName === \"string\" &&\n      blobNameOrOptions &&\n      typeof blobNameOrOptions === \"string\"\n    ) {\n      // (connectionString: string, containerName: string, blobName: string, options?: StoragePipelineOptions)\n      const containerName = credentialOrPipelineOrContainerName;\n      const blobName = blobNameOrOptions;\n\n      const extractedCreds = extractConnectionStringParts(urlOrConnectionString);\n      if (extractedCreds.kind === \"AccountConnString\") {\n        if (isNode) {\n          const sharedKeyCredential = new StorageSharedKeyCredential(\n            extractedCreds.accountName!,\n            extractedCreds.accountKey,\n          );\n          url = appendToURLPath(\n            appendToURLPath(extractedCreds.url, encodeURIComponent(containerName)),\n            encodeURIComponent(blobName),\n          );\n\n          if (!options.proxyOptions) {\n            options.proxyOptions = getDefaultProxySettings(extractedCreds.proxyUri);\n          }\n\n          pipeline = newPipeline(sharedKeyCredential, options);\n        } else {\n          throw new Error(\"Account connection string is only supported in Node.js environment\");\n        }\n      } else if (extractedCreds.kind === \"SASConnString\") {\n        url =\n          appendToURLPath(\n            appendToURLPath(extractedCreds.url, encodeURIComponent(containerName)),\n            encodeURIComponent(blobName),\n          ) +\n          \"?\" +\n          extractedCreds.accountSas;\n        pipeline = newPipeline(new AnonymousCredential(), options);\n      } else {\n        throw new Error(\n          \"Connection string must be either an Account connection string or a SAS connection string\",\n        );\n      }\n    } else {\n      throw new Error(\"Expecting non-empty strings for containerName and blobName parameters\");\n    }\n\n    super(url, pipeline);\n    ({ blobName: this._name, containerName: this._containerName } =\n      this.getBlobAndContainerNamesFromUrl());\n    this.blobContext = this.storageClientContext.blob;\n\n    this._snapshot = getURLParameter(this.url, URLConstants.Parameters.SNAPSHOT) as string;\n    this._versionId = getURLParameter(this.url, URLConstants.Parameters.VERSIONID) as string;\n  }\n\n  /**\n   * Creates a new BlobClient object identical to the source but with the specified snapshot timestamp.\n   * Provide \"\" will remove the snapshot and return a Client to the base blob.\n   *\n   * @param snapshot - The snapshot timestamp.\n   * @returns A new BlobClient object identical to the source but with the specified snapshot timestamp\n   */\n  public withSnapshot(snapshot: string): BlobClient {\n    return new BlobClient(\n      setURLParameter(\n        this.url,\n        URLConstants.Parameters.SNAPSHOT,\n        snapshot.length === 0 ? undefined : snapshot,\n      ),\n      this.pipeline,\n    );\n  }\n\n  /**\n   * Creates a new BlobClient object pointing to a version of this blob.\n   * Provide \"\" will remove the versionId and return a Client to the base blob.\n   *\n   * @param versionId - The versionId.\n   * @returns A new BlobClient object pointing to the version of this blob.\n   */\n  public withVersion(versionId: string): BlobClient {\n    return new BlobClient(\n      setURLParameter(\n        this.url,\n        URLConstants.Parameters.VERSIONID,\n        versionId.length === 0 ? undefined : versionId,\n      ),\n      this.pipeline,\n    );\n  }\n\n  /**\n   * Creates a AppendBlobClient object.\n   *\n   */\n  public getAppendBlobClient(): AppendBlobClient {\n    return new AppendBlobClient(this.url, this.pipeline);\n  }\n\n  /**\n   * Creates a BlockBlobClient object.\n   *\n   */\n  public getBlockBlobClient(): BlockBlobClient {\n    return new BlockBlobClient(this.url, this.pipeline);\n  }\n\n  /**\n   * Creates a PageBlobClient object.\n   *\n   */\n  public getPageBlobClient(): PageBlobClient {\n    return new PageBlobClient(this.url, this.pipeline);\n  }\n\n  /**\n   * Reads or downloads a blob from the system, including its metadata and properties.\n   * You can also call Get Blob to read a snapshot.\n   *\n   * * In Node.js, data returns in a Readable stream readableStreamBody\n   * * In browsers, data returns in a promise blobBody\n   *\n   * @see https://docs.microsoft.com/en-us/rest/api/storageservices/get-blob\n   *\n   * @param offset - From which position of the blob to download, greater than or equal to 0\n   * @param count - How much data to be downloaded, greater than 0. Will download to the end when undefined\n   * @param options - Optional options to Blob Download operation.\n   *\n   *\n   * Example usage (Node.js):\n   *\n   * ```js\n   * // Download and convert a blob to a string\n   * const downloadBlockBlobResponse = await blobClient.download();\n   * const downloaded = await streamToBuffer(downloadBlockBlobResponse.readableStreamBody);\n   * console.log(\"Downloaded blob content:\", downloaded.toString());\n   *\n   * async function streamToBuffer(readableStream) {\n   * return new Promise((resolve, reject) => {\n   * const chunks = [];\n   * readableStream.on(\"data\", (data) => {\n   * chunks.push(data instanceof Buffer ? data : Buffer.from(data));\n   * });\n   * readableStream.on(\"end\", () => {\n   * resolve(Buffer.concat(chunks));\n   * });\n   * readableStream.on(\"error\", reject);\n   * });\n   * }\n   * ```\n   *\n   * Example usage (browser):\n   *\n   * ```js\n   * // Download and convert a blob to a string\n   * const downloadBlockBlobResponse = await blobClient.download();\n   * const downloaded = await blobToString(await downloadBlockBlobResponse.blobBody);\n   * console.log(\n   *   \"Downloaded blob content\",\n   *   downloaded\n   * );\n   *\n   * async function blobToString(blob: Blob): Promise<string> {\n   *   const fileReader = new FileReader();\n   *   return new Promise<string>((resolve, reject) => {\n   *     fileReader.onloadend = (ev: any) => {\n   *       resolve(ev.target!.result);\n   *     };\n   *     fileReader.onerror = reject;\n   *     fileReader.readAsText(blob);\n   *   });\n   * }\n   * ```\n   */\n  public async download(\n    offset: number = 0,\n    count?: number,\n    options: BlobDownloadOptions = {},\n  ): Promise<BlobDownloadResponseParsed> {\n    options.conditions = options.conditions || {};\n    options.conditions = options.conditions || {};\n    ensureCpkIfSpecified(options.customerProvidedKey, this.isHttps);\n\n    return tracingClient.withSpan(\"BlobClient-download\", options, async (updatedOptions) => {\n      const res = assertResponse<BlobDownloadResponseInternal, BlobDownloadHeaders>(\n        await this.blobContext.download({\n          abortSignal: options.abortSignal,\n          leaseAccessConditions: options.conditions,\n          modifiedAccessConditions: {\n            ...options.conditions,\n            ifTags: options.conditions?.tagConditions,\n          },\n          requestOptions: {\n            onDownloadProgress: isNode ? undefined : options.onProgress, // for Node.js, progress is reported by RetriableReadableStream\n          },\n          range: offset === 0 && !count ? undefined : rangeToString({ offset, count }),\n          rangeGetContentMD5: options.rangeGetContentMD5,\n          rangeGetContentCRC64: options.rangeGetContentCrc64,\n          snapshot: options.snapshot,\n          cpkInfo: options.customerProvidedKey,\n          tracingOptions: updatedOptions.tracingOptions,\n        }),\n      );\n\n      const wrappedRes: BlobDownloadResponseParsed = {\n        ...res,\n        _response: res._response, // _response is made non-enumerable\n        objectReplicationDestinationPolicyId: res.objectReplicationPolicyId,\n        objectReplicationSourceProperties: parseObjectReplicationRecord(res.objectReplicationRules),\n      };\n      // Return browser response immediately\n      if (!isNode) {\n        return wrappedRes;\n      }\n\n      // We support retrying when download stream unexpected ends in Node.js runtime\n      // Following code shouldn't be bundled into browser build, however some\n      // bundlers may try to bundle following code and \"FileReadResponse.ts\".\n      // In this case, \"FileDownloadResponse.browser.ts\" will be used as a shim of \"FileDownloadResponse.ts\"\n      // The config is in package.json \"browser\" field\n      if (options.maxRetryRequests === undefined || options.maxRetryRequests < 0) {\n        // TODO: Default value or make it a required parameter?\n        options.maxRetryRequests = DEFAULT_MAX_DOWNLOAD_RETRY_REQUESTS;\n      }\n\n      if (res.contentLength === undefined) {\n        throw new RangeError(`File download response doesn't contain valid content length header`);\n      }\n\n      if (!res.etag) {\n        throw new RangeError(`File download response doesn't contain valid etag header`);\n      }\n\n      return new BlobDownloadResponse(\n        wrappedRes,\n        async (start: number): Promise<NodeJS.ReadableStream> => {\n          const updatedDownloadOptions: BlobDownloadOptionalParams = {\n            leaseAccessConditions: options.conditions,\n            modifiedAccessConditions: {\n              ifMatch: options.conditions!.ifMatch || res.etag,\n              ifModifiedSince: options.conditions!.ifModifiedSince,\n              ifNoneMatch: options.conditions!.ifNoneMatch,\n              ifUnmodifiedSince: options.conditions!.ifUnmodifiedSince,\n              ifTags: options.conditions?.tagConditions,\n            },\n            range: rangeToString({\n              count: offset + res.contentLength! - start,\n              offset: start,\n            }),\n            rangeGetContentMD5: options.rangeGetContentMD5,\n            rangeGetContentCRC64: options.rangeGetContentCrc64,\n            snapshot: options.snapshot,\n            cpkInfo: options.customerProvidedKey,\n          };\n\n          // Debug purpose only\n          // console.log(\n          //   `Read from internal stream, range: ${\n          //     updatedOptions.range\n          //   }, options: ${JSON.stringify(updatedOptions)}`\n          // );\n\n          return (\n            await this.blobContext.download({\n              abortSignal: options.abortSignal,\n              ...updatedDownloadOptions,\n            })\n          ).readableStreamBody!;\n        },\n        offset,\n        res.contentLength!,\n        {\n          maxRetryRequests: options.maxRetryRequests,\n          onProgress: options.onProgress,\n        },\n      );\n    });\n  }\n\n  /**\n   * Returns true if the Azure blob resource represented by this client exists; false otherwise.\n   *\n   * NOTE: use this function with care since an existing blob might be deleted by other clients or\n   * applications. Vice versa new blobs might be added by other clients or applications after this\n   * function completes.\n   *\n   * @param options - options to Exists operation.\n   */\n  public async exists(options: BlobExistsOptions = {}): Promise<boolean> {\n    return tracingClient.withSpan(\"BlobClient-exists\", options, async (updatedOptions) => {\n      try {\n        ensureCpkIfSpecified(options.customerProvidedKey, this.isHttps);\n        await this.getProperties({\n          abortSignal: options.abortSignal,\n          customerProvidedKey: options.customerProvidedKey,\n          conditions: options.conditions,\n          tracingOptions: updatedOptions.tracingOptions,\n        });\n        return true;\n      } catch (e: any) {\n        if (e.statusCode === 404) {\n          // Expected exception when checking blob existence\n          return false;\n        } else if (\n          e.statusCode === 409 &&\n          (e.details.errorCode === BlobUsesCustomerSpecifiedEncryptionMsg ||\n            e.details.errorCode === BlobDoesNotUseCustomerSpecifiedEncryption)\n        ) {\n          // Expected exception when checking blob existence\n          return true;\n        }\n        throw e;\n      }\n    });\n  }\n\n  /**\n   * Returns all user-defined metadata, standard HTTP properties, and system properties\n   * for the blob. It does not return the content of the blob.\n   * @see https://docs.microsoft.com/en-us/rest/api/storageservices/get-blob-properties\n   *\n   * WARNING: The `metadata` object returned in the response will have its keys in lowercase, even if\n   * they originally contained uppercase characters. This differs from the metadata keys returned by\n   * the methods of {@link ContainerClient} that list blobs using the `includeMetadata` option, which\n   * will retain their original casing.\n   *\n   * @param options - Optional options to Get Properties operation.\n   */\n  public async getProperties(\n    options: BlobGetPropertiesOptions = {},\n  ): Promise<BlobGetPropertiesResponse> {\n    options.conditions = options.conditions || {};\n    ensureCpkIfSpecified(options.customerProvidedKey, this.isHttps);\n    return tracingClient.withSpan(\"BlobClient-getProperties\", options, async (updatedOptions) => {\n      const res = assertResponse<BlobGetPropertiesResponseInternal, BlobGetPropertiesHeaders>(\n        await this.blobContext.getProperties({\n          abortSignal: options.abortSignal,\n          leaseAccessConditions: options.conditions,\n          modifiedAccessConditions: {\n            ...options.conditions,\n            ifTags: options.conditions?.tagConditions,\n          },\n          cpkInfo: options.customerProvidedKey,\n          tracingOptions: updatedOptions.tracingOptions,\n        }),\n      );\n\n      return {\n        ...res,\n        _response: res._response, // _response is made non-enumerable\n        objectReplicationDestinationPolicyId: res.objectReplicationPolicyId,\n        objectReplicationSourceProperties: parseObjectReplicationRecord(res.objectReplicationRules),\n      };\n    });\n  }\n\n  /**\n   * Marks the specified blob or snapshot for deletion. The blob is later deleted\n   * during garbage collection. Note that in order to delete a blob, you must delete\n   * all of its snapshots. You can delete both at the same time with the Delete\n   * Blob operation.\n   * @see https://docs.microsoft.com/en-us/rest/api/storageservices/delete-blob\n   *\n   * @param options - Optional options to Blob Delete operation.\n   */\n  public async delete(options: BlobDeleteOptions = {}): Promise<BlobDeleteResponse> {\n    options.conditions = options.conditions || {};\n    return tracingClient.withSpan(\"BlobClient-delete\", options, async (updatedOptions) => {\n      return assertResponse<BlobDeleteHeaders, BlobDeleteHeaders>(\n        await this.blobContext.delete({\n          abortSignal: options.abortSignal,\n          deleteSnapshots: options.deleteSnapshots,\n          leaseAccessConditions: options.conditions,\n          modifiedAccessConditions: {\n            ...options.conditions,\n            ifTags: options.conditions?.tagConditions,\n          },\n          tracingOptions: updatedOptions.tracingOptions,\n        }),\n      );\n    });\n  }\n\n  /**\n   * Marks the specified blob or snapshot for deletion if it exists. The blob is later deleted\n   * during garbage collection. Note that in order to delete a blob, you must delete\n   * all of its snapshots. You can delete both at the same time with the Delete\n   * Blob operation.\n   * @see https://docs.microsoft.com/en-us/rest/api/storageservices/delete-blob\n   *\n   * @param options - Optional options to Blob Delete operation.\n   */\n  public async deleteIfExists(\n    options: BlobDeleteOptions = {},\n  ): Promise<BlobDeleteIfExistsResponse> {\n    return tracingClient.withSpan(\"BlobClient-deleteIfExists\", options, async (updatedOptions) => {\n      try {\n        const res = assertResponse(await this.delete(updatedOptions));\n        return {\n          succeeded: true,\n          ...res,\n          _response: res._response, // _response is made non-enumerable\n        };\n      } catch (e: any) {\n        if (e.details?.errorCode === \"BlobNotFound\") {\n          return {\n            succeeded: false,\n            ...e.response?.parsedHeaders,\n            _response: e.response,\n          };\n        }\n        throw e;\n      }\n    });\n  }\n\n  /**\n   * Restores the contents and metadata of soft deleted blob and any associated\n   * soft deleted snapshots. Undelete Blob is supported only on version 2017-07-29\n   * or later.\n   * @see https://docs.microsoft.com/en-us/rest/api/storageservices/undelete-blob\n   *\n   * @param options - Optional options to Blob Undelete operation.\n   */\n  public async undelete(options: BlobUndeleteOptions = {}): Promise<BlobUndeleteResponse> {\n    return tracingClient.withSpan(\"BlobClient-undelete\", options, async (updatedOptions) => {\n      return assertResponse<BlobUndeleteHeaders, BlobUndeleteHeaders>(\n        await this.blobContext.undelete({\n          abortSignal: options.abortSignal,\n          tracingOptions: updatedOptions.tracingOptions,\n        }),\n      );\n    });\n  }\n\n  /**\n   * Sets system properties on the blob.\n   *\n   * If no value provided, or no value provided for the specified blob HTTP headers,\n   * these blob HTTP headers without a value will be cleared.\n   * @see https://docs.microsoft.com/en-us/rest/api/storageservices/set-blob-properties\n   *\n   * @param blobHTTPHeaders - If no value provided, or no value provided for\n   *                                                   the specified blob HTTP headers, these blob HTTP\n   *                                                   headers without a value will be cleared.\n   *                                                   A common header to set is `blobContentType`\n   *                                                   enabling the browser to provide functionality\n   *                                                   based on file type.\n   * @param options - Optional options to Blob Set HTTP Headers operation.\n   */\n  public async setHTTPHeaders(\n    blobHTTPHeaders?: BlobHTTPHeaders,\n    options: BlobSetHTTPHeadersOptions = {},\n  ): Promise<BlobSetHTTPHeadersResponse> {\n    options.conditions = options.conditions || {};\n    ensureCpkIfSpecified(options.customerProvidedKey, this.isHttps);\n    return tracingClient.withSpan(\"BlobClient-setHTTPHeaders\", options, async (updatedOptions) => {\n      return assertResponse<BlobSetHttpHeadersHeaders, BlobSetHttpHeadersHeaders>(\n        await this.blobContext.setHttpHeaders({\n          abortSignal: options.abortSignal,\n          blobHttpHeaders: blobHTTPHeaders,\n          leaseAccessConditions: options.conditions,\n          modifiedAccessConditions: {\n            ...options.conditions,\n            ifTags: options.conditions?.tagConditions,\n          },\n          // cpkInfo: options.customerProvidedKey, // CPK is not included in Swagger, should change this back when this issue is fixed in Swagger.\n          tracingOptions: updatedOptions.tracingOptions,\n        }),\n      );\n    });\n  }\n\n  /**\n   * Sets user-defined metadata for the specified blob as one or more name-value pairs.\n   *\n   * If no option provided, or no metadata defined in the parameter, the blob\n   * metadata will be removed.\n   * @see https://docs.microsoft.com/en-us/rest/api/storageservices/set-blob-metadata\n   *\n   * @param metadata - Replace existing metadata with this value.\n   *                               If no value provided the existing metadata will be removed.\n   * @param options - Optional options to Set Metadata operation.\n   */\n  public async setMetadata(\n    metadata?: Metadata,\n    options: BlobSetMetadataOptions = {},\n  ): Promise<BlobSetMetadataResponse> {\n    options.conditions = options.conditions || {};\n    ensureCpkIfSpecified(options.customerProvidedKey, this.isHttps);\n    return tracingClient.withSpan(\"BlobClient-setMetadata\", options, async (updatedOptions) => {\n      return assertResponse<BlobSetMetadataHeaders, BlobSetMetadataHeaders>(\n        await this.blobContext.setMetadata({\n          abortSignal: options.abortSignal,\n          leaseAccessConditions: options.conditions,\n          metadata,\n          modifiedAccessConditions: {\n            ...options.conditions,\n            ifTags: options.conditions?.tagConditions,\n          },\n          cpkInfo: options.customerProvidedKey,\n          encryptionScope: options.encryptionScope,\n          tracingOptions: updatedOptions.tracingOptions,\n        }),\n      );\n    });\n  }\n\n  /**\n   * Sets tags on the underlying blob.\n   * A blob can have up to 10 tags. Tag keys must be between 1 and 128 characters.  Tag values must be between 0 and 256 characters.\n   * Valid tag key and value characters include lower and upper case letters, digits (0-9),\n   * space (' '), plus ('+'), minus ('-'), period ('.'), foward slash ('/'), colon (':'), equals ('='), and underscore ('_').\n   *\n   * @param tags -\n   * @param options -\n   */\n  public async setTags(tags: Tags, options: BlobSetTagsOptions = {}): Promise<BlobSetTagsResponse> {\n    return tracingClient.withSpan(\"BlobClient-setTags\", options, async (updatedOptions) => {\n      return assertResponse<BlobSetTagsHeaders, BlobSetTagsHeaders>(\n        await this.blobContext.setTags({\n          abortSignal: options.abortSignal,\n          leaseAccessConditions: options.conditions,\n          modifiedAccessConditions: {\n            ...options.conditions,\n            ifTags: options.conditions?.tagConditions,\n          },\n          tracingOptions: updatedOptions.tracingOptions,\n          tags: toBlobTags(tags),\n        }),\n      );\n    });\n  }\n\n  /**\n   * Gets the tags associated with the underlying blob.\n   *\n   * @param options -\n   */\n  public async getTags(options: BlobGetTagsOptions = {}): Promise<BlobGetTagsResponse> {\n    return tracingClient.withSpan(\"BlobClient-getTags\", options, async (updatedOptions) => {\n      const response = assertResponse<BlobGetTagsResponseInternal, BlobGetTagsHeaders, BlobTags>(\n        await this.blobContext.getTags({\n          abortSignal: options.abortSignal,\n          leaseAccessConditions: options.conditions,\n          modifiedAccessConditions: {\n            ...options.conditions,\n            ifTags: options.conditions?.tagConditions,\n          },\n          tracingOptions: updatedOptions.tracingOptions,\n        }),\n      );\n      const wrappedResponse: BlobGetTagsResponse = {\n        ...response,\n        _response: response._response, // _response is made non-enumerable\n        tags: toTags({ blobTagSet: response.blobTagSet }) || {},\n      };\n      return wrappedResponse;\n    });\n  }\n\n  /**\n   * Get a {@link BlobLeaseClient} that manages leases on the blob.\n   *\n   * @param proposeLeaseId - Initial proposed lease Id.\n   * @returns A new BlobLeaseClient object for managing leases on the blob.\n   */\n  public getBlobLeaseClient(proposeLeaseId?: string): BlobLeaseClient {\n    return new BlobLeaseClient(this, proposeLeaseId);\n  }\n\n  /**\n   * Creates a read-only snapshot of a blob.\n   * @see https://docs.microsoft.com/en-us/rest/api/storageservices/snapshot-blob\n   *\n   * @param options - Optional options to the Blob Create Snapshot operation.\n   */\n  public async createSnapshot(\n    options: BlobCreateSnapshotOptions = {},\n  ): Promise<BlobCreateSnapshotResponse> {\n    options.conditions = options.conditions || {};\n    ensureCpkIfSpecified(options.customerProvidedKey, this.isHttps);\n    return tracingClient.withSpan(\"BlobClient-createSnapshot\", options, async (updatedOptions) => {\n      return assertResponse<BlobCreateSnapshotHeaders, BlobCreateSnapshotHeaders>(\n        await this.blobContext.createSnapshot({\n          abortSignal: options.abortSignal,\n          leaseAccessConditions: options.conditions,\n          metadata: options.metadata,\n          modifiedAccessConditions: {\n            ...options.conditions,\n            ifTags: options.conditions?.tagConditions,\n          },\n          cpkInfo: options.customerProvidedKey,\n          encryptionScope: options.encryptionScope,\n          tracingOptions: updatedOptions.tracingOptions,\n        }),\n      );\n    });\n  }\n\n  /**\n   * Asynchronously copies a blob to a destination within the storage account.\n   * This method returns a long running operation poller that allows you to wait\n   * indefinitely until the copy is completed.\n   * You can also cancel a copy before it is completed by calling `cancelOperation` on the poller.\n   * Note that the onProgress callback will not be invoked if the operation completes in the first\n   * request, and attempting to cancel a completed copy will result in an error being thrown.\n   *\n   * In version 2012-02-12 and later, the source for a Copy Blob operation can be\n   * a committed blob in any Azure storage account.\n   * Beginning with version 2015-02-21, the source for a Copy Blob operation can be\n   * an Azure file in any Azure storage account.\n   * Only storage accounts created on or after June 7th, 2012 allow the Copy Blob\n   * operation to copy from another storage account.\n   * @see https://docs.microsoft.com/en-us/rest/api/storageservices/copy-blob\n   *\n   * Example using automatic polling:\n   *\n   * ```js\n   * const copyPoller = await blobClient.beginCopyFromURL('url');\n   * const result = await copyPoller.pollUntilDone();\n   * ```\n   *\n   * Example using manual polling:\n   *\n   * ```js\n   * const copyPoller = await blobClient.beginCopyFromURL('url');\n   * while (!poller.isDone()) {\n   *    await poller.poll();\n   * }\n   * const result = copyPoller.getResult();\n   * ```\n   *\n   * Example using progress updates:\n   *\n   * ```js\n   * const copyPoller = await blobClient.beginCopyFromURL('url', {\n   *   onProgress(state) {\n   *     console.log(`Progress: ${state.copyProgress}`);\n   *   }\n   * });\n   * const result = await copyPoller.pollUntilDone();\n   * ```\n   *\n   * Example using a changing polling interval (default 15 seconds):\n   *\n   * ```js\n   * const copyPoller = await blobClient.beginCopyFromURL('url', {\n   *   intervalInMs: 1000 // poll blob every 1 second for copy progress\n   * });\n   * const result = await copyPoller.pollUntilDone();\n   * ```\n   *\n   * Example using copy cancellation:\n   *\n   * ```js\n   * const copyPoller = await blobClient.beginCopyFromURL('url');\n   * // cancel operation after starting it.\n   * try {\n   *   await copyPoller.cancelOperation();\n   *   // calls to get the result now throw PollerCancelledError\n   *   await copyPoller.getResult();\n   * } catch (err) {\n   *   if (err.name === 'PollerCancelledError') {\n   *     console.log('The copy was cancelled.');\n   *   }\n   * }\n   * ```\n   *\n   * @param copySource - url to the source Azure Blob/File.\n   * @param options - Optional options to the Blob Start Copy From URL operation.\n   */\n  public async beginCopyFromURL(\n    copySource: string,\n    options: BlobBeginCopyFromURLOptions = {},\n  ): Promise<\n    PollerLikeWithCancellation<\n      PollOperationState<BlobBeginCopyFromURLResponse>,\n      BlobBeginCopyFromURLResponse\n    >\n  > {\n    const client: CopyPollerBlobClient = {\n      abortCopyFromURL: (...args) => this.abortCopyFromURL(...args),\n      getProperties: (...args) => this.getProperties(...args),\n      startCopyFromURL: (...args) => this.startCopyFromURL(...args),\n    };\n    const poller = new BlobBeginCopyFromUrlPoller({\n      blobClient: client,\n      copySource,\n      intervalInMs: options.intervalInMs,\n      onProgress: options.onProgress,\n      resumeFrom: options.resumeFrom,\n      startCopyFromURLOptions: options,\n    });\n\n    // Trigger the startCopyFromURL call by calling poll.\n    // Any errors from this method should be surfaced to the user.\n    await poller.poll();\n\n    return poller;\n  }\n\n  /**\n   * Aborts a pending asynchronous Copy Blob operation, and leaves a destination blob with zero\n   * length and full metadata. Version 2012-02-12 and newer.\n   * @see https://docs.microsoft.com/en-us/rest/api/storageservices/abort-copy-blob\n   *\n   * @param copyId - Id of the Copy From URL operation.\n   * @param options - Optional options to the Blob Abort Copy From URL operation.\n   */\n  public async abortCopyFromURL(\n    copyId: string,\n    options: BlobAbortCopyFromURLOptions = {},\n  ): Promise<BlobAbortCopyFromURLResponse> {\n    return tracingClient.withSpan(\n      \"BlobClient-abortCopyFromURL\",\n      options,\n      async (updatedOptions) => {\n        return assertResponse<BlobAbortCopyFromURLHeaders, BlobAbortCopyFromURLHeaders>(\n          await this.blobContext.abortCopyFromURL(copyId, {\n            abortSignal: options.abortSignal,\n            leaseAccessConditions: options.conditions,\n            tracingOptions: updatedOptions.tracingOptions,\n          }),\n        );\n      },\n    );\n  }\n\n  /**\n   * The synchronous Copy From URL operation copies a blob or an internet resource to a new blob. It will not\n   * return a response until the copy is complete.\n   * @see https://docs.microsoft.com/en-us/rest/api/storageservices/copy-blob-from-url\n   *\n   * @param copySource - The source URL to copy from, Shared Access Signature(SAS) maybe needed for authentication\n   * @param options -\n   */\n  public async syncCopyFromURL(\n    copySource: string,\n    options: BlobSyncCopyFromURLOptions = {},\n  ): Promise<BlobCopyFromURLResponse> {\n    options.conditions = options.conditions || {};\n    options.sourceConditions = options.sourceConditions || {};\n    return tracingClient.withSpan(\"BlobClient-syncCopyFromURL\", options, async (updatedOptions) => {\n      return assertResponse<BlobCopyFromURLHeaders, BlobCopyFromURLHeaders>(\n        await this.blobContext.copyFromURL(copySource, {\n          abortSignal: options.abortSignal,\n          metadata: options.metadata,\n          leaseAccessConditions: options.conditions,\n          modifiedAccessConditions: {\n            ...options.conditions,\n            ifTags: options.conditions?.tagConditions,\n          },\n          sourceModifiedAccessConditions: {\n            sourceIfMatch: options.sourceConditions?.ifMatch,\n            sourceIfModifiedSince: options.sourceConditions?.ifModifiedSince,\n            sourceIfNoneMatch: options.sourceConditions?.ifNoneMatch,\n            sourceIfUnmodifiedSince: options.sourceConditions?.ifUnmodifiedSince,\n          },\n          sourceContentMD5: options.sourceContentMD5,\n          copySourceAuthorization: httpAuthorizationToString(options.sourceAuthorization),\n          tier: toAccessTier(options.tier),\n          blobTagsString: toBlobTagsString(options.tags),\n          immutabilityPolicyExpiry: options.immutabilityPolicy?.expiriesOn,\n          immutabilityPolicyMode: options.immutabilityPolicy?.policyMode,\n          legalHold: options.legalHold,\n          encryptionScope: options.encryptionScope,\n          copySourceTags: options.copySourceTags,\n          tracingOptions: updatedOptions.tracingOptions,\n        }),\n      );\n    });\n  }\n\n  /**\n   * Sets the tier on a blob. The operation is allowed on a page blob in a premium\n   * storage account and on a block blob in a blob storage account (locally redundant\n   * storage only). A premium page blob's tier determines the allowed size, IOPS,\n   * and bandwidth of the blob. A block blob's tier determines Hot/Cool/Archive\n   * storage type. This operation does not update the blob's ETag.\n   * @see https://docs.microsoft.com/en-us/rest/api/storageservices/set-blob-tier\n   *\n   * @param tier - The tier to be set on the blob. Valid values are Hot, Cool, or Archive.\n   * @param options - Optional options to the Blob Set Tier operation.\n   */\n  public async setAccessTier(\n    tier: BlockBlobTier | PremiumPageBlobTier | string,\n    options: BlobSetTierOptions = {},\n  ): Promise<BlobSetTierResponse> {\n    return tracingClient.withSpan(\"BlobClient-setAccessTier\", options, async (updatedOptions) => {\n      return assertResponse<BlobSetTierHeaders, BlobSetTierHeaders>(\n        await this.blobContext.setTier(toAccessTier(tier)!, {\n          abortSignal: options.abortSignal,\n          leaseAccessConditions: options.conditions,\n          modifiedAccessConditions: {\n            ...options.conditions,\n            ifTags: options.conditions?.tagConditions,\n          },\n          rehydratePriority: options.rehydratePriority,\n          tracingOptions: updatedOptions.tracingOptions,\n        }),\n      );\n    });\n  }\n\n  // High level function\n\n  /**\n   * ONLY AVAILABLE IN NODE.JS RUNTIME.\n   *\n   * Downloads an Azure Blob in parallel to a buffer.\n   * Offset and count are optional, downloads the entire blob if they are not provided.\n   *\n   * Warning: Buffers can only support files up to about one gigabyte on 32-bit systems or about two\n   * gigabytes on 64-bit systems due to limitations of Node.js/V8. For blobs larger than this size,\n   * consider {@link downloadToFile}.\n   *\n   * @param offset - From which position of the block blob to download(in bytes)\n   * @param count - How much data(in bytes) to be downloaded. Will download to the end when passing undefined\n   * @param options - BlobDownloadToBufferOptions\n   */\n  public async downloadToBuffer(\n    offset?: number,\n    count?: number,\n    options?: BlobDownloadToBufferOptions,\n  ): Promise<Buffer>;\n\n  /**\n   * ONLY AVAILABLE IN NODE.JS RUNTIME.\n   *\n   * Downloads an Azure Blob in parallel to a buffer.\n   * Offset and count are optional, downloads the entire blob if they are not provided.\n   *\n   * Warning: Buffers can only support files up to about one gigabyte on 32-bit systems or about two\n   * gigabytes on 64-bit systems due to limitations of Node.js/V8. For blobs larger than this size,\n   * consider {@link downloadToFile}.\n   *\n   * @param buffer - Buffer to be fill, must have length larger than count\n   * @param offset - From which position of the block blob to download(in bytes)\n   * @param count - How much data(in bytes) to be downloaded. Will download to the end when passing undefined\n   * @param options - BlobDownloadToBufferOptions\n   */\n  public async downloadToBuffer(\n    buffer: Buffer,\n    offset?: number,\n    count?: number,\n    options?: BlobDownloadToBufferOptions,\n  ): Promise<Buffer>;\n\n  public async downloadToBuffer(\n    param1?: Buffer | number,\n    param2?: number,\n    param3?: BlobDownloadToBufferOptions | number,\n    param4: BlobDownloadToBufferOptions = {},\n  ): Promise<Buffer | undefined> {\n    let buffer: Buffer | undefined;\n    let offset = 0;\n    let count = 0;\n    let options = param4;\n    if (param1 instanceof Buffer) {\n      buffer = param1;\n      offset = param2 || 0;\n      count = typeof param3 === \"number\" ? param3 : 0;\n    } else {\n      offset = typeof param1 === \"number\" ? param1 : 0;\n      count = typeof param2 === \"number\" ? param2 : 0;\n      options = (param3 as BlobDownloadToBufferOptions) || {};\n    }\n\n    let blockSize = options.blockSize ?? 0;\n\n    if (blockSize < 0) {\n      throw new RangeError(\"blockSize option must be >= 0\");\n    }\n    if (blockSize === 0) {\n      blockSize = DEFAULT_BLOB_DOWNLOAD_BLOCK_BYTES;\n    }\n\n    if (offset < 0) {\n      throw new RangeError(\"offset option must be >= 0\");\n    }\n\n    if (count && count <= 0) {\n      throw new RangeError(\"count option must be greater than 0\");\n    }\n\n    if (!options.conditions) {\n      options.conditions = {};\n    }\n\n    return tracingClient.withSpan(\n      \"BlobClient-downloadToBuffer\",\n      options,\n      async (updatedOptions) => {\n        // Customer doesn't specify length, get it\n        if (!count) {\n          const response = await this.getProperties({\n            ...options,\n            tracingOptions: updatedOptions.tracingOptions,\n          });\n          count = response.contentLength! - offset;\n          if (count < 0) {\n            throw new RangeError(\n              `offset ${offset} shouldn't be larger than blob size ${response.contentLength!}`,\n            );\n          }\n        }\n\n        // Allocate the buffer of size = count if the buffer is not provided\n        if (!buffer) {\n          try {\n            buffer = Buffer.alloc(count);\n          } catch (error: any) {\n            throw new Error(\n              `Unable to allocate the buffer of size: ${count}(in bytes). Please try passing your own buffer to the \"downloadToBuffer\" method or try using other methods like \"download\" or \"downloadToFile\".\\t ${error.message}`,\n            );\n          }\n        }\n\n        if (buffer.length < count) {\n          throw new RangeError(\n            `The buffer's size should be equal to or larger than the request count of bytes: ${count}`,\n          );\n        }\n\n        let transferProgress: number = 0;\n        const batch = new Batch(options.concurrency);\n        for (let off = offset; off < offset + count; off = off + blockSize) {\n          batch.addOperation(async () => {\n            // Exclusive chunk end position\n            let chunkEnd = offset + count!;\n            if (off + blockSize < chunkEnd) {\n              chunkEnd = off + blockSize;\n            }\n            const response = await this.download(off, chunkEnd - off, {\n              abortSignal: options.abortSignal,\n              conditions: options.conditions,\n              maxRetryRequests: options.maxRetryRequestsPerBlock,\n              customerProvidedKey: options.customerProvidedKey,\n              tracingOptions: updatedOptions.tracingOptions,\n            });\n            const stream = response.readableStreamBody!;\n            await streamToBuffer(stream, buffer!, off - offset, chunkEnd - offset);\n            // Update progress after block is downloaded, in case of block trying\n            // Could provide finer grained progress updating inside HTTP requests,\n            // only if convenience layer download try is enabled\n            transferProgress += chunkEnd - off;\n            if (options.onProgress) {\n              options.onProgress({ loadedBytes: transferProgress });\n            }\n          });\n        }\n        await batch.do();\n        return buffer;\n      },\n    );\n  }\n\n  /**\n   * ONLY AVAILABLE IN NODE.JS RUNTIME.\n   *\n   * Downloads an Azure Blob to a local file.\n   * Fails if the the given file path already exits.\n   * Offset and count are optional, pass 0 and undefined respectively to download the entire blob.\n   *\n   * @param filePath -\n   * @param offset - From which position of the block blob to download.\n   * @param count - How much data to be downloaded. Will download to the end when passing undefined.\n   * @param options - Options to Blob download options.\n   * @returns The response data for blob download operation,\n   *                                                 but with readableStreamBody set to undefined since its\n   *                                                 content is already read and written into a local file\n   *                                                 at the specified path.\n   */\n  public async downloadToFile(\n    filePath: string,\n    offset: number = 0,\n    count?: number,\n    options: BlobDownloadOptions = {},\n  ): Promise<BlobDownloadResponseParsed> {\n    return tracingClient.withSpan(\"BlobClient-downloadToFile\", options, async (updatedOptions) => {\n      const response = await this.download(offset, count, {\n        ...options,\n        tracingOptions: updatedOptions.tracingOptions,\n      });\n      if (response.readableStreamBody) {\n        await readStreamToLocalFile(response.readableStreamBody, filePath);\n      }\n\n      // The stream is no longer accessible so setting it to undefined.\n      (response as any).blobDownloadStream = undefined;\n      return response;\n    });\n  }\n\n  private getBlobAndContainerNamesFromUrl(): { blobName: string; containerName: string } {\n    let containerName;\n    let blobName;\n    try {\n      //  URL may look like the following\n      // \"https://myaccount.blob.core.windows.net/mycontainer/blob?sasString\";\n      // \"https://myaccount.blob.core.windows.net/mycontainer/blob\";\n      // \"https://myaccount.blob.core.windows.net/mycontainer/blob/a.txt?sasString\";\n      // \"https://myaccount.blob.core.windows.net/mycontainer/blob/a.txt\";\n      // IPv4/IPv6 address hosts, Endpoints - `http://127.0.0.1:10000/devstoreaccount1/containername/blob`\n      // http://localhost:10001/devstoreaccount1/containername/blob\n\n      const parsedUrl = new URL(this.url);\n\n      if (parsedUrl.host.split(\".\")[1] === \"blob\") {\n        // \"https://myaccount.blob.core.windows.net/containername/blob\".\n        // .getPath() -> /containername/blob\n        const pathComponents = parsedUrl.pathname.match(\"/([^/]*)(/(.*))?\");\n        containerName = pathComponents![1];\n        blobName = pathComponents![3];\n      } else if (isIpEndpointStyle(parsedUrl)) {\n        // IPv4/IPv6 address hosts... Example - http://**********:10001/devstoreaccount1/containername/blob\n        // Single word domain without a [dot] in the endpoint... Example - http://localhost:10001/devstoreaccount1/containername/blob\n        // .getPath() -> /devstoreaccount1/containername/blob\n        const pathComponents = parsedUrl.pathname.match(\"/([^/]*)/([^/]*)(/(.*))?\");\n        containerName = pathComponents![2];\n        blobName = pathComponents![4];\n      } else {\n        // \"https://customdomain.com/containername/blob\".\n        // .getPath() -> /containername/blob\n        const pathComponents = parsedUrl.pathname.match(\"/([^/]*)(/(.*))?\");\n        containerName = pathComponents![1];\n        blobName = pathComponents![3];\n      }\n\n      // decode the encoded blobName, containerName - to get all the special characters that might be present in them\n      containerName = decodeURIComponent(containerName);\n      blobName = decodeURIComponent(blobName);\n\n      // Azure Storage Server will replace \"\\\" with \"/\" in the blob names\n      //   doing the same in the SDK side so that the user doesn't have to replace \"\\\" instances in the blobName\n      blobName = blobName.replace(/\\\\/g, \"/\");\n\n      if (!containerName) {\n        throw new Error(\"Provided containerName is invalid.\");\n      }\n\n      return { blobName, containerName };\n    } catch (error: any) {\n      throw new Error(\"Unable to extract blobName and containerName with provided information.\");\n    }\n  }\n\n  /**\n   * Asynchronously copies a blob to a destination within the storage account.\n   * In version 2012-02-12 and later, the source for a Copy Blob operation can be\n   * a committed blob in any Azure storage account.\n   * Beginning with version 2015-02-21, the source for a Copy Blob operation can be\n   * an Azure file in any Azure storage account.\n   * Only storage accounts created on or after June 7th, 2012 allow the Copy Blob\n   * operation to copy from another storage account.\n   * @see https://docs.microsoft.com/en-us/rest/api/storageservices/copy-blob\n   *\n   * @param copySource - url to the source Azure Blob/File.\n   * @param options - Optional options to the Blob Start Copy From URL operation.\n   */\n  private async startCopyFromURL(\n    copySource: string,\n    options: BlobStartCopyFromURLOptions = {},\n  ): Promise<BlobStartCopyFromURLResponse> {\n    return tracingClient.withSpan(\n      \"BlobClient-startCopyFromURL\",\n      options,\n      async (updatedOptions) => {\n        options.conditions = options.conditions || {};\n        options.sourceConditions = options.sourceConditions || {};\n        return assertResponse<BlobStartCopyFromURLHeaders, BlobStartCopyFromURLHeaders>(\n          await this.blobContext.startCopyFromURL(copySource, {\n            abortSignal: options.abortSignal,\n            leaseAccessConditions: options.conditions,\n            metadata: options.metadata,\n            modifiedAccessConditions: {\n              ...options.conditions,\n              ifTags: options.conditions?.tagConditions,\n            },\n            sourceModifiedAccessConditions: {\n              sourceIfMatch: options.sourceConditions.ifMatch,\n              sourceIfModifiedSince: options.sourceConditions.ifModifiedSince,\n              sourceIfNoneMatch: options.sourceConditions.ifNoneMatch,\n              sourceIfUnmodifiedSince: options.sourceConditions.ifUnmodifiedSince,\n              sourceIfTags: options.sourceConditions.tagConditions,\n            },\n            immutabilityPolicyExpiry: options.immutabilityPolicy?.expiriesOn,\n            immutabilityPolicyMode: options.immutabilityPolicy?.policyMode,\n            legalHold: options.legalHold,\n            rehydratePriority: options.rehydratePriority,\n            tier: toAccessTier(options.tier),\n            blobTagsString: toBlobTagsString(options.tags),\n            sealBlob: options.sealBlob,\n            tracingOptions: updatedOptions.tracingOptions,\n          }),\n        );\n      },\n    );\n  }\n\n  /**\n   * Only available for BlobClient constructed with a shared key credential.\n   *\n   * Generates a Blob Service Shared Access Signature (SAS) URI based on the client properties\n   * and parameters passed in. The SAS is signed by the shared key credential of the client.\n   *\n   * @see https://docs.microsoft.com/en-us/rest/api/storageservices/constructing-a-service-sas\n   *\n   * @param options - Optional parameters.\n   * @returns The SAS URI consisting of the URI to the resource represented by this client, followed by the generated SAS token.\n   */\n  public generateSasUrl(options: BlobGenerateSasUrlOptions): Promise<string> {\n    return new Promise((resolve) => {\n      if (!(this.credential instanceof StorageSharedKeyCredential)) {\n        throw new RangeError(\n          \"Can only generate the SAS when the client is initialized with a shared key credential\",\n        );\n      }\n\n      const sas = generateBlobSASQueryParameters(\n        {\n          containerName: this._containerName,\n          blobName: this._name,\n          snapshotTime: this._snapshot,\n          versionId: this._versionId,\n          ...options,\n        },\n        this.credential,\n      ).toString();\n\n      resolve(appendToURLQuery(this.url, sas));\n    });\n  }\n\n  /**\n   * Only available for BlobClient constructed with a shared key credential.\n   *\n   * Generates string to sign for a Blob Service Shared Access Signature (SAS) URI based on\n   * the client properties and parameters passed in. The SAS is signed by the shared key credential of the client.\n   *\n   * @see https://docs.microsoft.com/en-us/rest/api/storageservices/constructing-a-service-sas\n   *\n   * @param options - Optional parameters.\n   * @returns The SAS URI consisting of the URI to the resource represented by this client, followed by the generated SAS token.\n   */\n  /* eslint-disable-next-line @azure/azure-sdk/ts-naming-options*/\n  public generateSasStringToSign(options: BlobGenerateSasUrlOptions): string {\n    if (!(this.credential instanceof StorageSharedKeyCredential)) {\n      throw new RangeError(\n        \"Can only generate the SAS when the client is initialized with a shared key credential\",\n      );\n    }\n\n    return generateBlobSASQueryParametersInternal(\n      {\n        containerName: this._containerName,\n        blobName: this._name,\n        snapshotTime: this._snapshot,\n        versionId: this._versionId,\n        ...options,\n      },\n      this.credential,\n    ).stringToSign;\n  }\n\n  /**\n   *\n   * Generates a Blob Service Shared Access Signature (SAS) URI based on\n   * the client properties and parameters passed in. The SAS is signed by the input user delegation key.\n   *\n   * @see https://docs.microsoft.com/en-us/rest/api/storageservices/constructing-a-service-sas\n   *\n   * @param options - Optional parameters.\n   * @param userDelegationKey -  Return value of `blobServiceClient.getUserDelegationKey()`\n   * @returns The SAS URI consisting of the URI to the resource represented by this client, followed by the generated SAS token.\n   */\n  public generateUserDelegationSasUrl(\n    options: BlobGenerateSasUrlOptions,\n    userDelegationKey: UserDelegationKey,\n  ): Promise<string> {\n    return new Promise((resolve) => {\n      const sas = generateBlobSASQueryParameters(\n        {\n          containerName: this._containerName,\n          blobName: this._name,\n          snapshotTime: this._snapshot,\n          versionId: this._versionId,\n          ...options,\n        },\n        userDelegationKey,\n        this.accountName,\n      ).toString();\n\n      resolve(appendToURLQuery(this.url, sas));\n    });\n  }\n\n  /**\n   * Only available for BlobClient constructed with a shared key credential.\n   *\n   * Generates string to sign for a Blob Service Shared Access Signature (SAS) URI based on\n   * the client properties and parameters passed in. The SAS is signed by the input user delegation key.\n   *\n   * @see https://docs.microsoft.com/en-us/rest/api/storageservices/constructing-a-service-sas\n   *\n   * @param options - Optional parameters.\n   * @param userDelegationKey -  Return value of `blobServiceClient.getUserDelegationKey()`\n   * @returns The SAS URI consisting of the URI to the resource represented by this client, followed by the generated SAS token.\n   */\n\n  public generateUserDelegationSasStringToSign(\n    options: BlobGenerateSasUrlOptions,\n    userDelegationKey: UserDelegationKey,\n  ): string {\n    return generateBlobSASQueryParametersInternal(\n      {\n        containerName: this._containerName,\n        blobName: this._name,\n        snapshotTime: this._snapshot,\n        versionId: this._versionId,\n        ...options,\n      },\n      userDelegationKey,\n      this.accountName,\n    ).stringToSign;\n  }\n\n  /**\n   * Delete the immutablility policy on the blob.\n   *\n   * @param options - Optional options to delete immutability policy on the blob.\n   */\n  public async deleteImmutabilityPolicy(\n    options: BlobDeleteImmutabilityPolicyOptions = {},\n  ): Promise<BlobDeleteImmutabilityPolicyResponse> {\n    return tracingClient.withSpan(\n      \"BlobClient-deleteImmutabilityPolicy\",\n      options,\n      async (updatedOptions) => {\n        return assertResponse<\n          BlobDeleteImmutabilityPolicyHeaders,\n          BlobDeleteImmutabilityPolicyHeaders\n        >(\n          await this.blobContext.deleteImmutabilityPolicy({\n            tracingOptions: updatedOptions.tracingOptions,\n          }),\n        );\n      },\n    );\n  }\n\n  /**\n   * Set immutability policy on the blob.\n   *\n   * @param options - Optional options to set immutability policy on the blob.\n   */\n  public async setImmutabilityPolicy(\n    immutabilityPolicy: BlobImmutabilityPolicy,\n    options: BlobSetImmutabilityPolicyOptions = {},\n  ): Promise<BlobSetImmutabilityPolicyResponse> {\n    return tracingClient.withSpan(\n      \"BlobClient-setImmutabilityPolicy\",\n      options,\n      async (updatedOptions) => {\n        return assertResponse<BlobSetImmutabilityPolicyHeaders, BlobSetImmutabilityPolicyHeaders>(\n          await this.blobContext.setImmutabilityPolicy({\n            immutabilityPolicyExpiry: immutabilityPolicy.expiriesOn,\n            immutabilityPolicyMode: immutabilityPolicy.policyMode,\n            tracingOptions: updatedOptions.tracingOptions,\n          }),\n        );\n      },\n    );\n  }\n\n  /**\n   * Set legal hold on the blob.\n   *\n   * @param options - Optional options to set legal hold on the blob.\n   */\n  public async setLegalHold(\n    legalHoldEnabled: boolean,\n    options: BlobSetLegalHoldOptions = {},\n  ): Promise<BlobSetLegalHoldResponse> {\n    return tracingClient.withSpan(\"BlobClient-setLegalHold\", options, async (updatedOptions) => {\n      return assertResponse<BlobSetLegalHoldHeaders, BlobSetLegalHoldHeaders>(\n        await this.blobContext.setLegalHold(legalHoldEnabled, {\n          tracingOptions: updatedOptions.tracingOptions,\n        }),\n      );\n    });\n  }\n\n  /**\n   * The Get Account Information operation returns the sku name and account kind\n   * for the specified account.\n   * The Get Account Information operation is available on service versions beginning\n   * with version 2018-03-28.\n   * @see https://docs.microsoft.com/en-us/rest/api/storageservices/get-account-information\n   *\n   * @param options - Options to the Service Get Account Info operation.\n   * @returns Response data for the Service Get Account Info operation.\n   */\n  public async getAccountInfo(\n    options: BlobGetAccountInfoOptions = {},\n  ): Promise<BlobGetAccountInfoResponse> {\n    return tracingClient.withSpan(\"BlobClient-getAccountInfo\", options, async (updatedOptions) => {\n      return assertResponse<BlobGetAccountInfoHeaders, BlobGetAccountInfoHeaders>(\n        await this.blobContext.getAccountInfo({\n          abortSignal: options.abortSignal,\n          tracingOptions: updatedOptions.tracingOptions,\n        }),\n      );\n    });\n  }\n}\n\n/**\n * Options to configure {@link AppendBlobClient.create} operation.\n */\nexport interface AppendBlobCreateOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n\n  /**\n   * Conditions to meet when creating append blobs.\n   */\n  conditions?: BlobRequestConditions;\n  /**\n   * HTTP headers to set when creating append blobs. A common header\n   * to set is `blobContentType`, enabling the browser to provide functionality\n   * based on file type.\n   *\n   */\n  blobHTTPHeaders?: BlobHTTPHeaders;\n  /**\n   * A collection of key-value string pair to associate with the blob when creating append blobs.\n   */\n  metadata?: Metadata;\n  /**\n   * Customer Provided Key Info.\n   */\n  customerProvidedKey?: CpkInfo;\n  /**\n   * Optional. Version 2019-07-07 and later.  Specifies the name of the encryption scope to use to\n   * encrypt the data provided in the request. If not specified, encryption is performed with the\n   * default account encryption scope.  For more information, see Encryption at Rest for Azure\n   * Storage Services.\n   */\n  encryptionScope?: string;\n  /**\n   * Optional. Specifies immutability policy for a blob.\n   * Note that is parameter is only applicable to a blob within a container that\n   * has version level worm enabled.\n   */\n  immutabilityPolicy?: BlobImmutabilityPolicy;\n  /**\n   * Optional. Indicates if a legal hold should be placed on the blob.\n   * Note that is parameter is only applicable to a blob within a container that\n   * has version level worm enabled.\n   */\n  legalHold?: boolean;\n  /**\n   * Blob tags.\n   */\n  tags?: Tags;\n}\n\n/**\n * Options to configure {@link AppendBlobClient.createIfNotExists} operation.\n */\nexport interface AppendBlobCreateIfNotExistsOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * HTTP headers to set when creating append blobs. A common header to set is\n   * `blobContentType`, enabling the browser to provide functionality\n   * based on file type.\n   *\n   */\n  blobHTTPHeaders?: BlobHTTPHeaders;\n  /**\n   * A collection of key-value string pair to associate with the blob when creating append blobs.\n   */\n  metadata?: Metadata;\n  /**\n   * Customer Provided Key Info.\n   */\n  customerProvidedKey?: CpkInfo;\n  /**\n   * Optional. Version 2019-07-07 and later.  Specifies the name of the encryption scope to use to\n   * encrypt the data provided in the request. If not specified, encryption is performed with the\n   * default account encryption scope.  For more information, see Encryption at Rest for Azure\n   * Storage Services.\n   */\n  encryptionScope?: string;\n  /**\n   * Optional. Specifies immutability policy for a blob.\n   * Note that is parameter is only applicable to a blob within a container that\n   * has version level worm enabled.\n   */\n  immutabilityPolicy?: BlobImmutabilityPolicy;\n  /**\n   * Optional. Indicates if a legal hold should be placed on the blob.\n   * Note that is parameter is only applicable to a blob within a container that\n   * has version level worm enabled.\n   */\n  legalHold?: boolean;\n}\n\n/**\n * Options to configure {@link AppendBlobClient.seal} operation.\n */\nexport interface AppendBlobSealOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * Conditions to meet.\n   */\n  conditions?: AppendBlobRequestConditions;\n}\n\n/**\n * Options to configure the {@link AppendBlobClient.appendBlock} operation.\n */\nexport interface AppendBlobAppendBlockOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * Conditions to meet when appending append blob blocks.\n   */\n  conditions?: AppendBlobRequestConditions;\n  /**\n   * Callback to receive events on the progress of append block operation.\n   */\n  onProgress?: (progress: TransferProgressEvent) => void;\n  /**\n   * An MD5 hash of the block content. This hash is used to verify the integrity of the block during transport.\n   * When this is specified, the storage service compares the hash of the content that has arrived with this value.\n   *\n   * transactionalContentMD5 and transactionalContentCrc64 cannot be set at same time.\n   */\n  transactionalContentMD5?: Uint8Array;\n  /**\n   * A CRC64 hash of the append block content. This hash is used to verify the integrity of the append block during transport.\n   * When this is specified, the storage service compares the hash of the content that has arrived with this value.\n   *\n   * transactionalContentMD5 and transactionalContentCrc64 cannot be set at same time.\n   */\n  transactionalContentCrc64?: Uint8Array;\n  /**\n   * Customer Provided Key Info.\n   */\n  customerProvidedKey?: CpkInfo;\n  /**\n   * Optional. Version 2019-07-07 and later.  Specifies the name of the encryption scope to use to\n   * encrypt the data provided in the request. If not specified, encryption is performed with the\n   * default account encryption scope.  For more information, see Encryption at Rest for Azure\n   * Storage Services.\n   */\n  encryptionScope?: string;\n}\n\n/**\n * Options to configure the {@link AppendBlobClient.appendBlockFromURL} operation.\n */\nexport interface AppendBlobAppendBlockFromURLOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * Conditions to meet when appending append blob blocks.\n   */\n  conditions?: AppendBlobRequestConditions;\n  /**\n   * Conditions to meet for the source Azure Blob/File when copying from a URL to the blob.\n   */\n  sourceConditions?: MatchConditions & ModificationConditions;\n  /**\n   * An MD5 hash of the append block content from the URI.\n   * This hash is used to verify the integrity of the append block during transport of the data from the URI.\n   * When this is specified, the storage service compares the hash of the content that has arrived from the copy-source with this value.\n   *\n   * sourceContentMD5 and sourceContentCrc64 cannot be set at same time.\n   */\n  sourceContentMD5?: Uint8Array;\n  /**\n   * A CRC64 hash of the append block content from the URI.\n   * This hash is used to verify the integrity of the append block during transport of the data from the URI.\n   * When this is specified, the storage service compares the hash of the content that has arrived from the copy-source with this value.\n   *\n   * sourceContentMD5 and sourceContentCrc64 cannot be set at same time.\n   */\n  sourceContentCrc64?: Uint8Array;\n  /**\n   * Customer Provided Key Info.\n   */\n  customerProvidedKey?: CpkInfo;\n  /**\n   * Optional. Version 2019-07-07 and later.  Specifies the name of the encryption scope to use to\n   * encrypt the data provided in the request. If not specified, encryption is performed with the\n   * default account encryption scope.  For more information, see Encryption at Rest for Azure\n   * Storage Services.\n   */\n  encryptionScope?: string;\n  /**\n   * Only Bearer type is supported. Credentials should be a valid OAuth access token to copy source.\n   */\n  sourceAuthorization?: HttpAuthorization;\n}\n\n/**\n * Contains response data for the {@link appendBlobClient.createIfNotExists} operation.\n */\nexport interface AppendBlobCreateIfNotExistsResponse extends AppendBlobCreateResponse {\n  /**\n   * Indicate whether the blob is successfully created. Is false when the blob is not changed as it already exists.\n   */\n  succeeded: boolean;\n}\n\n/**\n * AppendBlobClient defines a set of operations applicable to append blobs.\n */\nexport class AppendBlobClient extends BlobClient {\n  /**\n   * appendBlobsContext provided by protocol layer.\n   */\n  private appendBlobContext: AppendBlob;\n\n  /**\n   *\n   * Creates an instance of AppendBlobClient.\n   *\n   * @param connectionString - Account connection string or a SAS connection string of an Azure storage account.\n   *                                  [ Note - Account connection string can only be used in NODE.JS runtime. ]\n   *                                  Account connection string example -\n   *                                  `DefaultEndpointsProtocol=https;AccountName=myaccount;AccountKey=accountKey;EndpointSuffix=core.windows.net`\n   *                                  SAS connection string example -\n   *                                  `BlobEndpoint=https://myaccount.blob.core.windows.net/;QueueEndpoint=https://myaccount.queue.core.windows.net/;FileEndpoint=https://myaccount.file.core.windows.net/;TableEndpoint=https://myaccount.table.core.windows.net/;SharedAccessSignature=sasString`\n   * @param containerName - Container name.\n   * @param blobName - Blob name.\n   * @param options - Optional. Options to configure the HTTP pipeline.\n   */\n  constructor(\n    connectionString: string,\n    containerName: string,\n    blobName: string,\n    // Legacy, no fix for eslint error without breaking. Disable it for this interface.\n    /* eslint-disable-next-line @azure/azure-sdk/ts-naming-options*/\n    options?: StoragePipelineOptions,\n  );\n  /**\n   * Creates an instance of AppendBlobClient.\n   * This method accepts an encoded URL or non-encoded URL pointing to an append blob.\n   * Encoded URL string will NOT be escaped twice, only special characters in URL path will be escaped.\n   * If a blob name includes ? or %, blob name must be encoded in the URL.\n   *\n   * @param url - A URL string pointing to Azure Storage append blob, such as\n   *                     \"https://myaccount.blob.core.windows.net/mycontainer/appendblob\". You can\n   *                     append a SAS if using AnonymousCredential, such as\n   *                     \"https://myaccount.blob.core.windows.net/mycontainer/appendblob?sasString\".\n   *                     This method accepts an encoded URL or non-encoded URL pointing to a blob.\n   *                     Encoded URL string will NOT be escaped twice, only special characters in URL path will be escaped.\n   *                     However, if a blob name includes ? or %, blob name must be encoded in the URL.\n   *                     Such as a blob named \"my?blob%\", the URL should be \"https://myaccount.blob.core.windows.net/mycontainer/my%3Fblob%25\".\n   * @param credential -  Such as AnonymousCredential, StorageSharedKeyCredential or any credential from the `@azure/identity` package to authenticate requests to the service. You can also provide an object that implements the TokenCredential interface. If not specified, AnonymousCredential is used.\n   * @param options - Optional. Options to configure the HTTP pipeline.\n   */\n  constructor(\n    url: string,\n    credential: StorageSharedKeyCredential | AnonymousCredential | TokenCredential,\n    // Legacy, no fix for eslint error without breaking. Disable it for this interface.\n    /* eslint-disable-next-line @azure/azure-sdk/ts-naming-options*/\n    options?: StoragePipelineOptions,\n  );\n  /**\n   * Creates an instance of AppendBlobClient.\n   * This method accepts an encoded URL or non-encoded URL pointing to an append blob.\n   * Encoded URL string will NOT be escaped twice, only special characters in URL path will be escaped.\n   * If a blob name includes ? or %, blob name must be encoded in the URL.\n   *\n   * @param url - A URL string pointing to Azure Storage append blob, such as\n   *                     \"https://myaccount.blob.core.windows.net/mycontainer/appendblob\". You can\n   *                     append a SAS if using AnonymousCredential, such as\n   *                     \"https://myaccount.blob.core.windows.net/mycontainer/appendblob?sasString\".\n   *                     This method accepts an encoded URL or non-encoded URL pointing to a blob.\n   *                     Encoded URL string will NOT be escaped twice, only special characters in URL path will be escaped.\n   *                     However, if a blob name includes ? or %, blob name must be encoded in the URL.\n   *                     Such as a blob named \"my?blob%\", the URL should be \"https://myaccount.blob.core.windows.net/mycontainer/my%3Fblob%25\".\n   * @param pipeline - Call newPipeline() to create a default\n   *                            pipeline, or provide a customized pipeline.\n   */\n  constructor(url: string, pipeline: PipelineLike);\n  constructor(\n    urlOrConnectionString: string,\n    credentialOrPipelineOrContainerName:\n      | string\n      | StorageSharedKeyCredential\n      | AnonymousCredential\n      | TokenCredential\n      | PipelineLike,\n    blobNameOrOptions?: string | StoragePipelineOptions,\n    // Legacy, no fix for eslint error without breaking. Disable it for this interface.\n    /* eslint-disable-next-line @azure/azure-sdk/ts-naming-options*/\n    options?: StoragePipelineOptions,\n  ) {\n    // In TypeScript we cannot simply pass all parameters to super() like below so have to duplicate the code instead.\n    //   super(s, credentialOrPipelineOrContainerNameOrOptions, blobNameOrOptions, options);\n    let pipeline: PipelineLike;\n    let url: string;\n    options = options || {};\n    if (isPipelineLike(credentialOrPipelineOrContainerName)) {\n      // (url: string, pipeline: Pipeline)\n      url = urlOrConnectionString;\n      pipeline = credentialOrPipelineOrContainerName;\n    } else if (\n      (isNode && credentialOrPipelineOrContainerName instanceof StorageSharedKeyCredential) ||\n      credentialOrPipelineOrContainerName instanceof AnonymousCredential ||\n      isTokenCredential(credentialOrPipelineOrContainerName)\n    ) {\n      // (url: string, credential?: StorageSharedKeyCredential | AnonymousCredential | TokenCredential, options?: StoragePipelineOptions)      url = urlOrConnectionString;\n      url = urlOrConnectionString;\n      options = blobNameOrOptions as StoragePipelineOptions;\n      pipeline = newPipeline(credentialOrPipelineOrContainerName, options);\n    } else if (\n      !credentialOrPipelineOrContainerName &&\n      typeof credentialOrPipelineOrContainerName !== \"string\"\n    ) {\n      // (url: string, credential?: StorageSharedKeyCredential | AnonymousCredential | TokenCredential, options?: StoragePipelineOptions)\n      url = urlOrConnectionString;\n      // The second parameter is undefined. Use anonymous credential.\n      pipeline = newPipeline(new AnonymousCredential(), options);\n    } else if (\n      credentialOrPipelineOrContainerName &&\n      typeof credentialOrPipelineOrContainerName === \"string\" &&\n      blobNameOrOptions &&\n      typeof blobNameOrOptions === \"string\"\n    ) {\n      // (connectionString: string, containerName: string, blobName: string, options?: StoragePipelineOptions)\n      const containerName = credentialOrPipelineOrContainerName;\n      const blobName = blobNameOrOptions;\n\n      const extractedCreds = extractConnectionStringParts(urlOrConnectionString);\n      if (extractedCreds.kind === \"AccountConnString\") {\n        if (isNode) {\n          const sharedKeyCredential = new StorageSharedKeyCredential(\n            extractedCreds.accountName!,\n            extractedCreds.accountKey,\n          );\n          url = appendToURLPath(\n            appendToURLPath(extractedCreds.url, encodeURIComponent(containerName)),\n            encodeURIComponent(blobName),\n          );\n\n          if (!options.proxyOptions) {\n            options.proxyOptions = getDefaultProxySettings(extractedCreds.proxyUri);\n          }\n\n          pipeline = newPipeline(sharedKeyCredential, options);\n        } else {\n          throw new Error(\"Account connection string is only supported in Node.js environment\");\n        }\n      } else if (extractedCreds.kind === \"SASConnString\") {\n        url =\n          appendToURLPath(\n            appendToURLPath(extractedCreds.url, encodeURIComponent(containerName)),\n            encodeURIComponent(blobName),\n          ) +\n          \"?\" +\n          extractedCreds.accountSas;\n        pipeline = newPipeline(new AnonymousCredential(), options);\n      } else {\n        throw new Error(\n          \"Connection string must be either an Account connection string or a SAS connection string\",\n        );\n      }\n    } else {\n      throw new Error(\"Expecting non-empty strings for containerName and blobName parameters\");\n    }\n    super(url, pipeline);\n    this.appendBlobContext = this.storageClientContext.appendBlob;\n  }\n\n  /**\n   * Creates a new AppendBlobClient object identical to the source but with the\n   * specified snapshot timestamp.\n   * Provide \"\" will remove the snapshot and return a Client to the base blob.\n   *\n   * @param snapshot - The snapshot timestamp.\n   * @returns A new AppendBlobClient object identical to the source but with the specified snapshot timestamp.\n   */\n  public withSnapshot(snapshot: string): AppendBlobClient {\n    return new AppendBlobClient(\n      setURLParameter(\n        this.url,\n        URLConstants.Parameters.SNAPSHOT,\n        snapshot.length === 0 ? undefined : snapshot,\n      ),\n      this.pipeline,\n    );\n  }\n\n  /**\n   * Creates a 0-length append blob. Call AppendBlock to append data to an append blob.\n   * @see https://docs.microsoft.com/rest/api/storageservices/put-blob\n   *\n   * @param options - Options to the Append Block Create operation.\n   *\n   *\n   * Example usage:\n   *\n   * ```js\n   * const appendBlobClient = containerClient.getAppendBlobClient(\"<blob name>\");\n   * await appendBlobClient.create();\n   * ```\n   */\n  public async create(options: AppendBlobCreateOptions = {}): Promise<AppendBlobCreateResponse> {\n    options.conditions = options.conditions || {};\n    ensureCpkIfSpecified(options.customerProvidedKey, this.isHttps);\n    return tracingClient.withSpan(\"AppendBlobClient-create\", options, async (updatedOptions) => {\n      return assertResponse<AppendBlobCreateHeaders, AppendBlobCreateHeaders>(\n        await this.appendBlobContext.create(0, {\n          abortSignal: options.abortSignal,\n          blobHttpHeaders: options.blobHTTPHeaders,\n          leaseAccessConditions: options.conditions,\n          metadata: options.metadata,\n          modifiedAccessConditions: {\n            ...options.conditions,\n            ifTags: options.conditions?.tagConditions,\n          },\n          cpkInfo: options.customerProvidedKey,\n          encryptionScope: options.encryptionScope,\n          immutabilityPolicyExpiry: options.immutabilityPolicy?.expiriesOn,\n          immutabilityPolicyMode: options.immutabilityPolicy?.policyMode,\n          legalHold: options.legalHold,\n          blobTagsString: toBlobTagsString(options.tags),\n          tracingOptions: updatedOptions.tracingOptions,\n        }),\n      );\n    });\n  }\n\n  /**\n   * Creates a 0-length append blob. Call AppendBlock to append data to an append blob.\n   * If the blob with the same name already exists, the content of the existing blob will remain unchanged.\n   * @see https://docs.microsoft.com/rest/api/storageservices/put-blob\n   *\n   * @param options -\n   */\n  public async createIfNotExists(\n    options: AppendBlobCreateIfNotExistsOptions = {},\n  ): Promise<AppendBlobCreateIfNotExistsResponse> {\n    const conditions = { ifNoneMatch: ETagAny };\n    return tracingClient.withSpan(\n      \"AppendBlobClient-createIfNotExists\",\n      options,\n      async (updatedOptions) => {\n        try {\n          const res = assertResponse(\n            await this.create({\n              ...updatedOptions,\n              conditions,\n            }),\n          );\n          return {\n            succeeded: true,\n            ...res,\n            _response: res._response, // _response is made non-enumerable\n          };\n        } catch (e: any) {\n          if (e.details?.errorCode === \"BlobAlreadyExists\") {\n            return {\n              succeeded: false,\n              ...e.response?.parsedHeaders,\n              _response: e.response,\n            };\n          }\n          throw e;\n        }\n      },\n    );\n  }\n\n  /**\n   * Seals the append blob, making it read only.\n   *\n   * @param options -\n   */\n  public async seal(options: AppendBlobSealOptions = {}): Promise<AppendBlobAppendBlockResponse> {\n    options.conditions = options.conditions || {};\n    return tracingClient.withSpan(\"AppendBlobClient-seal\", options, async (updatedOptions) => {\n      return assertResponse<AppendBlobSealHeaders, AppendBlobSealHeaders>(\n        await this.appendBlobContext.seal({\n          abortSignal: options.abortSignal,\n          appendPositionAccessConditions: options.conditions,\n          leaseAccessConditions: options.conditions,\n          modifiedAccessConditions: {\n            ...options.conditions,\n            ifTags: options.conditions?.tagConditions,\n          },\n          tracingOptions: updatedOptions.tracingOptions,\n        }),\n      );\n    });\n  }\n\n  /**\n   * Commits a new block of data to the end of the existing append blob.\n   * @see https://docs.microsoft.com/rest/api/storageservices/append-block\n   *\n   * @param body - Data to be appended.\n   * @param contentLength - Length of the body in bytes.\n   * @param options - Options to the Append Block operation.\n   *\n   *\n   * Example usage:\n   *\n   * ```js\n   * const content = \"Hello World!\";\n   *\n   * // Create a new append blob and append data to the blob.\n   * const newAppendBlobClient = containerClient.getAppendBlobClient(\"<blob name>\");\n   * await newAppendBlobClient.create();\n   * await newAppendBlobClient.appendBlock(content, content.length);\n   *\n   * // Append data to an existing append blob.\n   * const existingAppendBlobClient = containerClient.getAppendBlobClient(\"<blob name>\");\n   * await existingAppendBlobClient.appendBlock(content, content.length);\n   * ```\n   */\n  public async appendBlock(\n    body: HttpRequestBody,\n    contentLength: number,\n    options: AppendBlobAppendBlockOptions = {},\n  ): Promise<AppendBlobAppendBlockResponse> {\n    options.conditions = options.conditions || {};\n    ensureCpkIfSpecified(options.customerProvidedKey, this.isHttps);\n    return tracingClient.withSpan(\n      \"AppendBlobClient-appendBlock\",\n      options,\n      async (updatedOptions) => {\n        return assertResponse<AppendBlobAppendBlockHeaders, AppendBlobAppendBlockHeaders>(\n          await this.appendBlobContext.appendBlock(contentLength, body, {\n            abortSignal: options.abortSignal,\n            appendPositionAccessConditions: options.conditions,\n            leaseAccessConditions: options.conditions,\n            modifiedAccessConditions: {\n              ...options.conditions,\n              ifTags: options.conditions?.tagConditions,\n            },\n            requestOptions: {\n              onUploadProgress: options.onProgress,\n            },\n            transactionalContentMD5: options.transactionalContentMD5,\n            transactionalContentCrc64: options.transactionalContentCrc64,\n            cpkInfo: options.customerProvidedKey,\n            encryptionScope: options.encryptionScope,\n            tracingOptions: updatedOptions.tracingOptions,\n          }),\n        );\n      },\n    );\n  }\n\n  /**\n   * The Append Block operation commits a new block of data to the end of an existing append blob\n   * where the contents are read from a source url.\n   * @see https://docs.microsoft.com/en-us/rest/api/storageservices/append-block-from-url\n   *\n   * @param sourceURL -\n   *                 The url to the blob that will be the source of the copy. A source blob in the same storage account can\n   *                 be authenticated via Shared Key. However, if the source is a blob in another account, the source blob\n   *                 must either be public or must be authenticated via a shared access signature. If the source blob is\n   *                 public, no authentication is required to perform the operation.\n   * @param sourceOffset - Offset in source to be appended\n   * @param count - Number of bytes to be appended as a block\n   * @param options -\n   */\n  public async appendBlockFromURL(\n    sourceURL: string,\n    sourceOffset: number,\n    count: number,\n    options: AppendBlobAppendBlockFromURLOptions = {},\n  ): Promise<AppendBlobAppendBlockFromUrlResponse> {\n    options.conditions = options.conditions || {};\n    options.sourceConditions = options.sourceConditions || {};\n\n    ensureCpkIfSpecified(options.customerProvidedKey, this.isHttps);\n    return tracingClient.withSpan(\n      \"AppendBlobClient-appendBlockFromURL\",\n      options,\n      async (updatedOptions) => {\n        return assertResponse<\n          AppendBlobAppendBlockFromUrlHeaders,\n          AppendBlobAppendBlockFromUrlHeaders\n        >(\n          await this.appendBlobContext.appendBlockFromUrl(sourceURL, 0, {\n            abortSignal: options.abortSignal,\n            sourceRange: rangeToString({ offset: sourceOffset, count }),\n            sourceContentMD5: options.sourceContentMD5,\n            sourceContentCrc64: options.sourceContentCrc64,\n            leaseAccessConditions: options.conditions,\n            appendPositionAccessConditions: options.conditions,\n            modifiedAccessConditions: {\n              ...options.conditions,\n              ifTags: options.conditions?.tagConditions,\n            },\n            sourceModifiedAccessConditions: {\n              sourceIfMatch: options.sourceConditions?.ifMatch,\n              sourceIfModifiedSince: options.sourceConditions?.ifModifiedSince,\n              sourceIfNoneMatch: options.sourceConditions?.ifNoneMatch,\n              sourceIfUnmodifiedSince: options.sourceConditions?.ifUnmodifiedSince,\n            },\n            copySourceAuthorization: httpAuthorizationToString(options.sourceAuthorization),\n            cpkInfo: options.customerProvidedKey,\n            encryptionScope: options.encryptionScope,\n            tracingOptions: updatedOptions.tracingOptions,\n          }),\n        );\n      },\n    );\n  }\n}\n\n/**\n * Options to configure {@link BlockBlobClient.upload} operation.\n */\nexport interface BlockBlobUploadOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * Conditions to meet when uploading to the block blob.\n   */\n  conditions?: BlobRequestConditions;\n  /**\n   * HTTP headers to set when uploading to a block blob. A common header to set is\n   * `blobContentType`, enabling the browser to provide functionality\n   * based on file type.\n   *\n   */\n  blobHTTPHeaders?: BlobHTTPHeaders;\n  /**\n   * A collection of key-value string pair to associate with the blob when uploading to a block blob.\n   */\n  metadata?: Metadata;\n  /**\n   * Callback to receive events on the progress of upload operation.\n   */\n  onProgress?: (progress: TransferProgressEvent) => void;\n  /**\n   * Customer Provided Key Info.\n   */\n  customerProvidedKey?: CpkInfo;\n  /**\n   * Optional. Version 2019-07-07 and later.  Specifies the name of the encryption scope to use to\n   * encrypt the data provided in the request. If not specified, encryption is performed with the\n   * default account encryption scope.  For more information, see Encryption at Rest for Azure\n   * Storage Services.\n   */\n  encryptionScope?: string;\n  /**\n   * Access tier.\n   * More Details - https://docs.microsoft.com/en-us/azure/storage/blobs/storage-blob-storage-tiers\n   */\n  tier?: BlockBlobTier | string;\n  /**\n   * Optional. Specifies immutability policy for a blob.\n   * Note that is parameter is only applicable to a blob within a container that\n   * has version level worm enabled.\n   */\n  immutabilityPolicy?: BlobImmutabilityPolicy;\n  /**\n   * Optional. Indicates if a legal hold should be placed on the blob.\n   * Note that is parameter is only applicable to a blob within a container that\n   * has version level worm enabled.\n   */\n  legalHold?: boolean;\n  /**\n   * Blob tags.\n   */\n  tags?: Tags;\n}\n\n/**\n * Options to configure {@link BlockBlobClient.syncUploadFromURL} operation.\n */\nexport interface BlockBlobSyncUploadFromURLOptions extends CommonOptions {\n  /**\n   * Server timeout in seconds.\n   * For more information, @see https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations\n   */\n  timeoutInSeconds?: number;\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * Optional. Specifies a user-defined name-value pair associated with the blob. If no name-value\n   * pairs are specified, the operation will copy the metadata from the source blob or file to the\n   * destination blob. If one or more name-value pairs are specified, the destination blob is\n   * created with the specified metadata, and metadata is not copied from the source blob or file.\n   * Note that beginning with version 2009-09-19, metadata names must adhere to the naming rules\n   * for C# identifiers. See Naming and Referencing Containers, Blobs, and Metadata for more\n   * information.\n   */\n  metadata?: Metadata;\n  /**\n   * Optional. Version 2019-07-07 and later.  Specifies the name of the encryption scope to use to\n   * encrypt the data provided in the request. If not specified, encryption is performed with the\n   * default account encryption scope.  For more information, see Encryption at Rest for Azure\n   * Storage Services.\n   */\n  encryptionScope?: string;\n  /**\n   * Access tier.\n   * More Details - https://docs.microsoft.com/en-us/azure/storage/blobs/storage-blob-storage-tiers\n   */\n  tier?: BlockBlobTier | string;\n  /**\n   * Specify the md5 calculated for the range of bytes that must be read from the copy source.\n   */\n  sourceContentMD5?: Uint8Array;\n  /**\n   * Blob tags.\n   */\n  tags?: Tags;\n  /**\n   * Optional, default is true.  Indicates if properties from the source blob should be copied.\n   */\n  copySourceBlobProperties?: boolean;\n  /**\n   * HTTP headers to set when uploading to a block blob.\n   *\n   * A common header to set is `blobContentType`, enabling the browser to provide functionality\n   * based on file type.\n   *\n   */\n  blobHTTPHeaders?: BlobHTTPHeaders;\n  /**\n   * Conditions to meet for the destination Azure Blob.\n   */\n  conditions?: BlobRequestConditions;\n  /**\n   * Customer Provided Key Info.\n   */\n  customerProvidedKey?: CpkInfo;\n  /**\n   * Optional. Conditions to meet for the source Azure Blob.\n   */\n  sourceConditions?: ModifiedAccessConditions;\n  /**\n   * Only Bearer type is supported. Credentials should be a valid OAuth access token to copy source.\n   */\n  sourceAuthorization?: HttpAuthorization;\n  /**\n   * Optional, default 'replace'.  Indicates if source tags should be copied or replaced with the tags specified by {@link tags}.\n   */\n  copySourceTags?: BlobCopySourceTags;\n}\n\n/**\n * Blob query error type.\n */\nexport interface BlobQueryError {\n  /**\n   * Whether error is fatal. Fatal error will stop query.\n   */\n  isFatal: boolean;\n  /**\n   * Error name.\n   */\n  name: string;\n  /**\n   * Position in bytes of the query.\n   */\n  position: number;\n  /**\n   * Error description.\n   */\n  description: string;\n}\n\n/**\n * Options to query blob with JSON format.\n */\nexport interface BlobQueryJsonTextConfiguration {\n  /**\n   * Record separator.\n   */\n  recordSeparator: string;\n  /**\n   * Query for a JSON format blob.\n   */\n  kind: \"json\";\n}\n\n/**\n * Options to query blob with CSV format.\n */\nexport interface BlobQueryCsvTextConfiguration {\n  /**\n   * Record separator.\n   */\n  recordSeparator: string;\n  /**\n   * Query for a CSV format blob.\n   */\n  kind: \"csv\";\n  /**\n   * Column separator. Default is \",\".\n   */\n  columnSeparator?: string;\n  /**\n   * Field quote.\n   */\n  fieldQuote?: string;\n  /**\n   * Escape character.\n   */\n  escapeCharacter?: string;\n  /**\n   * Has headers. Default is false.\n   */\n  hasHeaders?: boolean;\n}\n\n/**\n * Options to query blob with Apache Arrow format. Only valid for {@link BlockBlobQueryOptions.outputTextConfiguration}.\n */\nexport interface BlobQueryArrowConfiguration {\n  /**\n   * Kind.\n   */\n  kind: \"arrow\";\n\n  /**\n   * List of {@link BlobQueryArrowField} describing the schema of the data.\n   */\n  schema: BlobQueryArrowField[];\n}\n\n/**\n * Options to query blob with Parquet format. Only valid for {@link BlockBlobQueryOptions.inputTextConfiguration}.\n */\nexport interface BlobQueryParquetConfiguration {\n  /**\n   * Kind.\n   */\n  kind: \"parquet\";\n}\n\n/**\n * Options to configure {@link BlockBlobClient.query} operation.\n */\nexport interface BlockBlobQueryOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * Configurations for the query input.\n   */\n  inputTextConfiguration?:\n    | BlobQueryJsonTextConfiguration\n    | BlobQueryCsvTextConfiguration\n    | BlobQueryParquetConfiguration;\n  /**\n   * Configurations for the query output.\n   */\n  outputTextConfiguration?:\n    | BlobQueryJsonTextConfiguration\n    | BlobQueryCsvTextConfiguration\n    | BlobQueryArrowConfiguration;\n  /**\n   * Callback to receive events on the progress of query operation.\n   */\n  onProgress?: (progress: TransferProgressEvent) => void;\n  /**\n   * Callback to receive error events during the query operaiton.\n   */\n  onError?: (error: BlobQueryError) => void;\n  /**\n   * Conditions to meet when uploading to the block blob.\n   */\n  conditions?: BlobRequestConditions;\n  /**\n   * Customer Provided Key Info.\n   */\n  customerProvidedKey?: CpkInfo;\n}\n\n/**\n * Options to configure {@link BlockBlobClient.stageBlock} operation.\n */\nexport interface BlockBlobStageBlockOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * If specified, contains the lease id that must be matched and lease with this id\n   * must be active in order for the operation to succeed.\n   */\n  conditions?: LeaseAccessConditions;\n  /**\n   * Callback to receive events on the progress of stage block operation.\n   */\n  onProgress?: (progress: TransferProgressEvent) => void;\n  /**\n   * An MD5 hash of the block content. This hash is used to verify the integrity of the block during transport.\n   * When this is specified, the storage service compares the hash of the content that has arrived with this value.\n   *\n   * transactionalContentMD5 and transactionalContentCrc64 cannot be set at same time.\n   */\n  transactionalContentMD5?: Uint8Array;\n\n  /**\n   * A CRC64 hash of the block content. This hash is used to verify the integrity of the block during transport.\n   * When this is specified, the storage service compares the hash of the content that has arrived with this value.\n   *\n   * transactionalContentMD5 and transactionalContentCrc64 cannot be set at same time.\n   */\n  transactionalContentCrc64?: Uint8Array;\n  /**\n   * Customer Provided Key Info.\n   */\n  customerProvidedKey?: CpkInfo;\n  /**\n   * Optional. Version 2019-07-07 and later.  Specifies the name of the encryption scope to use to\n   * encrypt the data provided in the request. If not specified, encryption is performed with the\n   * default account encryption scope.  For more information, see Encryption at Rest for Azure\n   * Storage Services.\n   */\n  encryptionScope?: string;\n}\n\n/**\n * Options to configure {@link BlockBlobClient.stageBlockFromURL} operation.\n */\nexport interface BlockBlobStageBlockFromURLOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * Specifies the bytes of the source Blob/File to upload.\n   * If not specified, the entire content is uploaded as a single block.\n   */\n  range?: Range;\n  /**\n   * If specified, contains the lease id that must be matched and lease with this id\n   * must be active in order for the operation to succeed.\n   */\n  conditions?: LeaseAccessConditions;\n  /**\n   * An MD5 hash of the content from the URI.\n   * This hash is used to verify the integrity of the content during transport of the data from the URI.\n   * When this is specified, the storage service compares the hash of the content that has arrived from the copy-source with this value.\n   *\n   * sourceContentMD5 and sourceContentCrc64 cannot be set at same time.\n   */\n  sourceContentMD5?: Uint8Array;\n  /**\n   * A CRC64 hash of the content from the URI.\n   * This hash is used to verify the integrity of the content during transport of the data from the URI.\n   * When this is specified, the storage service compares the hash of the content that has arrived from the copy-source with this value.\n   *\n   * sourceContentMD5 and sourceContentCrc64 cannot be set at same time.\n   */\n  sourceContentCrc64?: Uint8Array;\n  /**\n   * Customer Provided Key Info.\n   */\n  customerProvidedKey?: CpkInfo;\n  /**\n   * Optional. Version 2019-07-07 and later.  Specifies the name of the encryption scope to use to\n   * encrypt the data provided in the request. If not specified, encryption is performed with the\n   * default account encryption scope.  For more information, see Encryption at Rest for Azure\n   * Storage Services.\n   */\n  encryptionScope?: string;\n  /**\n   * Only Bearer type is supported. Credentials should be a valid OAuth access token to copy source.\n   */\n  sourceAuthorization?: HttpAuthorization;\n}\n\n/**\n * Options to configure {@link BlockBlobClient.commitBlockList} operation.\n */\nexport interface BlockBlobCommitBlockListOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * Conditions to meet when committing the block list.\n   */\n  conditions?: BlobRequestConditions;\n  /**\n   * HTTP headers to set when committing block list.\n   */\n  blobHTTPHeaders?: BlobHTTPHeaders;\n  /**\n   * A collection of key-value string pair to associate with the blob when committing block list.\n   */\n  metadata?: Metadata;\n  /**\n   * Customer Provided Key Info.\n   */\n  customerProvidedKey?: CpkInfo;\n  /**\n   * Optional. Version 2019-07-07 and later.  Specifies the name of the encryption scope to use to\n   * encrypt the data provided in the request. If not specified, encryption is performed with the\n   * default account encryption scope.  For more information, see Encryption at Rest for Azure\n   * Storage Services.\n   */\n  encryptionScope?: string;\n  /**\n   * Optional. Specifies immutability policy for a blob.\n   * Note that is parameter is only applicable to a blob within a container that\n   * has version level worm enabled.\n   */\n  immutabilityPolicy?: BlobImmutabilityPolicy;\n  /**\n   * Optional. Indicates if a legal hold should be placed on the blob.\n   * Note that is parameter is only applicable to a blob within a container that\n   * has version level worm enabled.\n   */\n  legalHold?: boolean;\n  /**\n   * Access tier.\n   * More Details - https://docs.microsoft.com/en-us/azure/storage/blobs/storage-blob-storage-tiers\n   */\n  tier?: BlockBlobTier | string;\n\n  /**\n   * Blob tags.\n   */\n  tags?: Tags;\n}\n\n/**\n * Options to configure {@link BlockBlobClient.getBlockList} operation.\n */\nexport interface BlockBlobGetBlockListOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * If specified, contains the lease id that must be matched and lease with this id\n   * must be active in order for the operation to succeed.\n   */\n  conditions?: LeaseAccessConditions & TagConditions;\n}\n\n/**\n * Option interface for the {@link BlockBlobClient.uploadStream} operation.\n */\nexport interface BlockBlobUploadStreamOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n\n  /**\n   * Blob HTTP Headers.\n   *\n   * A common header to set is `blobContentType`, enabling the\n   * browser to provide functionality based on file type.\n   *\n   */\n  blobHTTPHeaders?: BlobHTTPHeaders;\n\n  /**\n   * Customer Provided Key Info.\n   */\n  customerProvidedKey?: CpkInfo;\n\n  /**\n   * Metadata of block blob.\n   */\n  metadata?: { [propertyName: string]: string };\n\n  /**\n   * Access conditions headers.\n   */\n  conditions?: BlobRequestConditions;\n\n  /**\n   * Progress updater.\n   */\n  onProgress?: (progress: TransferProgressEvent) => void;\n\n  /**\n   * Optional. Version 2019-07-07 and later.  Specifies the name of the encryption scope to use to\n   * encrypt the data provided in the request. If not specified, encryption is performed with the\n   * default account encryption scope.  For more information, see Encryption at Rest for Azure\n   * Storage Services.\n   */\n  encryptionScope?: string;\n\n  /**\n   * Blob tags.\n   */\n  tags?: Tags;\n\n  /**\n   * Access tier.\n   * More Details - https://docs.microsoft.com/en-us/azure/storage/blobs/storage-blob-storage-tiers\n   */\n  tier?: BlockBlobTier | string;\n}\n/**\n * Option interface for {@link BlockBlobClient.uploadFile} and {@link BlockBlobClient.uploadSeekableStream}.\n */\nexport interface BlockBlobParallelUploadOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n\n  /**\n   * Destination block blob size in bytes.\n   */\n  blockSize?: number;\n\n  /**\n   * Blob size threshold in bytes to start concurrency uploading.\n   * Default value is 256MB, blob size less than this option will\n   * be uploaded via one I/O operation without concurrency.\n   * You can customize a value less equal than the default value.\n   */\n  maxSingleShotSize?: number;\n\n  /**\n   * Progress updater.\n   */\n  onProgress?: (progress: TransferProgressEvent) => void;\n\n  /**\n   * Blob HTTP Headers. A common header to set is\n   * `blobContentType`, enabling the browser to provide\n   * functionality based on file type.\n   *\n   */\n  blobHTTPHeaders?: BlobHTTPHeaders;\n\n  /**\n   * Metadata of block blob.\n   */\n  metadata?: { [propertyName: string]: string };\n\n  /**\n   * Access conditions headers.\n   */\n  conditions?: BlobRequestConditions;\n\n  /**\n   * Concurrency of parallel uploading. Must be greater than or equal to 0.\n   */\n  concurrency?: number;\n\n  /**\n   * Optional. Version 2019-07-07 and later.  Specifies the name of the encryption scope to use to\n   * encrypt the data provided in the request. If not specified, encryption is performed with the\n   * default account encryption scope.  For more information, see Encryption at Rest for Azure\n   * Storage Services.\n   */\n  encryptionScope?: string;\n\n  /**\n   * Blob tags.\n   */\n  tags?: Tags;\n\n  /**\n   * Access tier.\n   * More Details - https://docs.microsoft.com/en-us/azure/storage/blobs/storage-blob-storage-tiers\n   */\n  tier?: BlockBlobTier | string;\n}\n\n/**\n * Response type for {@link BlockBlobClient.uploadFile}, {@link BlockBlobClient.uploadStream}, and\n * {@link BlockBlobClient.uploadBrowserDate}.\n */\nexport type BlobUploadCommonResponse = WithResponse<BlockBlobUploadHeaders>;\n\n/**\n * BlockBlobClient defines a set of operations applicable to block blobs.\n */\nexport class BlockBlobClient extends BlobClient {\n  /**\n   * blobContext provided by protocol layer.\n   *\n   * Note. Ideally BlobClient should set BlobClient.blobContext to protected. However, API\n   * extractor has issue blocking that. Here we redecelare _blobContext in BlockBlobClient.\n   */\n  private _blobContext: StorageBlob;\n\n  /**\n   * blockBlobContext provided by protocol layer.\n   */\n  private blockBlobContext: BlockBlob;\n\n  /**\n   *\n   * Creates an instance of BlockBlobClient.\n   *\n   * @param connectionString - Account connection string or a SAS connection string of an Azure storage account.\n   *                                  [ Note - Account connection string can only be used in NODE.JS runtime. ]\n   *                                  Account connection string example -\n   *                                  `DefaultEndpointsProtocol=https;AccountName=myaccount;AccountKey=accountKey;EndpointSuffix=core.windows.net`\n   *                                  SAS connection string example -\n   *                                  `BlobEndpoint=https://myaccount.blob.core.windows.net/;QueueEndpoint=https://myaccount.queue.core.windows.net/;FileEndpoint=https://myaccount.file.core.windows.net/;TableEndpoint=https://myaccount.table.core.windows.net/;SharedAccessSignature=sasString`\n   * @param containerName - Container name.\n   * @param blobName - Blob name.\n   * @param options - Optional. Options to configure the HTTP pipeline.\n   */\n  constructor(\n    connectionString: string,\n    containerName: string,\n    blobName: string,\n    // Legacy, no fix for eslint error without breaking. Disable it for this interface.\n    /* eslint-disable-next-line @azure/azure-sdk/ts-naming-options*/\n    options?: StoragePipelineOptions,\n  );\n  /**\n   * Creates an instance of BlockBlobClient.\n   * This method accepts an encoded URL or non-encoded URL pointing to a block blob.\n   * Encoded URL string will NOT be escaped twice, only special characters in URL path will be escaped.\n   * If a blob name includes ? or %, blob name must be encoded in the URL.\n   *\n   * @param url - A URL string pointing to Azure Storage block blob, such as\n   *                     \"https://myaccount.blob.core.windows.net/mycontainer/blockblob\". You can\n   *                     append a SAS if using AnonymousCredential, such as\n   *                     \"https://myaccount.blob.core.windows.net/mycontainer/blockblob?sasString\".\n   *                     This method accepts an encoded URL or non-encoded URL pointing to a blob.\n   *                     Encoded URL string will NOT be escaped twice, only special characters in URL path will be escaped.\n   *                     However, if a blob name includes ? or %, blob name must be encoded in the URL.\n   *                     Such as a blob named \"my?blob%\", the URL should be \"https://myaccount.blob.core.windows.net/mycontainer/my%3Fblob%25\".\n   * @param credential -  Such as AnonymousCredential, StorageSharedKeyCredential or any credential from the `@azure/identity` package to authenticate requests to the service. You can also provide an object that implements the TokenCredential interface. If not specified, AnonymousCredential is used.\n   * @param options - Optional. Options to configure the HTTP pipeline.\n   */\n  constructor(\n    url: string,\n    credential?: StorageSharedKeyCredential | AnonymousCredential | TokenCredential,\n    // Legacy, no fix for eslint error without breaking. Disable it for this interface.\n    /* eslint-disable-next-line @azure/azure-sdk/ts-naming-options*/\n    options?: StoragePipelineOptions,\n  );\n  /**\n   * Creates an instance of BlockBlobClient.\n   * This method accepts an encoded URL or non-encoded URL pointing to a block blob.\n   * Encoded URL string will NOT be escaped twice, only special characters in URL path will be escaped.\n   * If a blob name includes ? or %, blob name must be encoded in the URL.\n   *\n   * @param url - A URL string pointing to Azure Storage block blob, such as\n   *                     \"https://myaccount.blob.core.windows.net/mycontainer/blockblob\". You can\n   *                     append a SAS if using AnonymousCredential, such as\n   *                     \"https://myaccount.blob.core.windows.net/mycontainer/blockblob?sasString\".\n   *                     This method accepts an encoded URL or non-encoded URL pointing to a blob.\n   *                     Encoded URL string will NOT be escaped twice, only special characters in URL path will be escaped.\n   *                     However, if a blob name includes ? or %, blob name must be encoded in the URL.\n   *                     Such as a blob named \"my?blob%\", the URL should be \"https://myaccount.blob.core.windows.net/mycontainer/my%3Fblob%25\".\n   * @param pipeline - Call newPipeline() to create a default\n   *                            pipeline, or provide a customized pipeline.\n   */\n  constructor(url: string, pipeline: PipelineLike);\n  constructor(\n    urlOrConnectionString: string,\n    credentialOrPipelineOrContainerName?:\n      | string\n      | StorageSharedKeyCredential\n      | AnonymousCredential\n      | TokenCredential\n      | PipelineLike,\n    blobNameOrOptions?: string | StoragePipelineOptions,\n    // Legacy, no fix for eslint error without breaking. Disable it for this interface.\n    /* eslint-disable-next-line @azure/azure-sdk/ts-naming-options*/\n    options?: StoragePipelineOptions,\n  ) {\n    // In TypeScript we cannot simply pass all parameters to super() like below so have to duplicate the code instead.\n    //   super(s, credentialOrPipelineOrContainerNameOrOptions, blobNameOrOptions, options);\n    let pipeline: PipelineLike;\n    let url: string;\n    options = options || {};\n    if (isPipelineLike(credentialOrPipelineOrContainerName)) {\n      // (url: string, pipeline: Pipeline)\n      url = urlOrConnectionString;\n      pipeline = credentialOrPipelineOrContainerName;\n    } else if (\n      (isNode && credentialOrPipelineOrContainerName instanceof StorageSharedKeyCredential) ||\n      credentialOrPipelineOrContainerName instanceof AnonymousCredential ||\n      isTokenCredential(credentialOrPipelineOrContainerName)\n    ) {\n      // (url: string, credential?: StorageSharedKeyCredential | AnonymousCredential | TokenCredential, options?: StoragePipelineOptions)\n      url = urlOrConnectionString;\n      options = blobNameOrOptions as StoragePipelineOptions;\n      pipeline = newPipeline(credentialOrPipelineOrContainerName, options);\n    } else if (\n      !credentialOrPipelineOrContainerName &&\n      typeof credentialOrPipelineOrContainerName !== \"string\"\n    ) {\n      // (url: string, credential?: StorageSharedKeyCredential | AnonymousCredential | TokenCredential, options?: StoragePipelineOptions)\n      // The second parameter is undefined. Use anonymous credential.\n      url = urlOrConnectionString;\n      if (blobNameOrOptions && typeof blobNameOrOptions !== \"string\") {\n        options = blobNameOrOptions as StoragePipelineOptions;\n      }\n      pipeline = newPipeline(new AnonymousCredential(), options);\n    } else if (\n      credentialOrPipelineOrContainerName &&\n      typeof credentialOrPipelineOrContainerName === \"string\" &&\n      blobNameOrOptions &&\n      typeof blobNameOrOptions === \"string\"\n    ) {\n      // (connectionString: string, containerName: string, blobName: string, options?: StoragePipelineOptions)\n      const containerName = credentialOrPipelineOrContainerName;\n      const blobName = blobNameOrOptions;\n\n      const extractedCreds = extractConnectionStringParts(urlOrConnectionString);\n      if (extractedCreds.kind === \"AccountConnString\") {\n        if (isNode) {\n          const sharedKeyCredential = new StorageSharedKeyCredential(\n            extractedCreds.accountName!,\n            extractedCreds.accountKey,\n          );\n          url = appendToURLPath(\n            appendToURLPath(extractedCreds.url, encodeURIComponent(containerName)),\n            encodeURIComponent(blobName),\n          );\n\n          if (!options.proxyOptions) {\n            options.proxyOptions = getDefaultProxySettings(extractedCreds.proxyUri);\n          }\n\n          pipeline = newPipeline(sharedKeyCredential, options);\n        } else {\n          throw new Error(\"Account connection string is only supported in Node.js environment\");\n        }\n      } else if (extractedCreds.kind === \"SASConnString\") {\n        url =\n          appendToURLPath(\n            appendToURLPath(extractedCreds.url, encodeURIComponent(containerName)),\n            encodeURIComponent(blobName),\n          ) +\n          \"?\" +\n          extractedCreds.accountSas;\n        pipeline = newPipeline(new AnonymousCredential(), options);\n      } else {\n        throw new Error(\n          \"Connection string must be either an Account connection string or a SAS connection string\",\n        );\n      }\n    } else {\n      throw new Error(\"Expecting non-empty strings for containerName and blobName parameters\");\n    }\n    super(url, pipeline);\n    this.blockBlobContext = this.storageClientContext.blockBlob;\n    this._blobContext = this.storageClientContext.blob;\n  }\n\n  /**\n   * Creates a new BlockBlobClient object identical to the source but with the\n   * specified snapshot timestamp.\n   * Provide \"\" will remove the snapshot and return a URL to the base blob.\n   *\n   * @param snapshot - The snapshot timestamp.\n   * @returns A new BlockBlobClient object identical to the source but with the specified snapshot timestamp.\n   */\n  public withSnapshot(snapshot: string): BlockBlobClient {\n    return new BlockBlobClient(\n      setURLParameter(\n        this.url,\n        URLConstants.Parameters.SNAPSHOT,\n        snapshot.length === 0 ? undefined : snapshot,\n      ),\n      this.pipeline,\n    );\n  }\n\n  /**\n   * ONLY AVAILABLE IN NODE.JS RUNTIME.\n   *\n   * Quick query for a JSON or CSV formatted blob.\n   *\n   * Example usage (Node.js):\n   *\n   * ```js\n   * // Query and convert a blob to a string\n   * const queryBlockBlobResponse = await blockBlobClient.query(\"select * from BlobStorage\");\n   * const downloaded = (await streamToBuffer(queryBlockBlobResponse.readableStreamBody)).toString();\n   * console.log(\"Query blob content:\", downloaded);\n   *\n   * async function streamToBuffer(readableStream) {\n   *   return new Promise((resolve, reject) => {\n   *     const chunks = [];\n   *     readableStream.on(\"data\", (data) => {\n   *       chunks.push(data instanceof Buffer ? data : Buffer.from(data));\n   *     });\n   *     readableStream.on(\"end\", () => {\n   *       resolve(Buffer.concat(chunks));\n   *     });\n   *     readableStream.on(\"error\", reject);\n   *   });\n   * }\n   * ```\n   *\n   * @param query -\n   * @param options -\n   */\n  public async query(\n    query: string,\n    options: BlockBlobQueryOptions = {},\n  ): Promise<BlobDownloadResponseModel> {\n    ensureCpkIfSpecified(options.customerProvidedKey, this.isHttps);\n    if (!isNode) {\n      throw new Error(\"This operation currently is only supported in Node.js.\");\n    }\n\n    return tracingClient.withSpan(\"BlockBlobClient-query\", options, async (updatedOptions) => {\n      const response = assertResponse<BlobQueryResponseInternal, BlobQueryHeaders>(\n        await this._blobContext.query({\n          abortSignal: options.abortSignal,\n          queryRequest: {\n            queryType: \"SQL\",\n            expression: query,\n            inputSerialization: toQuerySerialization(options.inputTextConfiguration),\n            outputSerialization: toQuerySerialization(options.outputTextConfiguration),\n          },\n          leaseAccessConditions: options.conditions,\n          modifiedAccessConditions: {\n            ...options.conditions,\n            ifTags: options.conditions?.tagConditions,\n          },\n          cpkInfo: options.customerProvidedKey,\n          tracingOptions: updatedOptions.tracingOptions,\n        }),\n      );\n      return new BlobQueryResponse(response, {\n        abortSignal: options.abortSignal,\n        onProgress: options.onProgress,\n        onError: options.onError,\n      });\n    });\n  }\n\n  /**\n   * Creates a new block blob, or updates the content of an existing block blob.\n   * Updating an existing block blob overwrites any existing metadata on the blob.\n   * Partial updates are not supported; the content of the existing blob is\n   * overwritten with the new content. To perform a partial update of a block blob's,\n   * use {@link stageBlock} and {@link commitBlockList}.\n   *\n   * This is a non-parallel uploading method, please use {@link uploadFile},\n   * {@link uploadStream} or {@link uploadBrowserData} for better performance\n   * with concurrency uploading.\n   *\n   * @see https://docs.microsoft.com/rest/api/storageservices/put-blob\n   *\n   * @param body - Blob, string, ArrayBuffer, ArrayBufferView or a function\n   *                               which returns a new Readable stream whose offset is from data source beginning.\n   * @param contentLength - Length of body in bytes. Use Buffer.byteLength() to calculate body length for a\n   *                               string including non non-Base64/Hex-encoded characters.\n   * @param options - Options to the Block Blob Upload operation.\n   * @returns Response data for the Block Blob Upload operation.\n   *\n   * Example usage:\n   *\n   * ```js\n   * const content = \"Hello world!\";\n   * const uploadBlobResponse = await blockBlobClient.upload(content, content.length);\n   * ```\n   */\n  public async upload(\n    body: HttpRequestBody,\n    contentLength: number,\n    options: BlockBlobUploadOptions = {},\n  ): Promise<BlockBlobUploadResponse> {\n    options.conditions = options.conditions || {};\n    ensureCpkIfSpecified(options.customerProvidedKey, this.isHttps);\n    return tracingClient.withSpan(\"BlockBlobClient-upload\", options, async (updatedOptions) => {\n      return assertResponse<BlockBlobUploadHeaders, BlockBlobUploadHeaders>(\n        await this.blockBlobContext.upload(contentLength, body, {\n          abortSignal: options.abortSignal,\n          blobHttpHeaders: options.blobHTTPHeaders,\n          leaseAccessConditions: options.conditions,\n          metadata: options.metadata,\n          modifiedAccessConditions: {\n            ...options.conditions,\n            ifTags: options.conditions?.tagConditions,\n          },\n          requestOptions: {\n            onUploadProgress: options.onProgress,\n          },\n          cpkInfo: options.customerProvidedKey,\n          encryptionScope: options.encryptionScope,\n          immutabilityPolicyExpiry: options.immutabilityPolicy?.expiriesOn,\n          immutabilityPolicyMode: options.immutabilityPolicy?.policyMode,\n          legalHold: options.legalHold,\n          tier: toAccessTier(options.tier),\n          blobTagsString: toBlobTagsString(options.tags),\n          tracingOptions: updatedOptions.tracingOptions,\n        }),\n      );\n    });\n  }\n\n  /**\n   * Creates a new Block Blob where the contents of the blob are read from a given URL.\n   * This API is supported beginning with the 2020-04-08 version. Partial updates\n   * are not supported with Put Blob from URL; the content of an existing blob is overwritten with\n   * the content of the new blob.  To perform partial updates to a block blob’s contents using a\n   * source URL, use {@link stageBlockFromURL} and {@link commitBlockList}.\n   *\n   * @param sourceURL - Specifies the URL of the blob. The value\n   *                           may be a URL of up to 2 KB in length that specifies a blob.\n   *                           The value should be URL-encoded as it would appear\n   *                           in a request URI. The source blob must either be public\n   *                           or must be authenticated via a shared access signature.\n   *                           If the source blob is public, no authentication is required\n   *                           to perform the operation. Here are some examples of source object URLs:\n   *                           - https://myaccount.blob.core.windows.net/mycontainer/myblob\n   *                           - https://myaccount.blob.core.windows.net/mycontainer/myblob?snapshot=<DateTime>\n   * @param options - Optional parameters.\n   */\n\n  public async syncUploadFromURL(\n    sourceURL: string,\n    options: BlockBlobSyncUploadFromURLOptions = {},\n  ): Promise<BlockBlobPutBlobFromUrlResponse> {\n    options.conditions = options.conditions || {};\n    ensureCpkIfSpecified(options.customerProvidedKey, this.isHttps);\n    return tracingClient.withSpan(\n      \"BlockBlobClient-syncUploadFromURL\",\n      options,\n      async (updatedOptions) => {\n        return assertResponse<BlockBlobPutBlobFromUrlHeaders, BlockBlobPutBlobFromUrlHeaders>(\n          await this.blockBlobContext.putBlobFromUrl(0, sourceURL, {\n            ...options,\n            blobHttpHeaders: options.blobHTTPHeaders,\n            leaseAccessConditions: options.conditions,\n            modifiedAccessConditions: {\n              ...options.conditions,\n              ifTags: options.conditions?.tagConditions,\n            },\n            sourceModifiedAccessConditions: {\n              sourceIfMatch: options.sourceConditions?.ifMatch,\n              sourceIfModifiedSince: options.sourceConditions?.ifModifiedSince,\n              sourceIfNoneMatch: options.sourceConditions?.ifNoneMatch,\n              sourceIfUnmodifiedSince: options.sourceConditions?.ifUnmodifiedSince,\n              sourceIfTags: options.sourceConditions?.tagConditions,\n            },\n            cpkInfo: options.customerProvidedKey,\n            copySourceAuthorization: httpAuthorizationToString(options.sourceAuthorization),\n            tier: toAccessTier(options.tier),\n            blobTagsString: toBlobTagsString(options.tags),\n            copySourceTags: options.copySourceTags,\n            tracingOptions: updatedOptions.tracingOptions,\n          }),\n        );\n      },\n    );\n  }\n\n  /**\n   * Uploads the specified block to the block blob's \"staging area\" to be later\n   * committed by a call to commitBlockList.\n   * @see https://docs.microsoft.com/rest/api/storageservices/put-block\n   *\n   * @param blockId - A 64-byte value that is base64-encoded\n   * @param body - Data to upload to the staging area.\n   * @param contentLength - Number of bytes to upload.\n   * @param options - Options to the Block Blob Stage Block operation.\n   * @returns Response data for the Block Blob Stage Block operation.\n   */\n  public async stageBlock(\n    blockId: string,\n    body: HttpRequestBody,\n    contentLength: number,\n    options: BlockBlobStageBlockOptions = {},\n  ): Promise<BlockBlobStageBlockResponse> {\n    ensureCpkIfSpecified(options.customerProvidedKey, this.isHttps);\n    return tracingClient.withSpan(\"BlockBlobClient-stageBlock\", options, async (updatedOptions) => {\n      return assertResponse<BlockBlobStageBlockHeaders, BlockBlobStageBlockHeaders>(\n        await this.blockBlobContext.stageBlock(blockId, contentLength, body, {\n          abortSignal: options.abortSignal,\n          leaseAccessConditions: options.conditions,\n          requestOptions: {\n            onUploadProgress: options.onProgress,\n          },\n          transactionalContentMD5: options.transactionalContentMD5,\n          transactionalContentCrc64: options.transactionalContentCrc64,\n          cpkInfo: options.customerProvidedKey,\n          encryptionScope: options.encryptionScope,\n          tracingOptions: updatedOptions.tracingOptions,\n        }),\n      );\n    });\n  }\n\n  /**\n   * The Stage Block From URL operation creates a new block to be committed as part\n   * of a blob where the contents are read from a URL.\n   * This API is available starting in version 2018-03-28.\n   * @see https://docs.microsoft.com/en-us/rest/api/storageservices/put-block-from-url\n   *\n   * @param blockId - A 64-byte value that is base64-encoded\n   * @param sourceURL - Specifies the URL of the blob. The value\n   *                           may be a URL of up to 2 KB in length that specifies a blob.\n   *                           The value should be URL-encoded as it would appear\n   *                           in a request URI. The source blob must either be public\n   *                           or must be authenticated via a shared access signature.\n   *                           If the source blob is public, no authentication is required\n   *                           to perform the operation. Here are some examples of source object URLs:\n   *                           - https://myaccount.blob.core.windows.net/mycontainer/myblob\n   *                           - https://myaccount.blob.core.windows.net/mycontainer/myblob?snapshot=<DateTime>\n   * @param offset - From which position of the blob to download, greater than or equal to 0\n   * @param count - How much data to be downloaded, greater than 0. Will download to the end when undefined\n   * @param options - Options to the Block Blob Stage Block From URL operation.\n   * @returns Response data for the Block Blob Stage Block From URL operation.\n   */\n  public async stageBlockFromURL(\n    blockId: string,\n    sourceURL: string,\n    offset: number = 0,\n    count?: number,\n    options: BlockBlobStageBlockFromURLOptions = {},\n  ): Promise<BlockBlobStageBlockFromURLResponse> {\n    ensureCpkIfSpecified(options.customerProvidedKey, this.isHttps);\n    return tracingClient.withSpan(\n      \"BlockBlobClient-stageBlockFromURL\",\n      options,\n      async (updatedOptions) => {\n        return assertResponse<BlockBlobStageBlockFromURLHeaders, BlockBlobStageBlockFromURLHeaders>(\n          await this.blockBlobContext.stageBlockFromURL(blockId, 0, sourceURL, {\n            abortSignal: options.abortSignal,\n            leaseAccessConditions: options.conditions,\n            sourceContentMD5: options.sourceContentMD5,\n            sourceContentCrc64: options.sourceContentCrc64,\n            sourceRange: offset === 0 && !count ? undefined : rangeToString({ offset, count }),\n            cpkInfo: options.customerProvidedKey,\n            encryptionScope: options.encryptionScope,\n            copySourceAuthorization: httpAuthorizationToString(options.sourceAuthorization),\n            tracingOptions: updatedOptions.tracingOptions,\n          }),\n        );\n      },\n    );\n  }\n\n  /**\n   * Writes a blob by specifying the list of block IDs that make up the blob.\n   * In order to be written as part of a blob, a block must have been successfully written\n   * to the server in a prior {@link stageBlock} operation. You can call {@link commitBlockList} to\n   * update a blob by uploading only those blocks that have changed, then committing the new and existing\n   * blocks together. Any blocks not specified in the block list and permanently deleted.\n   * @see https://docs.microsoft.com/rest/api/storageservices/put-block-list\n   *\n   * @param blocks -  Array of 64-byte value that is base64-encoded\n   * @param options - Options to the Block Blob Commit Block List operation.\n   * @returns Response data for the Block Blob Commit Block List operation.\n   */\n  public async commitBlockList(\n    blocks: string[],\n    options: BlockBlobCommitBlockListOptions = {},\n  ): Promise<BlockBlobCommitBlockListResponse> {\n    options.conditions = options.conditions || {};\n    ensureCpkIfSpecified(options.customerProvidedKey, this.isHttps);\n    return tracingClient.withSpan(\n      \"BlockBlobClient-commitBlockList\",\n      options,\n      async (updatedOptions) => {\n        return assertResponse<BlockBlobCommitBlockListHeaders, BlockBlobCommitBlockListHeaders>(\n          await this.blockBlobContext.commitBlockList(\n            { latest: blocks },\n            {\n              abortSignal: options.abortSignal,\n              blobHttpHeaders: options.blobHTTPHeaders,\n              leaseAccessConditions: options.conditions,\n              metadata: options.metadata,\n              modifiedAccessConditions: {\n                ...options.conditions,\n                ifTags: options.conditions?.tagConditions,\n              },\n              cpkInfo: options.customerProvidedKey,\n              encryptionScope: options.encryptionScope,\n              immutabilityPolicyExpiry: options.immutabilityPolicy?.expiriesOn,\n              immutabilityPolicyMode: options.immutabilityPolicy?.policyMode,\n              legalHold: options.legalHold,\n              tier: toAccessTier(options.tier),\n              blobTagsString: toBlobTagsString(options.tags),\n              tracingOptions: updatedOptions.tracingOptions,\n            },\n          ),\n        );\n      },\n    );\n  }\n\n  /**\n   * Returns the list of blocks that have been uploaded as part of a block blob\n   * using the specified block list filter.\n   * @see https://docs.microsoft.com/rest/api/storageservices/get-block-list\n   *\n   * @param listType - Specifies whether to return the list of committed blocks,\n   *                                        the list of uncommitted blocks, or both lists together.\n   * @param options - Options to the Block Blob Get Block List operation.\n   * @returns Response data for the Block Blob Get Block List operation.\n   */\n  public async getBlockList(\n    listType: BlockListType,\n    options: BlockBlobGetBlockListOptions = {},\n  ): Promise<BlockBlobGetBlockListResponse> {\n    return tracingClient.withSpan(\n      \"BlockBlobClient-getBlockList\",\n      options,\n      async (updatedOptions) => {\n        const res = assertResponse<\n          BlockBlobGetBlockListResponseInternal,\n          BlockBlobGetBlockListHeaders\n        >(\n          await this.blockBlobContext.getBlockList(listType, {\n            abortSignal: options.abortSignal,\n            leaseAccessConditions: options.conditions,\n            modifiedAccessConditions: {\n              ...options.conditions,\n              ifTags: options.conditions?.tagConditions,\n            },\n            tracingOptions: updatedOptions.tracingOptions,\n          }),\n        );\n\n        if (!res.committedBlocks) {\n          res.committedBlocks = [];\n        }\n\n        if (!res.uncommittedBlocks) {\n          res.uncommittedBlocks = [];\n        }\n\n        return res;\n      },\n    );\n  }\n\n  // High level functions\n\n  /**\n   * Uploads a Buffer(Node.js)/Blob(browsers)/ArrayBuffer/ArrayBufferView object to a BlockBlob.\n   *\n   * When data length is no more than the specifiled {@link BlockBlobParallelUploadOptions.maxSingleShotSize} (default is\n   * {@link BLOCK_BLOB_MAX_UPLOAD_BLOB_BYTES}), this method will use 1 {@link upload} call to finish the upload.\n   * Otherwise, this method will call {@link stageBlock} to upload blocks, and finally call {@link commitBlockList}\n   * to commit the block list.\n   *\n   * A common {@link BlockBlobParallelUploadOptions.blobHTTPHeaders} option to set is\n   * `blobContentType`, enabling the browser to provide\n   * functionality based on file type.\n   *\n   * @param data - Buffer(Node.js), Blob, ArrayBuffer or ArrayBufferView\n   * @param options -\n   */\n  public async uploadData(\n    data: Buffer | Blob | ArrayBuffer | ArrayBufferView,\n    options: BlockBlobParallelUploadOptions = {},\n  ): Promise<BlobUploadCommonResponse> {\n    return tracingClient.withSpan(\"BlockBlobClient-uploadData\", options, async (updatedOptions) => {\n      if (isNode) {\n        let buffer: Buffer;\n        if (data instanceof Buffer) {\n          buffer = data;\n        } else if (data instanceof ArrayBuffer) {\n          buffer = Buffer.from(data);\n        } else {\n          data = data as ArrayBufferView;\n          buffer = Buffer.from(data.buffer, data.byteOffset, data.byteLength);\n        }\n\n        return this.uploadSeekableInternal(\n          (offset: number, size: number): Buffer => buffer.slice(offset, offset + size),\n          buffer.byteLength,\n          updatedOptions,\n        );\n      } else {\n        const browserBlob = new Blob([data]);\n        return this.uploadSeekableInternal(\n          (offset: number, size: number): Blob => browserBlob.slice(offset, offset + size),\n          browserBlob.size,\n          updatedOptions,\n        );\n      }\n    });\n  }\n\n  /**\n   * ONLY AVAILABLE IN BROWSERS.\n   *\n   * Uploads a browser Blob/File/ArrayBuffer/ArrayBufferView object to block blob.\n   *\n   * When buffer length lesser than or equal to 256MB, this method will use 1 upload call to finish the upload.\n   * Otherwise, this method will call {@link stageBlock} to upload blocks, and finally call\n   * {@link commitBlockList} to commit the block list.\n   *\n   * A common {@link BlockBlobParallelUploadOptions.blobHTTPHeaders} option to set is\n   * `blobContentType`, enabling the browser to provide\n   * functionality based on file type.\n   *\n   * @deprecated Use {@link uploadData} instead.\n   *\n   * @param browserData - Blob, File, ArrayBuffer or ArrayBufferView\n   * @param options - Options to upload browser data.\n   * @returns Response data for the Blob Upload operation.\n   */\n  public async uploadBrowserData(\n    browserData: Blob | ArrayBuffer | ArrayBufferView,\n    options: BlockBlobParallelUploadOptions = {},\n  ): Promise<BlobUploadCommonResponse> {\n    return tracingClient.withSpan(\n      \"BlockBlobClient-uploadBrowserData\",\n      options,\n      async (updatedOptions) => {\n        const browserBlob = new Blob([browserData]);\n        return this.uploadSeekableInternal(\n          (offset: number, size: number): Blob => browserBlob.slice(offset, offset + size),\n          browserBlob.size,\n          updatedOptions,\n        );\n      },\n    );\n  }\n\n  /**\n   *\n   * Uploads data to block blob. Requires a bodyFactory as the data source,\n   * which need to return a {@link HttpRequestBody} object with the offset and size provided.\n   *\n   * When data length is no more than the specified {@link BlockBlobParallelUploadOptions.maxSingleShotSize} (default is\n   * {@link BLOCK_BLOB_MAX_UPLOAD_BLOB_BYTES}), this method will use 1 {@link upload} call to finish the upload.\n   * Otherwise, this method will call {@link stageBlock} to upload blocks, and finally call {@link commitBlockList}\n   * to commit the block list.\n   *\n   * @param bodyFactory -\n   * @param size - size of the data to upload.\n   * @param options - Options to Upload to Block Blob operation.\n   * @returns Response data for the Blob Upload operation.\n   */\n  private async uploadSeekableInternal(\n    bodyFactory: (offset: number, size: number) => HttpRequestBody,\n    size: number,\n    options: BlockBlobParallelUploadOptions = {},\n  ): Promise<BlobUploadCommonResponse> {\n    let blockSize = options.blockSize ?? 0;\n    if (blockSize < 0 || blockSize > BLOCK_BLOB_MAX_STAGE_BLOCK_BYTES) {\n      throw new RangeError(\n        `blockSize option must be >= 0 and <= ${BLOCK_BLOB_MAX_STAGE_BLOCK_BYTES}`,\n      );\n    }\n\n    const maxSingleShotSize = options.maxSingleShotSize ?? BLOCK_BLOB_MAX_UPLOAD_BLOB_BYTES;\n\n    if (maxSingleShotSize < 0 || maxSingleShotSize > BLOCK_BLOB_MAX_UPLOAD_BLOB_BYTES) {\n      throw new RangeError(\n        `maxSingleShotSize option must be >= 0 and <= ${BLOCK_BLOB_MAX_UPLOAD_BLOB_BYTES}`,\n      );\n    }\n\n    if (blockSize === 0) {\n      if (size > BLOCK_BLOB_MAX_STAGE_BLOCK_BYTES * BLOCK_BLOB_MAX_BLOCKS) {\n        throw new RangeError(`${size} is too larger to upload to a block blob.`);\n      }\n      if (size > maxSingleShotSize) {\n        blockSize = Math.ceil(size / BLOCK_BLOB_MAX_BLOCKS);\n        if (blockSize < DEFAULT_BLOB_DOWNLOAD_BLOCK_BYTES) {\n          blockSize = DEFAULT_BLOB_DOWNLOAD_BLOCK_BYTES;\n        }\n      }\n    }\n    if (!options.blobHTTPHeaders) {\n      options.blobHTTPHeaders = {};\n    }\n    if (!options.conditions) {\n      options.conditions = {};\n    }\n\n    return tracingClient.withSpan(\n      \"BlockBlobClient-uploadSeekableInternal\",\n      options,\n      async (updatedOptions) => {\n        if (size <= maxSingleShotSize) {\n          return assertResponse(await this.upload(bodyFactory(0, size), size, updatedOptions));\n        }\n\n        const numBlocks: number = Math.floor((size - 1) / blockSize) + 1;\n        if (numBlocks > BLOCK_BLOB_MAX_BLOCKS) {\n          throw new RangeError(\n            `The buffer's size is too big or the BlockSize is too small;` +\n              `the number of blocks must be <= ${BLOCK_BLOB_MAX_BLOCKS}`,\n          );\n        }\n\n        const blockList: string[] = [];\n        const blockIDPrefix = randomUUID();\n        let transferProgress: number = 0;\n\n        const batch = new Batch(options.concurrency);\n        for (let i = 0; i < numBlocks; i++) {\n          batch.addOperation(async (): Promise<any> => {\n            const blockID = generateBlockID(blockIDPrefix, i);\n            const start = blockSize * i;\n            const end = i === numBlocks - 1 ? size : start + blockSize;\n            const contentLength = end - start;\n            blockList.push(blockID);\n            await this.stageBlock(blockID, bodyFactory(start, contentLength), contentLength, {\n              abortSignal: options.abortSignal,\n              conditions: options.conditions,\n              encryptionScope: options.encryptionScope,\n              tracingOptions: updatedOptions.tracingOptions,\n            });\n            // Update progress after block is successfully uploaded to server, in case of block trying\n            // TODO: Hook with convenience layer progress event in finer level\n            transferProgress += contentLength;\n            if (options.onProgress) {\n              options.onProgress!({\n                loadedBytes: transferProgress,\n              });\n            }\n          });\n        }\n        await batch.do();\n\n        return this.commitBlockList(blockList, updatedOptions);\n      },\n    );\n  }\n\n  /**\n   * ONLY AVAILABLE IN NODE.JS RUNTIME.\n   *\n   * Uploads a local file in blocks to a block blob.\n   *\n   * When file size lesser than or equal to 256MB, this method will use 1 upload call to finish the upload.\n   * Otherwise, this method will call stageBlock to upload blocks, and finally call commitBlockList\n   * to commit the block list.\n   *\n   * @param filePath - Full path of local file\n   * @param options - Options to Upload to Block Blob operation.\n   * @returns Response data for the Blob Upload operation.\n   */\n  public async uploadFile(\n    filePath: string,\n    options: BlockBlobParallelUploadOptions = {},\n  ): Promise<BlobUploadCommonResponse> {\n    return tracingClient.withSpan(\"BlockBlobClient-uploadFile\", options, async (updatedOptions) => {\n      const size = (await fsStat(filePath)).size;\n      return this.uploadSeekableInternal(\n        (offset, count) => {\n          return () =>\n            fsCreateReadStream(filePath, {\n              autoClose: true,\n              end: count ? offset + count - 1 : Infinity,\n              start: offset,\n            });\n        },\n        size,\n        {\n          ...options,\n          tracingOptions: updatedOptions.tracingOptions,\n        },\n      );\n    });\n  }\n\n  /**\n   * ONLY AVAILABLE IN NODE.JS RUNTIME.\n   *\n   * Uploads a Node.js Readable stream into block blob.\n   *\n   * PERFORMANCE IMPROVEMENT TIPS:\n   * * Input stream highWaterMark is better to set a same value with bufferSize\n   *    parameter, which will avoid Buffer.concat() operations.\n   *\n   * @param stream - Node.js Readable stream\n   * @param bufferSize - Size of every buffer allocated, also the block size in the uploaded block blob. Default value is 8MB\n   * @param maxConcurrency -  Max concurrency indicates the max number of buffers that can be allocated,\n   *                                 positive correlation with max uploading concurrency. Default value is 5\n   * @param options - Options to Upload Stream to Block Blob operation.\n   * @returns Response data for the Blob Upload operation.\n   */\n  public async uploadStream(\n    stream: Readable,\n    bufferSize: number = DEFAULT_BLOCK_BUFFER_SIZE_BYTES,\n    maxConcurrency: number = 5,\n    options: BlockBlobUploadStreamOptions = {},\n  ): Promise<BlobUploadCommonResponse> {\n    if (!options.blobHTTPHeaders) {\n      options.blobHTTPHeaders = {};\n    }\n    if (!options.conditions) {\n      options.conditions = {};\n    }\n\n    return tracingClient.withSpan(\n      \"BlockBlobClient-uploadStream\",\n      options,\n      async (updatedOptions) => {\n        let blockNum = 0;\n        const blockIDPrefix = randomUUID();\n        let transferProgress: number = 0;\n        const blockList: string[] = [];\n\n        const scheduler = new BufferScheduler(\n          stream,\n          bufferSize,\n          maxConcurrency,\n          async (body, length) => {\n            const blockID = generateBlockID(blockIDPrefix, blockNum);\n            blockList.push(blockID);\n            blockNum++;\n\n            await this.stageBlock(blockID, body, length, {\n              customerProvidedKey: options.customerProvidedKey,\n              conditions: options.conditions,\n              encryptionScope: options.encryptionScope,\n              tracingOptions: updatedOptions.tracingOptions,\n            });\n\n            // Update progress after block is successfully uploaded to server, in case of block trying\n            transferProgress += length;\n            if (options.onProgress) {\n              options.onProgress({ loadedBytes: transferProgress });\n            }\n          },\n          // concurrency should set a smaller value than maxConcurrency, which is helpful to\n          // reduce the possibility when a outgoing handler waits for stream data, in\n          // this situation, outgoing handlers are blocked.\n          // Outgoing queue shouldn't be empty.\n          Math.ceil((maxConcurrency / 4) * 3),\n        );\n        await scheduler.do();\n\n        return assertResponse(\n          await this.commitBlockList(blockList, {\n            ...options,\n            tracingOptions: updatedOptions.tracingOptions,\n          }),\n        );\n      },\n    );\n  }\n}\n\n/**\n * Options to configure the {@link PageBlobClient.create} operation.\n */\nexport interface PageBlobCreateOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * Conditions to meet when creating a page blob.\n   */\n  conditions?: BlobRequestConditions;\n  /**\n   * A user-controlled value that can be used to track requests.\n   * The value must be between 0 and 2^63 - 1. The default value is 0.\n   */\n  blobSequenceNumber?: number;\n  /**\n   * HTTP headers to set when creating a page blob.\n   */\n  blobHTTPHeaders?: BlobHTTPHeaders;\n  /**\n   * A collection of key-value string pair to associate with the blob when creating append blobs.\n   */\n  metadata?: Metadata;\n  /**\n   * Customer Provided Key Info.\n   */\n  customerProvidedKey?: CpkInfo;\n  /**\n   * Optional. Version 2019-07-07 and later.  Specifies the name of the encryption scope to use to\n   * encrypt the data provided in the request. If not specified, encryption is performed with the\n   * default account encryption scope.  For more information, see Encryption at Rest for Azure\n   * Storage Services.\n   */\n  encryptionScope?: string;\n  /**\n   * Optional. Specifies immutability policy for a blob.\n   * Note that is parameter is only applicable to a blob within a container that\n   * has version level worm enabled.\n   */\n  immutabilityPolicy?: BlobImmutabilityPolicy;\n  /**\n   * Optional. Indicates if a legal hold should be placed on the blob.\n   * Note that is parameter is only applicable to a blob within a container that\n   * has version level worm enabled.\n   */\n  legalHold?: boolean;\n  /**\n   * Access tier.\n   * More Details - https://docs.microsoft.com/en-us/azure/storage/blobs/storage-blob-storage-tiers\n   */\n  tier?: PremiumPageBlobTier | string;\n  /**\n   * Blob tags.\n   */\n  tags?: Tags;\n}\n\n/**\n * Options to configure the {@link PageBlobClient.createIfNotExists} operation.\n */\nexport interface PageBlobCreateIfNotExistsOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * A user-controlled value that can be used to track requests.\n   * The value must be between 0 and 2^63 - 1. The default value is 0.\n   */\n  blobSequenceNumber?: number;\n  /**\n   * HTTP headers to set when creating a page blob.\n   */\n  blobHTTPHeaders?: BlobHTTPHeaders;\n  /**\n   * A collection of key-value string pair to associate with the blob when creating append blobs.\n   */\n  metadata?: Metadata;\n  /**\n   * Customer Provided Key Info.\n   */\n  customerProvidedKey?: CpkInfo;\n  /**\n   * Optional. Version 2019-07-07 and later.  Specifies the name of the encryption scope to use to\n   * encrypt the data provided in the request. If not specified, encryption is performed with the\n   * default account encryption scope.  For more information, see Encryption at Rest for Azure\n   * Storage Services.\n   */\n  encryptionScope?: string;\n  /**\n   * Optional. Specifies immutability policy for a blob.\n   * Note that is parameter is only applicable to a blob within a container that\n   * has version level worm enabled.\n   */\n  immutabilityPolicy?: BlobImmutabilityPolicy;\n  /**\n   * Optional. Indicates if a legal hold should be placed on the blob.\n   * Note that is parameter is only applicable to a blob within a container that\n   * has version level worm enabled.\n   */\n  legalHold?: boolean;\n  /**\n   * Access tier.\n   * More Details - https://docs.microsoft.com/en-us/azure/storage/blobs/storage-blob-storage-tiers\n   */\n  tier?: PremiumPageBlobTier | string;\n}\n\n/**\n * Options to configure the {@link PageBlobClient.uploadPages} operation.\n */\nexport interface PageBlobUploadPagesOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * Conditions to meet when uploading pages.\n   */\n  conditions?: PageBlobRequestConditions;\n  /**\n   * Callback to receive events on the progress of upload pages operation.\n   */\n  onProgress?: (progress: TransferProgressEvent) => void;\n  /**\n   * An MD5 hash of the content. This hash is used to verify the integrity of the content during transport.\n   * When this is specified, the storage service compares the hash of the content that has arrived with this value.\n   *\n   * transactionalContentMD5 and transactionalContentCrc64 cannot be set at same time.\n   */\n  transactionalContentMD5?: Uint8Array;\n  /**\n   * A CRC64 hash of the content. This hash is used to verify the integrity of the content during transport.\n   * When this is specified, the storage service compares the hash of the content that has arrived with this value.\n   *\n   * transactionalContentMD5 and transactionalContentCrc64 cannot be set at same time.\n   */\n  transactionalContentCrc64?: Uint8Array;\n  /**\n   * Customer Provided Key Info.\n   */\n  customerProvidedKey?: CpkInfo;\n  /**\n   * Optional. Version 2019-07-07 and later.  Specifies the name of the encryption scope to use to\n   * encrypt the data provided in the request. If not specified, encryption is performed with the\n   * default account encryption scope.  For more information, see Encryption at Rest for Azure\n   * Storage Services.\n   */\n  encryptionScope?: string;\n}\n\n/**\n * Options to configure the {@link PageBlobClient.clearPages} operation.\n */\nexport interface PageBlobClearPagesOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * Conditions to meet when clearing pages.\n   */\n  conditions?: PageBlobRequestConditions;\n  /**\n   * Customer Provided Key Info.\n   */\n  customerProvidedKey?: CpkInfo;\n  /**\n   * Optional. Version 2019-07-07 and later.  Specifies the name of the encryption scope to use to\n   * encrypt the data provided in the request. If not specified, encryption is performed with the\n   * default account encryption scope.  For more information, see Encryption at Rest for Azure\n   * Storage Services.\n   */\n  encryptionScope?: string;\n}\n\n/**\n * Options to configure the {@link PageBlobClient.getPageRanges} operation.\n */\nexport interface PageBlobGetPageRangesOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * Conditions to meet when getting page ranges.\n   */\n  conditions?: BlobRequestConditions;\n}\n\n/**\n * Options to configure page blob - get page ranges segment operations.\n *\n * See:\n * - {@link PageBlobClient.listPageRangesSegment}\n * - {@link PageBlobClient.listPageRangeItemSegments}\n * - {@link PageBlobClient.listPageRangeItems}\n */\ninterface PageBlobListPageRangesSegmentOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * Conditions to meet when getting page ranges.\n   */\n  conditions?: BlobRequestConditions;\n  /**\n   * Specifies the maximum number of containers\n   * to return. If the request does not specify maxPageSize, or specifies a\n   * value greater than 5000, the server will return up to 5000 items. Note\n   * that if the listing operation crosses a partition boundary, then the\n   * service will return a continuation token for retrieving the remainder of\n   * the results. For this reason, it is possible that the service will return\n   * fewer results than specified by maxPageSize, or than the default of 5000.\n   */\n  maxPageSize?: number;\n}\n\n/**\n * Options to configure the {@link PageBlobClient.listPageRanges} operation.\n */\nexport interface PageBlobListPageRangesOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * Conditions to meet when getting page ranges.\n   */\n  conditions?: BlobRequestConditions;\n}\n\n/**\n * Options to configure the {@link PageBlobClient.getRangesDiff} operation.\n */\nexport interface PageBlobGetPageRangesDiffOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * Conditions to meet when getting page ranges diff.\n   */\n  conditions?: BlobRequestConditions;\n  /**\n   * (unused)\n   */\n  range?: string;\n}\n\n/**\n * Options to configure page blob - get page ranges diff segment operations.\n *\n * See:\n * - {@link PageBlobClient.listPageRangesDiffSegment}\n * - {@link PageBlobClient.listPageRangeDiffItemSegments}\n * - {@link PageBlobClient.listPageRangeDiffItems}\n */\ninterface PageBlobListPageRangesDiffSegmentOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * Conditions to meet when getting page ranges.\n   */\n  conditions?: BlobRequestConditions;\n  /**\n   * Specifies the maximum number of containers\n   * to return. If the request does not specify maxPageSize, or specifies a\n   * value greater than 5000, the server will return up to 5000 items. Note\n   * that if the listing operation crosses a partition boundary, then the\n   * service will return a continuation token for retrieving the remainder of\n   * the results. For this reason, it is possible that the service will return\n   * fewer results than specified by maxPageSize, or than the default of 5000.\n   */\n  maxPageSize?: number;\n}\n\n/**\n * Options to configure the {@link PageBlobClient.listPageRangesDiff} operation.\n */\nexport interface PageBlobListPageRangesDiffOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * Conditions to meet when getting page ranges diff.\n   */\n  conditions?: BlobRequestConditions;\n}\n\n/**\n * Options to configure {@link PageBlobClient.resize} operation.\n */\nexport interface PageBlobResizeOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * Conditions to meet when resizing a page blob.\n   */\n  conditions?: BlobRequestConditions;\n  /**\n   * Optional. Version 2019-07-07 and later.  Specifies the name of the encryption scope to use to\n   * encrypt the data provided in the request. If not specified, encryption is performed with the\n   * default account encryption scope.  For more information, see Encryption at Rest for Azure\n   * Storage Services.\n   */\n  encryptionScope?: string;\n}\n\n/**\n * Options to configure {@link PageBlobClient.updateSequenceNumber} operation.\n */\nexport interface PageBlobUpdateSequenceNumberOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * Conditions to meet when updating sequence number.\n   */\n  conditions?: BlobRequestConditions;\n}\n\n/**\n * Options to configure {@link PageBlobClient.startCopyIncremental} operation.\n */\nexport interface PageBlobStartCopyIncrementalOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * Conditions to meet when starting a copy incremental operation.\n   */\n  conditions?: ModifiedAccessConditions;\n}\n\n/**\n * Options to configure {@link PageBlobClient.uploadPagesFromURL} operation.\n */\nexport interface PageBlobUploadPagesFromURLOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * Conditions to meet when updating sequence number.\n   */\n  conditions?: PageBlobRequestConditions;\n  /**\n   * Conditions to meet for the source Azure Blob/File when copying from a URL to the blob.\n   */\n  sourceConditions?: MatchConditions & ModificationConditions;\n  /**\n   * An MD5 hash of the content from the URI.\n   * This hash is used to verify the integrity of the content during transport of the data from the URI.\n   * When this is specified, the storage service compares the hash of the content that has arrived from the copy-source with this value.\n   *\n   * sourceContentMD5 and sourceContentCrc64 cannot be set at same time.\n   */\n  sourceContentMD5?: Uint8Array;\n  /**\n   * A CRC64 hash of the content from the URI.\n   * This hash is used to verify the integrity of the content during transport of the data from the URI.\n   * When this is specified, the storage service compares the hash of the content that has arrived from the copy-source with this value.\n   *\n   * sourceContentMD5 and sourceContentCrc64 cannot be set at same time.\n   */\n  sourceContentCrc64?: Uint8Array;\n  /**\n   * Customer Provided Key Info.\n   */\n  customerProvidedKey?: CpkInfo;\n  /**\n   * Optional. Version 2019-07-07 and later.  Specifies the name of the encryption scope to use to\n   * encrypt the data provided in the request. If not specified, encryption is performed with the\n   * default account encryption scope.  For more information, see Encryption at Rest for Azure\n   * Storage Services.\n   */\n  encryptionScope?: string;\n  /**\n   * Only Bearer type is supported. Credentials should be a valid OAuth access token to copy source.\n   */\n  sourceAuthorization?: HttpAuthorization;\n}\n\n/**\n * Contains response data for the {@link PageBlobClient.createIfNotExists} operation.\n */\nexport interface PageBlobCreateIfNotExistsResponse extends PageBlobCreateResponse {\n  /**\n   * Indicate whether the blob is successfully created. Is false when the blob is not changed as it already exists.\n   */\n  succeeded: boolean;\n}\n\n/**\n * PageBlobClient defines a set of operations applicable to page blobs.\n */\nexport class PageBlobClient extends BlobClient {\n  /**\n   * pageBlobsContext provided by protocol layer.\n   */\n  private pageBlobContext: PageBlob;\n\n  /**\n   *\n   * Creates an instance of PageBlobClient.\n   *\n   * @param connectionString - Account connection string or a SAS connection string of an Azure storage account.\n   *                                  [ Note - Account connection string can only be used in NODE.JS runtime. ]\n   *                                  Account connection string example -\n   *                                  `DefaultEndpointsProtocol=https;AccountName=myaccount;AccountKey=accountKey;EndpointSuffix=core.windows.net`\n   *                                  SAS connection string example -\n   *                                  `BlobEndpoint=https://myaccount.blob.core.windows.net/;QueueEndpoint=https://myaccount.queue.core.windows.net/;FileEndpoint=https://myaccount.file.core.windows.net/;TableEndpoint=https://myaccount.table.core.windows.net/;SharedAccessSignature=sasString`\n   * @param containerName - Container name.\n   * @param blobName - Blob name.\n   * @param options - Optional. Options to configure the HTTP pipeline.\n   */\n  constructor(\n    connectionString: string,\n    containerName: string,\n    blobName: string,\n    // Legacy, no fix for eslint error without breaking. Disable it for this interface.\n    /* eslint-disable-next-line @azure/azure-sdk/ts-naming-options*/\n    options?: StoragePipelineOptions,\n  );\n  /**\n   * Creates an instance of PageBlobClient.\n   * This method accepts an encoded URL or non-encoded URL pointing to a blob.\n   * Encoded URL string will NOT be escaped twice, only special characters in URL path will be escaped.\n   * If a blob name includes ? or %, blob name must be encoded in the URL.\n   *\n   * @param url - A Client string pointing to Azure Storage page blob, such as\n   *                     \"https://myaccount.blob.core.windows.net/mycontainer/pageblob\". You can append a SAS\n   *                     if using AnonymousCredential, such as \"https://myaccount.blob.core.windows.net/mycontainer/pageblob?sasString\".\n   * @param credential -  Such as AnonymousCredential, StorageSharedKeyCredential or any credential from the `@azure/identity` package to authenticate requests to the service. You can also provide an object that implements the TokenCredential interface. If not specified, AnonymousCredential is used.\n   * @param options - Optional. Options to configure the HTTP pipeline.\n   */\n  constructor(\n    url: string,\n    credential: StorageSharedKeyCredential | AnonymousCredential | TokenCredential,\n    // Legacy, no fix for eslint error without breaking. Disable it for this interface.\n    /* eslint-disable-next-line @azure/azure-sdk/ts-naming-options*/\n    options?: StoragePipelineOptions,\n  );\n  /**\n   * Creates an instance of PageBlobClient.\n   *\n   * @param url - A URL string pointing to Azure Storage page blob, such as\n   *                     \"https://myaccount.blob.core.windows.net/mycontainer/pageblob\".\n   *                     You can append a SAS if using AnonymousCredential, such as\n   *                     \"https://myaccount.blob.core.windows.net/mycontainer/pageblob?sasString\".\n   *                     This method accepts an encoded URL or non-encoded URL pointing to a blob.\n   *                     Encoded URL string will NOT be escaped twice, only special characters in URL path will be escaped.\n   *                     However, if a blob name includes ? or %, blob name must be encoded in the URL.\n   *                     Such as a blob named \"my?blob%\", the URL should be \"https://myaccount.blob.core.windows.net/mycontainer/my%3Fblob%25\".\n   * @param pipeline - Call newPipeline() to create a default\n   *                            pipeline, or provide a customized pipeline.\n   */\n  constructor(url: string, pipeline: PipelineLike);\n  constructor(\n    urlOrConnectionString: string,\n    credentialOrPipelineOrContainerName:\n      | string\n      | StorageSharedKeyCredential\n      | AnonymousCredential\n      | TokenCredential\n      | PipelineLike,\n    blobNameOrOptions?: string | StoragePipelineOptions,\n    // Legacy, no fix for eslint error without breaking. Disable it for this interface.\n    /* eslint-disable-next-line @azure/azure-sdk/ts-naming-options*/\n    options?: StoragePipelineOptions,\n  ) {\n    // In TypeScript we cannot simply pass all parameters to super() like below so have to duplicate the code instead.\n    //   super(s, credentialOrPipelineOrContainerNameOrOptions, blobNameOrOptions, options);\n    let pipeline: PipelineLike;\n    let url: string;\n    options = options || {};\n    if (isPipelineLike(credentialOrPipelineOrContainerName)) {\n      // (url: string, pipeline: Pipeline)\n      url = urlOrConnectionString;\n      pipeline = credentialOrPipelineOrContainerName;\n    } else if (\n      (isNode && credentialOrPipelineOrContainerName instanceof StorageSharedKeyCredential) ||\n      credentialOrPipelineOrContainerName instanceof AnonymousCredential ||\n      isTokenCredential(credentialOrPipelineOrContainerName)\n    ) {\n      // (url: string, credential?: StorageSharedKeyCredential | AnonymousCredential | TokenCredential, options?: StoragePipelineOptions)\n      url = urlOrConnectionString;\n      options = blobNameOrOptions as StoragePipelineOptions;\n      pipeline = newPipeline(credentialOrPipelineOrContainerName, options);\n    } else if (\n      !credentialOrPipelineOrContainerName &&\n      typeof credentialOrPipelineOrContainerName !== \"string\"\n    ) {\n      // (url: string, credential?: StorageSharedKeyCredential | AnonymousCredential | TokenCredential, options?: StoragePipelineOptions)\n      // The second parameter is undefined. Use anonymous credential.\n      url = urlOrConnectionString;\n      pipeline = newPipeline(new AnonymousCredential(), options);\n    } else if (\n      credentialOrPipelineOrContainerName &&\n      typeof credentialOrPipelineOrContainerName === \"string\" &&\n      blobNameOrOptions &&\n      typeof blobNameOrOptions === \"string\"\n    ) {\n      // (connectionString: string, containerName: string, blobName: string, options?: StoragePipelineOptions)\n      const containerName = credentialOrPipelineOrContainerName;\n      const blobName = blobNameOrOptions;\n\n      const extractedCreds = extractConnectionStringParts(urlOrConnectionString);\n      if (extractedCreds.kind === \"AccountConnString\") {\n        if (isNode) {\n          const sharedKeyCredential = new StorageSharedKeyCredential(\n            extractedCreds.accountName!,\n            extractedCreds.accountKey,\n          );\n          url = appendToURLPath(\n            appendToURLPath(extractedCreds.url, encodeURIComponent(containerName)),\n            encodeURIComponent(blobName),\n          );\n\n          if (!options.proxyOptions) {\n            options.proxyOptions = getDefaultProxySettings(extractedCreds.proxyUri);\n          }\n\n          pipeline = newPipeline(sharedKeyCredential, options);\n        } else {\n          throw new Error(\"Account connection string is only supported in Node.js environment\");\n        }\n      } else if (extractedCreds.kind === \"SASConnString\") {\n        url =\n          appendToURLPath(\n            appendToURLPath(extractedCreds.url, encodeURIComponent(containerName)),\n            encodeURIComponent(blobName),\n          ) +\n          \"?\" +\n          extractedCreds.accountSas;\n        pipeline = newPipeline(new AnonymousCredential(), options);\n      } else {\n        throw new Error(\n          \"Connection string must be either an Account connection string or a SAS connection string\",\n        );\n      }\n    } else {\n      throw new Error(\"Expecting non-empty strings for containerName and blobName parameters\");\n    }\n    super(url, pipeline);\n    this.pageBlobContext = this.storageClientContext.pageBlob;\n  }\n\n  /**\n   * Creates a new PageBlobClient object identical to the source but with the\n   * specified snapshot timestamp.\n   * Provide \"\" will remove the snapshot and return a Client to the base blob.\n   *\n   * @param snapshot - The snapshot timestamp.\n   * @returns A new PageBlobClient object identical to the source but with the specified snapshot timestamp.\n   */\n  public withSnapshot(snapshot: string): PageBlobClient {\n    return new PageBlobClient(\n      setURLParameter(\n        this.url,\n        URLConstants.Parameters.SNAPSHOT,\n        snapshot.length === 0 ? undefined : snapshot,\n      ),\n      this.pipeline,\n    );\n  }\n\n  /**\n   * Creates a page blob of the specified length. Call uploadPages to upload data\n   * data to a page blob.\n   * @see https://docs.microsoft.com/rest/api/storageservices/put-blob\n   *\n   * @param size - size of the page blob.\n   * @param options - Options to the Page Blob Create operation.\n   * @returns Response data for the Page Blob Create operation.\n   */\n  public async create(\n    size: number,\n    options: PageBlobCreateOptions = {},\n  ): Promise<PageBlobCreateResponse> {\n    options.conditions = options.conditions || {};\n    ensureCpkIfSpecified(options.customerProvidedKey, this.isHttps);\n    return tracingClient.withSpan(\"PageBlobClient-create\", options, async (updatedOptions) => {\n      return assertResponse<PageBlobCreateHeaders, PageBlobCreateHeaders>(\n        await this.pageBlobContext.create(0, size, {\n          abortSignal: options.abortSignal,\n          blobHttpHeaders: options.blobHTTPHeaders,\n          blobSequenceNumber: options.blobSequenceNumber,\n          leaseAccessConditions: options.conditions,\n          metadata: options.metadata,\n          modifiedAccessConditions: {\n            ...options.conditions,\n            ifTags: options.conditions?.tagConditions,\n          },\n          cpkInfo: options.customerProvidedKey,\n          encryptionScope: options.encryptionScope,\n          immutabilityPolicyExpiry: options.immutabilityPolicy?.expiriesOn,\n          immutabilityPolicyMode: options.immutabilityPolicy?.policyMode,\n          legalHold: options.legalHold,\n          tier: toAccessTier(options.tier),\n          blobTagsString: toBlobTagsString(options.tags),\n          tracingOptions: updatedOptions.tracingOptions,\n        }),\n      );\n    });\n  }\n\n  /**\n   * Creates a page blob of the specified length. Call uploadPages to upload data\n   * data to a page blob. If the blob with the same name already exists, the content\n   * of the existing blob will remain unchanged.\n   * @see https://docs.microsoft.com/rest/api/storageservices/put-blob\n   *\n   * @param size - size of the page blob.\n   * @param options -\n   */\n  public async createIfNotExists(\n    size: number,\n    options: PageBlobCreateIfNotExistsOptions = {},\n  ): Promise<PageBlobCreateIfNotExistsResponse> {\n    return tracingClient.withSpan(\n      \"PageBlobClient-createIfNotExists\",\n      options,\n      async (updatedOptions) => {\n        try {\n          const conditions = { ifNoneMatch: ETagAny };\n          const res = assertResponse(\n            await this.create(size, {\n              ...options,\n              conditions,\n              tracingOptions: updatedOptions.tracingOptions,\n            }),\n          );\n          return {\n            succeeded: true,\n            ...res,\n            _response: res._response, // _response is made non-enumerable\n          };\n        } catch (e: any) {\n          if (e.details?.errorCode === \"BlobAlreadyExists\") {\n            return {\n              succeeded: false,\n              ...e.response?.parsedHeaders,\n              _response: e.response,\n            };\n          }\n\n          throw e;\n        }\n      },\n    );\n  }\n\n  /**\n   * Writes 1 or more pages to the page blob. The start and end offsets must be a multiple of 512.\n   * @see https://docs.microsoft.com/rest/api/storageservices/put-page\n   *\n   * @param body - Data to upload\n   * @param offset - Offset of destination page blob\n   * @param count - Content length of the body, also number of bytes to be uploaded\n   * @param options - Options to the Page Blob Upload Pages operation.\n   * @returns Response data for the Page Blob Upload Pages operation.\n   */\n  public async uploadPages(\n    body: HttpRequestBody,\n    offset: number,\n    count: number,\n    options: PageBlobUploadPagesOptions = {},\n  ): Promise<PageBlobUploadPagesResponse> {\n    options.conditions = options.conditions || {};\n    ensureCpkIfSpecified(options.customerProvidedKey, this.isHttps);\n    return tracingClient.withSpan(\"PageBlobClient-uploadPages\", options, async (updatedOptions) => {\n      return assertResponse<PageBlobUploadPagesHeaders, PageBlobUploadPagesHeaders>(\n        await this.pageBlobContext.uploadPages(count, body, {\n          abortSignal: options.abortSignal,\n          leaseAccessConditions: options.conditions,\n          modifiedAccessConditions: {\n            ...options.conditions,\n            ifTags: options.conditions?.tagConditions,\n          },\n          requestOptions: {\n            onUploadProgress: options.onProgress,\n          },\n          range: rangeToString({ offset, count }),\n          sequenceNumberAccessConditions: options.conditions,\n          transactionalContentMD5: options.transactionalContentMD5,\n          transactionalContentCrc64: options.transactionalContentCrc64,\n          cpkInfo: options.customerProvidedKey,\n          encryptionScope: options.encryptionScope,\n          tracingOptions: updatedOptions.tracingOptions,\n        }),\n      );\n    });\n  }\n\n  /**\n   * The Upload Pages operation writes a range of pages to a page blob where the\n   * contents are read from a URL.\n   * @see https://docs.microsoft.com/en-us/rest/api/storageservices/put-page-from-url\n   *\n   * @param sourceURL - Specify a URL to the copy source, Shared Access Signature(SAS) maybe needed for authentication\n   * @param sourceOffset - The source offset to copy from. Pass 0 to copy from the beginning of source page blob\n   * @param destOffset - Offset of destination page blob\n   * @param count - Number of bytes to be uploaded from source page blob\n   * @param options -\n   */\n  public async uploadPagesFromURL(\n    sourceURL: string,\n    sourceOffset: number,\n    destOffset: number,\n    count: number,\n    options: PageBlobUploadPagesFromURLOptions = {},\n  ): Promise<PageBlobUploadPagesFromURLResponse> {\n    options.conditions = options.conditions || {};\n    options.sourceConditions = options.sourceConditions || {};\n    ensureCpkIfSpecified(options.customerProvidedKey, this.isHttps);\n    return tracingClient.withSpan(\n      \"PageBlobClient-uploadPagesFromURL\",\n      options,\n      async (updatedOptions) => {\n        return assertResponse<PageBlobUploadPagesFromURLHeaders, PageBlobUploadPagesFromURLHeaders>(\n          await this.pageBlobContext.uploadPagesFromURL(\n            sourceURL,\n            rangeToString({ offset: sourceOffset, count }),\n            0,\n            rangeToString({ offset: destOffset, count }),\n            {\n              abortSignal: options.abortSignal,\n              sourceContentMD5: options.sourceContentMD5,\n              sourceContentCrc64: options.sourceContentCrc64,\n              leaseAccessConditions: options.conditions,\n              sequenceNumberAccessConditions: options.conditions,\n              modifiedAccessConditions: {\n                ...options.conditions,\n                ifTags: options.conditions?.tagConditions,\n              },\n              sourceModifiedAccessConditions: {\n                sourceIfMatch: options.sourceConditions?.ifMatch,\n                sourceIfModifiedSince: options.sourceConditions?.ifModifiedSince,\n                sourceIfNoneMatch: options.sourceConditions?.ifNoneMatch,\n                sourceIfUnmodifiedSince: options.sourceConditions?.ifUnmodifiedSince,\n              },\n              cpkInfo: options.customerProvidedKey,\n              encryptionScope: options.encryptionScope,\n              copySourceAuthorization: httpAuthorizationToString(options.sourceAuthorization),\n              tracingOptions: updatedOptions.tracingOptions,\n            },\n          ),\n        );\n      },\n    );\n  }\n\n  /**\n   * Frees the specified pages from the page blob.\n   * @see https://docs.microsoft.com/rest/api/storageservices/put-page\n   *\n   * @param offset - Starting byte position of the pages to clear.\n   * @param count - Number of bytes to clear.\n   * @param options - Options to the Page Blob Clear Pages operation.\n   * @returns Response data for the Page Blob Clear Pages operation.\n   */\n  public async clearPages(\n    offset: number = 0,\n    count?: number,\n    options: PageBlobClearPagesOptions = {},\n  ): Promise<PageBlobClearPagesResponse> {\n    options.conditions = options.conditions || {};\n    return tracingClient.withSpan(\"PageBlobClient-clearPages\", options, async (updatedOptions) => {\n      return assertResponse<PageBlobClearPagesHeaders, PageBlobClearPagesHeaders>(\n        await this.pageBlobContext.clearPages(0, {\n          abortSignal: options.abortSignal,\n          leaseAccessConditions: options.conditions,\n          modifiedAccessConditions: {\n            ...options.conditions,\n            ifTags: options.conditions?.tagConditions,\n          },\n          range: rangeToString({ offset, count }),\n          sequenceNumberAccessConditions: options.conditions,\n          cpkInfo: options.customerProvidedKey,\n          encryptionScope: options.encryptionScope,\n          tracingOptions: updatedOptions.tracingOptions,\n        }),\n      );\n    });\n  }\n\n  /**\n   * Returns the list of valid page ranges for a page blob or snapshot of a page blob.\n   * @see https://docs.microsoft.com/rest/api/storageservices/get-page-ranges\n   *\n   * @param offset - Starting byte position of the page ranges.\n   * @param count - Number of bytes to get.\n   * @param options - Options to the Page Blob Get Ranges operation.\n   * @returns Response data for the Page Blob Get Ranges operation.\n   */\n  public async getPageRanges(\n    offset: number = 0,\n    count?: number,\n    options: PageBlobGetPageRangesOptions = {},\n  ): Promise<PageBlobGetPageRangesResponse> {\n    options.conditions = options.conditions || {};\n    return tracingClient.withSpan(\n      \"PageBlobClient-getPageRanges\",\n      options,\n      async (updatedOptions) => {\n        const response = assertResponse<\n          PageBlobGetPageRangesResponseInternal,\n          PageBlobGetPageRangesHeaders,\n          PageListInternal\n        >(\n          await this.pageBlobContext.getPageRanges({\n            abortSignal: options.abortSignal,\n            leaseAccessConditions: options.conditions,\n            modifiedAccessConditions: {\n              ...options.conditions,\n              ifTags: options.conditions?.tagConditions,\n            },\n            range: rangeToString({ offset, count }),\n            tracingOptions: updatedOptions.tracingOptions,\n          }),\n        );\n        return rangeResponseFromModel(response);\n      },\n    );\n  }\n\n  /**\n   * getPageRangesSegment returns a single segment of page ranges starting from the\n   * specified Marker. Use an empty Marker to start enumeration from the beginning.\n   * After getting a segment, process it, and then call getPageRangesSegment again\n   * (passing the the previously-returned Marker) to get the next segment.\n   * @see https://docs.microsoft.com/rest/api/storageservices/get-page-ranges\n   *\n   * @param offset - Starting byte position of the page ranges.\n   * @param count - Number of bytes to get.\n   * @param marker - A string value that identifies the portion of the list to be returned with the next list operation.\n   * @param options - Options to PageBlob Get Page Ranges Segment operation.\n   */\n  private async listPageRangesSegment(\n    offset: number = 0,\n    count?: number,\n    marker?: string,\n    options: PageBlobListPageRangesSegmentOptions = {},\n  ): Promise<PageBlobGetPageRangesResponseModel> {\n    return tracingClient.withSpan(\n      \"PageBlobClient-getPageRangesSegment\",\n      options,\n      async (updatedOptions) => {\n        return assertResponse<\n          PageBlobGetPageRangesResponseInternal,\n          PageBlobGetPageRangesHeaders,\n          PageListInternal\n        >(\n          await this.pageBlobContext.getPageRanges({\n            abortSignal: options.abortSignal,\n            leaseAccessConditions: options.conditions,\n            modifiedAccessConditions: {\n              ...options.conditions,\n              ifTags: options.conditions?.tagConditions,\n            },\n            range: rangeToString({ offset, count }),\n            marker: marker,\n            maxPageSize: options.maxPageSize,\n            tracingOptions: updatedOptions.tracingOptions,\n          }),\n        );\n      },\n    );\n  }\n  /**\n   * Returns an AsyncIterableIterator for {@link PageBlobGetPageRangesResponseModel}\n   *\n   * @param offset - Starting byte position of the page ranges.\n   * @param count - Number of bytes to get.\n   * @param marker - A string value that identifies the portion of\n   *                          the get of page ranges to be returned with the next getting operation. The\n   *                          operation returns the ContinuationToken value within the response body if the\n   *                          getting operation did not return all page ranges remaining within the current page.\n   *                          The ContinuationToken value can be used as the value for\n   *                          the marker parameter in a subsequent call to request the next page of get\n   *                          items. The marker value is opaque to the client.\n   * @param options - Options to List Page Ranges operation.\n   */\n  private async *listPageRangeItemSegments(\n    offset: number = 0,\n    count?: number,\n    marker?: string,\n    options: PageBlobListPageRangesSegmentOptions = {},\n  ): AsyncIterableIterator<PageBlobGetPageRangesResponseModel> {\n    let getPageRangeItemSegmentsResponse;\n    if (!!marker || marker === undefined) {\n      do {\n        getPageRangeItemSegmentsResponse = await this.listPageRangesSegment(\n          offset,\n          count,\n          marker,\n          options,\n        );\n        marker = getPageRangeItemSegmentsResponse.continuationToken;\n        yield await getPageRangeItemSegmentsResponse;\n      } while (marker);\n    }\n  }\n\n  /**\n   * Returns an AsyncIterableIterator of {@link PageRangeInfo} objects\n   *\n   * @param offset - Starting byte position of the page ranges.\n   * @param count - Number of bytes to get.\n   * @param options - Options to List Page Ranges operation.\n   */\n  private async *listPageRangeItems(\n    offset: number = 0,\n    count?: number,\n    options: PageBlobListPageRangesSegmentOptions = {},\n  ): AsyncIterableIterator<PageRangeInfo> {\n    let marker: string | undefined;\n    for await (const getPageRangesSegment of this.listPageRangeItemSegments(\n      offset,\n      count,\n      marker,\n      options,\n    )) {\n      yield* ExtractPageRangeInfoItems(getPageRangesSegment);\n    }\n  }\n\n  /**\n   * Returns an async iterable iterator to list of page ranges for a page blob.\n   * @see https://docs.microsoft.com/rest/api/storageservices/get-page-ranges\n   *\n   *  .byPage() returns an async iterable iterator to list of page ranges for a page blob.\n   *\n   * Example using `for await` syntax:\n   *\n   * ```js\n   * // Get the pageBlobClient before you run these snippets,\n   * // Can be obtained from `blobServiceClient.getContainerClient(\"<your-container-name>\").getPageBlobClient(\"<your-blob-name>\");`\n   * let i = 1;\n   * for await (const pageRange of pageBlobClient.listPageRanges()) {\n   *   console.log(`Page range ${i++}: ${pageRange.start} - ${pageRange.end}`);\n   * }\n   * ```\n   *\n   * Example using `iter.next()`:\n   *\n   * ```js\n   * let i = 1;\n   * let iter = pageBlobClient.listPageRanges();\n   * let pageRangeItem = await iter.next();\n   * while (!pageRangeItem.done) {\n   *   console.log(`Page range ${i++}: ${pageRangeItem.value.start} - ${pageRangeItem.value.end}, IsClear: ${pageRangeItem.value.isClear}`);\n   *   pageRangeItem = await iter.next();\n   * }\n   * ```\n   *\n   * Example using `byPage()`:\n   *\n   * ```js\n   * // passing optional maxPageSize in the page settings\n   * let i = 1;\n   * for await (const response of pageBlobClient.listPageRanges().byPage({ maxPageSize: 20 })) {\n   *   for (const pageRange of response) {\n   *     console.log(`Page range ${i++}: ${pageRange.start} - ${pageRange.end}`);\n   *   }\n   * }\n   * ```\n   *\n   * Example using paging with a marker:\n   *\n   * ```js\n   * let i = 1;\n   * let iterator = pageBlobClient.listPageRanges().byPage({ maxPageSize: 2 });\n   * let response = (await iterator.next()).value;\n   *\n   * // Prints 2 page ranges\n   * for (const pageRange of response) {\n   *   console.log(`Page range ${i++}: ${pageRange.start} - ${pageRange.end}`);\n   * }\n   *\n   * // Gets next marker\n   * let marker = response.continuationToken;\n   *\n   * // Passing next marker as continuationToken\n   *\n   * iterator = pageBlobClient.listPageRanges().byPage({ continuationToken: marker, maxPageSize: 10 });\n   * response = (await iterator.next()).value;\n   *\n   * // Prints 10 page ranges\n   * for (const blob of response) {\n   *   console.log(`Page range ${i++}: ${pageRange.start} - ${pageRange.end}`);\n   * }\n   * ```\n   * @param offset - Starting byte position of the page ranges.\n   * @param count - Number of bytes to get.\n   * @param options - Options to the Page Blob Get Ranges operation.\n   * @returns An asyncIterableIterator that supports paging.\n   */\n  public listPageRanges(\n    offset: number = 0,\n    count?: number,\n    options: PageBlobListPageRangesOptions = {},\n  ): PagedAsyncIterableIterator<PageRangeInfo, PageBlobGetPageRangesResponseModel> {\n    options.conditions = options.conditions || {};\n    // AsyncIterableIterator to iterate over blobs\n    const iter = this.listPageRangeItems(offset, count, options);\n    return {\n      /**\n       * The next method, part of the iteration protocol\n       */\n      next() {\n        return iter.next();\n      },\n      /**\n       * The connection to the async iterator, part of the iteration protocol\n       */\n      [Symbol.asyncIterator]() {\n        return this;\n      },\n      /**\n       * Return an AsyncIterableIterator that works a page at a time\n       */\n      byPage: (settings: PageSettings = {}) => {\n        return this.listPageRangeItemSegments(offset, count, settings.continuationToken, {\n          maxPageSize: settings.maxPageSize,\n          ...options,\n        });\n      },\n    };\n  }\n\n  /**\n   * Gets the collection of page ranges that differ between a specified snapshot and this page blob.\n   * @see https://docs.microsoft.com/rest/api/storageservices/get-page-ranges\n   *\n   * @param offset - Starting byte position of the page blob\n   * @param count - Number of bytes to get ranges diff.\n   * @param prevSnapshot - Timestamp of snapshot to retrieve the difference.\n   * @param options - Options to the Page Blob Get Page Ranges Diff operation.\n   * @returns Response data for the Page Blob Get Page Range Diff operation.\n   */\n  public async getPageRangesDiff(\n    offset: number,\n    count: number,\n    prevSnapshot: string,\n    options: PageBlobGetPageRangesDiffOptions = {},\n  ): Promise<PageBlobGetPageRangesDiffResponse> {\n    options.conditions = options.conditions || {};\n    return tracingClient.withSpan(\n      \"PageBlobClient-getPageRangesDiff\",\n      options,\n      async (updatedOptions) => {\n        const result = assertResponse<\n          PageBlobGetPageRangesDiffResponseInternal,\n          PageBlobGetPageRangesDiffHeaders,\n          PageListInternal\n        >(\n          await this.pageBlobContext.getPageRangesDiff({\n            abortSignal: options.abortSignal,\n            leaseAccessConditions: options.conditions,\n            modifiedAccessConditions: {\n              ...options.conditions,\n              ifTags: options.conditions?.tagConditions,\n            },\n            prevsnapshot: prevSnapshot,\n            range: rangeToString({ offset, count }),\n            tracingOptions: updatedOptions.tracingOptions,\n          }),\n        );\n        return rangeResponseFromModel(result);\n      },\n    );\n  }\n\n  /**\n   * getPageRangesDiffSegment returns a single segment of page ranges starting from the\n   * specified Marker for difference between previous snapshot and the target page blob.\n   * Use an empty Marker to start enumeration from the beginning.\n   * After getting a segment, process it, and then call getPageRangesDiffSegment again\n   * (passing the the previously-returned Marker) to get the next segment.\n   * @see https://docs.microsoft.com/rest/api/storageservices/get-page-ranges\n   *\n   * @param offset - Starting byte position of the page ranges.\n   * @param count - Number of bytes to get.\n   * @param prevSnapshotOrUrl - Timestamp of snapshot to retrieve the difference or URL of snapshot to retrieve the difference.\n   * @param marker - A string value that identifies the portion of the get to be returned with the next get operation.\n   * @param options - Options to the Page Blob Get Page Ranges Diff operation.\n   */\n  private async listPageRangesDiffSegment(\n    offset: number,\n    count: number,\n    prevSnapshotOrUrl: string,\n    marker?: string,\n    options: PageBlobListPageRangesDiffSegmentOptions = {},\n  ): Promise<PageBlobGetPageRangesResponseModel> {\n    return tracingClient.withSpan(\n      \"PageBlobClient-getPageRangesDiffSegment\",\n      options,\n      async (updatedOptions) => {\n        return assertResponse<\n          PageBlobGetPageRangesResponseInternal,\n          PageBlobGetPageRangesHeaders,\n          PageListInternal\n        >(\n          await this.pageBlobContext.getPageRangesDiff({\n            abortSignal: options?.abortSignal,\n            leaseAccessConditions: options?.conditions,\n            modifiedAccessConditions: {\n              ...options?.conditions,\n              ifTags: options?.conditions?.tagConditions,\n            },\n            prevsnapshot: prevSnapshotOrUrl,\n            range: rangeToString({\n              offset: offset,\n              count: count,\n            }),\n            marker: marker,\n            maxPageSize: options?.maxPageSize,\n            tracingOptions: updatedOptions.tracingOptions,\n          }),\n        );\n      },\n    );\n  }\n  /**\n   * Returns an AsyncIterableIterator for {@link PageBlobGetPageRangesDiffResponseModel}\n   *\n   *\n   * @param offset - Starting byte position of the page ranges.\n   * @param count - Number of bytes to get.\n   * @param prevSnapshotOrUrl - Timestamp of snapshot to retrieve the difference or URL of snapshot to retrieve the difference.\n   * @param marker - A string value that identifies the portion of\n   *                          the get of page ranges to be returned with the next getting operation. The\n   *                          operation returns the ContinuationToken value within the response body if the\n   *                          getting operation did not return all page ranges remaining within the current page.\n   *                          The ContinuationToken value can be used as the value for\n   *                          the marker parameter in a subsequent call to request the next page of get\n   *                          items. The marker value is opaque to the client.\n   * @param options - Options to the Page Blob Get Page Ranges Diff operation.\n   */\n  private async *listPageRangeDiffItemSegments(\n    offset: number,\n    count: number,\n    prevSnapshotOrUrl: string,\n    marker?: string,\n    options?: PageBlobListPageRangesDiffSegmentOptions,\n  ): AsyncIterableIterator<PageBlobGetPageRangesDiffResponseModel> {\n    let getPageRangeItemSegmentsResponse: PageBlobGetPageRangesResponseModel;\n    if (!!marker || marker === undefined) {\n      do {\n        getPageRangeItemSegmentsResponse = await this.listPageRangesDiffSegment(\n          offset,\n          count,\n          prevSnapshotOrUrl,\n          marker,\n          options,\n        );\n        marker = getPageRangeItemSegmentsResponse.continuationToken;\n        yield await getPageRangeItemSegmentsResponse;\n      } while (marker);\n    }\n  }\n\n  /**\n   * Returns an AsyncIterableIterator of {@link PageRangeInfo} objects\n   *\n   * @param offset - Starting byte position of the page ranges.\n   * @param count - Number of bytes to get.\n   * @param prevSnapshotOrUrl - Timestamp of snapshot to retrieve the difference or URL of snapshot to retrieve the difference.\n   * @param options - Options to the Page Blob Get Page Ranges Diff operation.\n   */\n  private async *listPageRangeDiffItems(\n    offset: number,\n    count: number,\n    prevSnapshotOrUrl: string,\n    options?: PageBlobListPageRangesDiffSegmentOptions,\n  ): AsyncIterableIterator<PageRangeInfo> {\n    let marker: string | undefined;\n    for await (const getPageRangesSegment of this.listPageRangeDiffItemSegments(\n      offset,\n      count,\n      prevSnapshotOrUrl,\n      marker,\n      options,\n    )) {\n      yield* ExtractPageRangeInfoItems(getPageRangesSegment);\n    }\n  }\n\n  /**\n   * Returns an async iterable iterator to list of page ranges that differ between a specified snapshot and this page blob.\n   * @see https://docs.microsoft.com/rest/api/storageservices/get-page-ranges\n   *\n   *  .byPage() returns an async iterable iterator to list of page ranges that differ between a specified snapshot and this page blob.\n   *\n   * Example using `for await` syntax:\n   *\n   * ```js\n   * // Get the pageBlobClient before you run these snippets,\n   * // Can be obtained from `blobServiceClient.getContainerClient(\"<your-container-name>\").getPageBlobClient(\"<your-blob-name>\");`\n   * let i = 1;\n   * for await (const pageRange of pageBlobClient.listPageRangesDiff()) {\n   *   console.log(`Page range ${i++}: ${pageRange.start} - ${pageRange.end}`);\n   * }\n   * ```\n   *\n   * Example using `iter.next()`:\n   *\n   * ```js\n   * let i = 1;\n   * let iter = pageBlobClient.listPageRangesDiff();\n   * let pageRangeItem = await iter.next();\n   * while (!pageRangeItem.done) {\n   *   console.log(`Page range ${i++}: ${pageRangeItem.value.start} - ${pageRangeItem.value.end}, IsClear: ${pageRangeItem.value.isClear}`);\n   *   pageRangeItem = await iter.next();\n   * }\n   * ```\n   *\n   * Example using `byPage()`:\n   *\n   * ```js\n   * // passing optional maxPageSize in the page settings\n   * let i = 1;\n   * for await (const response of pageBlobClient.listPageRangesDiff().byPage({ maxPageSize: 20 })) {\n   *   for (const pageRange of response) {\n   *     console.log(`Page range ${i++}: ${pageRange.start} - ${pageRange.end}`);\n   *   }\n   * }\n   * ```\n   *\n   * Example using paging with a marker:\n   *\n   * ```js\n   * let i = 1;\n   * let iterator = pageBlobClient.listPageRangesDiff().byPage({ maxPageSize: 2 });\n   * let response = (await iterator.next()).value;\n   *\n   * // Prints 2 page ranges\n   * for (const pageRange of response) {\n   *   console.log(`Page range ${i++}: ${pageRange.start} - ${pageRange.end}`);\n   * }\n   *\n   * // Gets next marker\n   * let marker = response.continuationToken;\n   *\n   * // Passing next marker as continuationToken\n   *\n   * iterator = pageBlobClient.listPageRangesDiff().byPage({ continuationToken: marker, maxPageSize: 10 });\n   * response = (await iterator.next()).value;\n   *\n   * // Prints 10 page ranges\n   * for (const blob of response) {\n   *   console.log(`Page range ${i++}: ${pageRange.start} - ${pageRange.end}`);\n   * }\n   * ```\n   * @param offset - Starting byte position of the page ranges.\n   * @param count - Number of bytes to get.\n   * @param prevSnapshot - Timestamp of snapshot to retrieve the difference.\n   * @param options - Options to the Page Blob Get Ranges operation.\n   * @returns An asyncIterableIterator that supports paging.\n   */\n  public listPageRangesDiff(\n    offset: number,\n    count: number,\n    prevSnapshot: string,\n    options: PageBlobListPageRangesDiffOptions = {},\n  ): PagedAsyncIterableIterator<PageRangeInfo, PageBlobGetPageRangesDiffResponseModel> {\n    options.conditions = options.conditions || {};\n\n    // AsyncIterableIterator to iterate over blobs\n    const iter = this.listPageRangeDiffItems(offset, count, prevSnapshot, {\n      ...options,\n    });\n    return {\n      /**\n       * The next method, part of the iteration protocol\n       */\n      next() {\n        return iter.next();\n      },\n      /**\n       * The connection to the async iterator, part of the iteration protocol\n       */\n      [Symbol.asyncIterator]() {\n        return this;\n      },\n      /**\n       * Return an AsyncIterableIterator that works a page at a time\n       */\n      byPage: (settings: PageSettings = {}) => {\n        return this.listPageRangeDiffItemSegments(\n          offset,\n          count,\n          prevSnapshot,\n          settings.continuationToken,\n          {\n            maxPageSize: settings.maxPageSize,\n            ...options,\n          },\n        );\n      },\n    };\n  }\n\n  /**\n   * Gets the collection of page ranges that differ between a specified snapshot and this page blob for managed disks.\n   * @see https://docs.microsoft.com/rest/api/storageservices/get-page-ranges\n   *\n   * @param offset - Starting byte position of the page blob\n   * @param count - Number of bytes to get ranges diff.\n   * @param prevSnapshotUrl - URL of snapshot to retrieve the difference.\n   * @param options - Options to the Page Blob Get Page Ranges Diff operation.\n   * @returns Response data for the Page Blob Get Page Range Diff operation.\n   */\n  public async getPageRangesDiffForManagedDisks(\n    offset: number,\n    count: number,\n    prevSnapshotUrl: string,\n    options: PageBlobGetPageRangesDiffOptions = {},\n  ): Promise<PageBlobGetPageRangesDiffResponse> {\n    options.conditions = options.conditions || {};\n    return tracingClient.withSpan(\n      \"PageBlobClient-GetPageRangesDiffForManagedDisks\",\n      options,\n      async (updatedOptions) => {\n        const response = assertResponse<\n          PageBlobGetPageRangesDiffResponseInternal,\n          PageBlobGetPageRangesDiffHeaders,\n          PageListInternal\n        >(\n          await this.pageBlobContext.getPageRangesDiff({\n            abortSignal: options.abortSignal,\n            leaseAccessConditions: options.conditions,\n            modifiedAccessConditions: {\n              ...options.conditions,\n              ifTags: options.conditions?.tagConditions,\n            },\n            prevSnapshotUrl,\n            range: rangeToString({ offset, count }),\n            tracingOptions: updatedOptions.tracingOptions,\n          }),\n        );\n        return rangeResponseFromModel(response);\n      },\n    );\n  }\n\n  /**\n   * Resizes the page blob to the specified size (which must be a multiple of 512).\n   * @see https://docs.microsoft.com/rest/api/storageservices/set-blob-properties\n   *\n   * @param size - Target size\n   * @param options - Options to the Page Blob Resize operation.\n   * @returns Response data for the Page Blob Resize operation.\n   */\n  public async resize(\n    size: number,\n    options: PageBlobResizeOptions = {},\n  ): Promise<PageBlobResizeResponse> {\n    options.conditions = options.conditions || {};\n    return tracingClient.withSpan(\"PageBlobClient-resize\", options, async (updatedOptions) => {\n      return assertResponse<PageBlobResizeHeaders, PageBlobResizeHeaders>(\n        await this.pageBlobContext.resize(size, {\n          abortSignal: options.abortSignal,\n          leaseAccessConditions: options.conditions,\n          modifiedAccessConditions: {\n            ...options.conditions,\n            ifTags: options.conditions?.tagConditions,\n          },\n          encryptionScope: options.encryptionScope,\n          tracingOptions: updatedOptions.tracingOptions,\n        }),\n      );\n    });\n  }\n\n  /**\n   * Sets a page blob's sequence number.\n   * @see https://docs.microsoft.com/en-us/rest/api/storageservices/set-blob-properties\n   *\n   * @param sequenceNumberAction - Indicates how the service should modify the blob's sequence number.\n   * @param sequenceNumber - Required if sequenceNumberAction is max or update\n   * @param options - Options to the Page Blob Update Sequence Number operation.\n   * @returns Response data for the Page Blob Update Sequence Number operation.\n   */\n  public async updateSequenceNumber(\n    sequenceNumberAction: SequenceNumberActionType,\n    sequenceNumber?: number,\n    options: PageBlobUpdateSequenceNumberOptions = {},\n  ): Promise<PageBlobUpdateSequenceNumberResponse> {\n    options.conditions = options.conditions || {};\n    return tracingClient.withSpan(\n      \"PageBlobClient-updateSequenceNumber\",\n      options,\n      async (updatedOptions) => {\n        return assertResponse<\n          PageBlobUpdateSequenceNumberHeaders,\n          PageBlobUpdateSequenceNumberHeaders\n        >(\n          await this.pageBlobContext.updateSequenceNumber(sequenceNumberAction, {\n            abortSignal: options.abortSignal,\n            blobSequenceNumber: sequenceNumber,\n            leaseAccessConditions: options.conditions,\n            modifiedAccessConditions: {\n              ...options.conditions,\n              ifTags: options.conditions?.tagConditions,\n            },\n            tracingOptions: updatedOptions.tracingOptions,\n          }),\n        );\n      },\n    );\n  }\n\n  /**\n   * Begins an operation to start an incremental copy from one page blob's snapshot to this page blob.\n   * The snapshot is copied such that only the differential changes between the previously\n   * copied snapshot are transferred to the destination.\n   * The copied snapshots are complete copies of the original snapshot and can be read or copied from as usual.\n   * @see https://docs.microsoft.com/rest/api/storageservices/incremental-copy-blob\n   * @see https://docs.microsoft.com/en-us/azure/virtual-machines/windows/incremental-snapshots\n   *\n   * @param copySource - Specifies the name of the source page blob snapshot. For example,\n   *                            https://myaccount.blob.core.windows.net/mycontainer/myblob?snapshot=<DateTime>\n   * @param options - Options to the Page Blob Copy Incremental operation.\n   * @returns Response data for the Page Blob Copy Incremental operation.\n   */\n  public async startCopyIncremental(\n    copySource: string,\n    options: PageBlobStartCopyIncrementalOptions = {},\n  ): Promise<PageBlobCopyIncrementalResponse> {\n    return tracingClient.withSpan(\n      \"PageBlobClient-startCopyIncremental\",\n      options,\n      async (updatedOptions) => {\n        return assertResponse<PageBlobCopyIncrementalHeaders, PageBlobCopyIncrementalHeaders>(\n          await this.pageBlobContext.copyIncremental(copySource, {\n            abortSignal: options.abortSignal,\n            modifiedAccessConditions: {\n              ...options.conditions,\n              ifTags: options.conditions?.tagConditions,\n            },\n            tracingOptions: updatedOptions.tracingOptions,\n          }),\n        );\n      },\n    );\n  }\n}\n"]}
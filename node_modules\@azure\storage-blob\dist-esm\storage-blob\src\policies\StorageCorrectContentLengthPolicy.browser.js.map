{"version": 3, "file": "StorageCorrectContentLengthPolicy.browser.js", "sourceRoot": "", "sources": ["../../../../src/policies/StorageCorrectContentLengthPolicy.browser.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AASlC;;GAEG;AACH,MAAM,CAAC,MAAM,qCAAqC,GAAG,mCAAmC,CAAC;AAEzF;;GAEG;AACH,MAAM,UAAU,iCAAiC;IAC/C,OAAO;QACL,IAAI,EAAE,qCAAqC;QAC3C,KAAK,CAAC,WAAW,CAAC,OAAwB,EAAE,IAAiB;YAC3D,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;QACvB,CAAC;KACF,CAAC;AACJ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type {\n  PipelineRequest,\n  PipelineResponse,\n  SendRequest,\n  PipelinePolicy,\n} from \"@azure/core-rest-pipeline\";\n\n/**\n * The programmatic identifier of the storageCorrectContentLengthPolicy.\n */\nexport const storageCorrectContentLengthPolicyName = \"StorageCorrectContentLengthPolicy\";\n\n/**\n * storageCorrectContentLengthPolicy to correctly set Content-Length header with request body length.\n */\nexport function storageCorrectContentLengthPolicy(): PipelinePolicy {\n  return {\n    name: storageCorrectContentLengthPolicyName,\n    async sendRequest(request: PipelineRequest, next: SendRequest): Promise<PipelineResponse> {\n      return next(request);\n    },\n  };\n}\n"]}
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
/**
 * Credential is an abstract class for Azure Storage HTTP requests signing. This
 * class will host an credentialPolicyCreator factory which generates CredentialPolicy.
 */
export class Credential {
    /**
     * Creates a RequestPolicy object.
     *
     * @param _nextPolicy -
     * @param _options -
     */
    create(_nextPolicy, _options) {
        throw new Error("Method should be implemented in children classes.");
    }
}
//# sourceMappingURL=Credential.js.map
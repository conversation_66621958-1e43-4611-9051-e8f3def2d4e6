{"version": 3, "file": "pageBlob.js", "sourceRoot": "", "sources": ["../../../../../../src/generated/src/operationsInterfaces/pageBlob.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG", "sourcesContent": ["/*\n * Copyright (c) Microsoft Corporation.\n * Licensed under the MIT License.\n *\n * Code generated by Microsoft (R) AutoRest Code Generator.\n * Changes may cause incorrect behavior and will be lost if the code is regenerated.\n */\n\nimport * as coreRestPipeline from \"@azure/core-rest-pipeline\";\nimport {\n  PageBlobCreateOptionalParams,\n  PageBlobCreateResponse,\n  PageBlobUploadPagesOptionalParams,\n  PageBlobUploadPagesResponse,\n  PageBlobClearPagesOptionalParams,\n  PageBlobClearPagesResponse,\n  PageBlobUploadPagesFromURLOptionalParams,\n  PageBlobUploadPagesFromURLResponse,\n  PageBlobGetPageRangesOptionalParams,\n  PageBlobGetPageRangesResponse,\n  PageBlobGetPageRangesDiffOptionalParams,\n  PageBlobGetPageRangesDiffResponse,\n  PageBlobResizeOptionalParams,\n  PageBlobResizeResponse,\n  SequenceNumberActionType,\n  PageBlobUpdateSequenceNumberOptionalParams,\n  PageBlobUpdateSequenceNumberResponse,\n  PageBlobCopyIncrementalOptionalParams,\n  PageBlobCopyIncrementalResponse,\n} from \"../models\";\n\n/** Interface representing a PageBlob. */\nexport interface PageBlob {\n  /**\n   * The Create operation creates a new page blob.\n   * @param contentLength The length of the request.\n   * @param blobContentLength This header specifies the maximum size for the page blob, up to 1 TB. The\n   *                          page blob size must be aligned to a 512-byte boundary.\n   * @param options The options parameters.\n   */\n  create(\n    contentLength: number,\n    blobContentLength: number,\n    options?: PageBlobCreateOptionalParams,\n  ): Promise<PageBlobCreateResponse>;\n  /**\n   * The Upload Pages operation writes a range of pages to a page blob\n   * @param contentLength The length of the request.\n   * @param body Initial data\n   * @param options The options parameters.\n   */\n  uploadPages(\n    contentLength: number,\n    body: coreRestPipeline.RequestBodyType,\n    options?: PageBlobUploadPagesOptionalParams,\n  ): Promise<PageBlobUploadPagesResponse>;\n  /**\n   * The Clear Pages operation clears a set of pages from a page blob\n   * @param contentLength The length of the request.\n   * @param options The options parameters.\n   */\n  clearPages(\n    contentLength: number,\n    options?: PageBlobClearPagesOptionalParams,\n  ): Promise<PageBlobClearPagesResponse>;\n  /**\n   * The Upload Pages operation writes a range of pages to a page blob where the contents are read from a\n   * URL\n   * @param sourceUrl Specify a URL to the copy source.\n   * @param sourceRange Bytes of source data in the specified range. The length of this range should\n   *                    match the ContentLength header and x-ms-range/Range destination range header.\n   * @param contentLength The length of the request.\n   * @param range The range of bytes to which the source range would be written. The range should be 512\n   *              aligned and range-end is required.\n   * @param options The options parameters.\n   */\n  uploadPagesFromURL(\n    sourceUrl: string,\n    sourceRange: string,\n    contentLength: number,\n    range: string,\n    options?: PageBlobUploadPagesFromURLOptionalParams,\n  ): Promise<PageBlobUploadPagesFromURLResponse>;\n  /**\n   * The Get Page Ranges operation returns the list of valid page ranges for a page blob or snapshot of a\n   * page blob\n   * @param options The options parameters.\n   */\n  getPageRanges(\n    options?: PageBlobGetPageRangesOptionalParams,\n  ): Promise<PageBlobGetPageRangesResponse>;\n  /**\n   * The Get Page Ranges Diff operation returns the list of valid page ranges for a page blob that were\n   * changed between target blob and previous snapshot.\n   * @param options The options parameters.\n   */\n  getPageRangesDiff(\n    options?: PageBlobGetPageRangesDiffOptionalParams,\n  ): Promise<PageBlobGetPageRangesDiffResponse>;\n  /**\n   * Resize the Blob\n   * @param blobContentLength This header specifies the maximum size for the page blob, up to 1 TB. The\n   *                          page blob size must be aligned to a 512-byte boundary.\n   * @param options The options parameters.\n   */\n  resize(\n    blobContentLength: number,\n    options?: PageBlobResizeOptionalParams,\n  ): Promise<PageBlobResizeResponse>;\n  /**\n   * Update the sequence number of the blob\n   * @param sequenceNumberAction Required if the x-ms-blob-sequence-number header is set for the request.\n   *                             This property applies to page blobs only. This property indicates how the service should modify the\n   *                             blob's sequence number\n   * @param options The options parameters.\n   */\n  updateSequenceNumber(\n    sequenceNumberAction: SequenceNumberActionType,\n    options?: PageBlobUpdateSequenceNumberOptionalParams,\n  ): Promise<PageBlobUpdateSequenceNumberResponse>;\n  /**\n   * The Copy Incremental operation copies a snapshot of the source page blob to a destination page blob.\n   * The snapshot is copied such that only the differential changes between the previously copied\n   * snapshot are transferred to the destination. The copied snapshots are complete copies of the\n   * original snapshot and can be read or copied from as usual. This API is supported since REST version\n   * 2016-05-31.\n   * @param copySource Specifies the name of the source page blob snapshot. This value is a URL of up to\n   *                   2 KB in length that specifies a page blob snapshot. The value should be URL-encoded as it would\n   *                   appear in a request URI. The source blob must either be public or must be authenticated via a shared\n   *                   access signature.\n   * @param options The options parameters.\n   */\n  copyIncremental(\n    copySource: string,\n    options?: PageBlobCopyIncrementalOptionalParams,\n  ): Promise<PageBlobCopyIncrementalResponse>;\n}\n"]}
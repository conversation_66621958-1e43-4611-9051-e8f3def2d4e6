{"version": 3, "file": "StorageClient.js", "sourceRoot": "", "sources": ["../../../src/StorageClient.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EAAE,oBAAoB,EAAE,MAAM,wBAAwB,CAAC;AAE9D,OAAO,EAAE,oBAAoB,EAAE,yBAAyB,EAAE,MAAM,YAAY,CAAC;AAC7E,OAAO,EAAE,aAAa,EAAE,YAAY,EAAE,MAAM,EAAE,qBAAqB,EAAE,MAAM,sBAAsB,CAAC;AAgBlG;;;GAGG;AACH,MAAM,OAAgB,aAAa;IAyBjC;;;;OAIG;IACH,YAAsB,GAAW,EAAE,QAAsB;QACvD,iFAAiF;QACjF,IAAI,CAAC,GAAG,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC;QAC9B,IAAI,CAAC,WAAW,GAAG,qBAAqB,CAAC,GAAG,CAAC,CAAC;QAC9C,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,oBAAoB,GAAG,IAAI,oBAAoB,CAAC,IAAI,CAAC,GAAG,EAAE,oBAAoB,CAAC,QAAQ,CAAC,CAAC,CAAC;QAE/F,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,OAAO,CAAC,CAAC;QAE7D,IAAI,CAAC,UAAU,GAAG,yBAAyB,CAAC,QAAQ,CAAC,CAAC;QAEtD,iDAAiD;QACjD,MAAM,oBAAoB,GAAG,IAAI,CAAC,oBAA2B,CAAC;QAC9D,oBAAoB,CAAC,kBAAkB,GAAG,SAAS,CAAC;IACtD,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { StorageClient as StorageClientContext } from \"./generated/src/\";\nimport { StorageContextClient } from \"./StorageContextClient\";\nimport type { PipelineLike } from \"./Pipeline\";\nimport { getCoreClientOptions, getCredentialFromPipeline } from \"./Pipeline\";\nimport { escapeURLPath, getURLScheme, iEqual, getAccountNameFromUrl } from \"./utils/utils.common\";\nimport type { AnonymousCredential } from \"./credentials/AnonymousCredential\";\nimport type { StorageSharedKeyCredential } from \"./credentials/StorageSharedKeyCredential\";\nimport type { TokenCredential } from \"@azure/core-auth\";\nimport type { OperationTracingOptions } from \"@azure/core-tracing\";\n\n/**\n * An interface for options common to every remote operation.\n */\nexport interface CommonOptions {\n  /**\n   * Options to configure spans created when tracing is enabled.\n   */\n  tracingOptions?: OperationTracingOptions;\n}\n\n/**\n * A StorageClient represents a based URL class for {@link BlobServiceClient}, {@link ContainerClient}\n * and etc.\n */\nexport abstract class StorageClient {\n  /**\n   * Encoded URL string value.\n   */\n  public readonly url: string;\n  public readonly accountName: string;\n  /**\n   * Request policy pipeline.\n   *\n   * @internal\n   */\n  protected readonly pipeline: PipelineLike;\n  /**\n   * Such as AnonymousCredential, StorageSharedKeyCredential or any credential from the `@azure/identity` package to authenticate requests to the service. You can also provide an object that implements the TokenCredential interface. If not specified, AnonymousCredential is used.\n   */\n  public readonly credential: StorageSharedKeyCredential | AnonymousCredential | TokenCredential;\n  /**\n   * StorageClient is a reference to protocol layer operations entry, which is\n   * generated by AutoRest generator.\n   */\n  protected readonly storageClientContext: StorageClientContext;\n  /**\n   */\n  protected readonly isHttps: boolean;\n\n  /**\n   * Creates an instance of StorageClient.\n   * @param url - url to resource\n   * @param pipeline - request policy pipeline.\n   */\n  protected constructor(url: string, pipeline: PipelineLike) {\n    // URL should be encoded and only once, protocol layer shouldn't encode URL again\n    this.url = escapeURLPath(url);\n    this.accountName = getAccountNameFromUrl(url);\n    this.pipeline = pipeline;\n    this.storageClientContext = new StorageContextClient(this.url, getCoreClientOptions(pipeline));\n\n    this.isHttps = iEqual(getURLScheme(this.url) || \"\", \"https\");\n\n    this.credential = getCredentialFromPipeline(pipeline);\n\n    // Override protocol layer's default content-type\n    const storageClientContext = this.storageClientContext as any;\n    storageClientContext.requestContentType = undefined;\n  }\n}\n"]}
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
import { StorageBrowserPolicy } from "./policies/StorageBrowserPolicy";
export { StorageBrowserPolicy };
/**
 * StorageBrowserPolicyFactory is a factory class helping generating StorageBrowserPolicy objects.
 */
export class StorageBrowserPolicyFactory {
    /**
     * Creates a StorageBrowserPolicyFactory object.
     *
     * @param nextPolicy -
     * @param options -
     */
    create(nextPolicy, options) {
        return new StorageBrowserPolicy(nextPolicy, options);
    }
}
//# sourceMappingURL=StorageBrowserPolicyFactory.js.map
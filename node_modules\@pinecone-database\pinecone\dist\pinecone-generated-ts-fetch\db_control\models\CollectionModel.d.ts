/**
 * Pinecone Control Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
/**
 * The CollectionModel describes the configuration and status of a Pinecone collection.
 * @export
 * @interface CollectionModel
 */
export interface CollectionModel {
    /**
     * The name of the collection.
     * @type {string}
     * @memberof CollectionModel
     */
    name: string;
    /**
     * The size of the collection in bytes.
     * @type {number}
     * @memberof CollectionModel
     */
    size?: number;
    /**
     * The status of the collection.
     * @type {string}
     * @memberof CollectionModel
     */
    status: CollectionModelStatusEnum;
    /**
     * The dimension of the vectors stored in each record held in the collection.
     * @type {number}
     * @memberof CollectionModel
     */
    dimension?: number;
    /**
     * The number of records stored in the collection.
     * @type {number}
     * @memberof CollectionModel
     */
    vectorCount?: number;
    /**
     * The environment where the collection is hosted.
     * @type {string}
     * @memberof CollectionModel
     */
    environment: string;
}
/**
 * @export
 */
export declare const CollectionModelStatusEnum: {
    readonly Initializing: "Initializing";
    readonly Ready: "Ready";
    readonly Terminating: "Terminating";
};
export type CollectionModelStatusEnum = typeof CollectionModelStatusEnum[keyof typeof CollectionModelStatusEnum];
/**
 * Check if a given object implements the CollectionModel interface.
 */
export declare function instanceOfCollectionModel(value: object): boolean;
export declare function CollectionModelFromJSON(json: any): CollectionModel;
export declare function CollectionModelFromJSONTyped(json: any, ignoreDiscriminator: boolean): CollectionModel;
export declare function CollectionModelToJSON(value?: CollectionModel | null): any;

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🤖 AI Agent Interface</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
            border: 1px solid rgba(255, 255, 255, 0.18);
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .status {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid #4CAF50;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            text-align: center;
        }
        .method {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .method h3 {
            margin-top: 0;
            color: #FFD700;
        }
        .code-block {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 10px 0;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .copy-btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
            margin-left: 10px;
        }
        .copy-btn:hover {
            background: #45a049;
        }
        .examples {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
        }
        .example {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 10px;
            margin: 10px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .example:hover {
            background: rgba(255, 255, 255, 0.2);
        }
        .warning {
            background: rgba(255, 193, 7, 0.2);
            border: 1px solid #FFC107;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 Your AI Agent Interface</h1>
        
        <div class="status">
            ✅ <strong>n8n Status:</strong> Running on localhost:2410<br>
            🔗 <strong>Interface:</strong> <a href="http://localhost:2410" target="_blank" style="color: #FFD700;">http://localhost:2410</a>
        </div>

        <div class="warning">
            ⚠️ <strong>CORS Notice:</strong> Due to browser security, direct API calls from this HTML file may be blocked. Use the PowerShell methods below instead.
        </div>

        <div class="method">
            <h3>🚀 Method 1: PowerShell Command</h3>
            <p>Copy and paste this command into PowerShell to test your AI agent:</p>
            <div class="code-block">
$headers = @{"Content-Type" = "application/json"}
$body = @{
    instruction = "Create a chatbot for customer support"
    chatInput = "Hello AI Agent"
} | ConvertTo-Json

Invoke-RestMethod -Uri "http://localhost:2410/webhook/simple-ai" -Method POST -Headers $headers -Body $body
            </div>
            <button class="copy-btn" onclick="copyToClipboard('powershell1')">Copy</button>
        </div>

        <div class="method">
            <h3>🔧 Method 2: Create New AI Agent</h3>
            <p>Use this PowerShell command to create a new AI agent workflow:</p>
            <div class="code-block" id="powershell2">
$apiKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI0NDgyZmY2Yi0xNzcwLTRlZGQtOTY2Yy1hNTVjZTNkZDZmZGQiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzQ4NTAyNTMyfQ.Si6LRA-uLi8HIdWZIX6tKNXzrSJDd6gBqiRHwtf8K8M"
$headers = @{"Content-Type" = "application/json"; "X-N8N-API-KEY" = $apiKey}

# Create a simple working AI agent
$workflow = @{
    name = "My AI Agent"
    nodes = @(
        @{
            parameters = @{httpMethod = "POST"; path = "my-ai-agent"}
            id = "webhook"
            name = "Webhook"
            type = "n8n-nodes-base.webhook"
            typeVersion = 2
            position = @(300, 300)
        },
        @{
            parameters = @{
                respondWith = "json"
                responseBody = '{"success": true, "message": "AI Agent working!", "instruction": "{{ $json.body.instruction || $json.body.chatInput }}"}'
            }
            id = "response"
            name = "Response"
            type = "n8n-nodes-base.respondToWebhook"
            typeVersion = 1
            position = @(500, 300)
        }
    )
    connections = @{
        Webhook = @{main = @(@(@{node = "Response"; type = "main"; index = 0}))}
    }
    settings = @{timezone = "UTC"}
} | ConvertTo-Json -Depth 10

$result = Invoke-RestMethod -Uri "http://localhost:2410/api/v1/workflows" -Method POST -Headers $headers -Body $workflow
Write-Host "✅ AI Agent created! ID: $($result.id)"

# Activate it
Invoke-RestMethod -Uri "http://localhost:2410/api/v1/workflows/$($result.id)/activate" -Method POST -Headers $headers
Write-Host "✅ AI Agent activated! Webhook: http://localhost:2410/webhook/my-ai-agent"
            </div>
            <button class="copy-btn" onclick="copyToClipboard('powershell2')">Copy</button>
        </div>

        <div class="method">
            <h3>🧪 Method 3: Test Any Webhook</h3>
            <p>Test any webhook with this template:</p>
            <div class="code-block" id="powershell3">
# Replace WEBHOOK_PATH with your actual webhook path
$webhookUrl = "http://localhost:2410/webhook/WEBHOOK_PATH"
$headers = @{"Content-Type" = "application/json"}
$body = @{
    instruction = "Your instruction here"
    chatInput = "Your message here"
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri $webhookUrl -Method POST -Headers $headers -Body $body
    Write-Host "✅ Success!" -ForegroundColor Green
    $response | ConvertTo-Json -Depth 3
} catch {
    Write-Host "❌ Error: $($_.Exception.Message)" -ForegroundColor Red
}
            </div>
            <button class="copy-btn" onclick="copyToClipboard('powershell3')">Copy</button>
        </div>

        <div class="examples">
            <h3>💡 Example Instructions to Try:</h3>
            <div class="example" onclick="copyInstruction('Create a chatbot that answers customer questions about our products')">
                🤖 "Create a chatbot that answers customer questions about our products"
            </div>
            <div class="example" onclick="copyInstruction('Build an email automation that sends welcome emails to new subscribers')">
                📧 "Build an email automation that sends welcome emails to new subscribers"
            </div>
            <div class="example" onclick="copyInstruction('Make a social media content generator for LinkedIn posts')">
                📱 "Make a social media content generator for LinkedIn posts"
            </div>
            <div class="example" onclick="copyInstruction('Create a data analyzer that processes customer feedback')">
                📊 "Create a data analyzer that processes customer feedback"
            </div>
            <div class="example" onclick="copyInstruction('Build a workflow that monitors website uptime and sends alerts')">
                🔍 "Build a workflow that monitors website uptime and sends alerts"
            </div>
        </div>

        <div class="method">
            <h3>🌐 Direct n8n Access</h3>
            <p>You can also work directly in the n8n interface:</p>
            <ul>
                <li><strong>Open n8n:</strong> <a href="http://localhost:2410" target="_blank" style="color: #FFD700;">http://localhost:2410</a></li>
                <li><strong>Create workflows</strong> using the visual editor</li>
                <li><strong>Import/Export</strong> workflow JSON files</li>
                <li><strong>Monitor executions</strong> and debug issues</li>
            </ul>
        </div>
    </div>

    <script>
        function copyToClipboard(elementId) {
            const element = document.getElementById(elementId);
            const text = element ? element.textContent : document.querySelector('.code-block').textContent;
            navigator.clipboard.writeText(text).then(() => {
                alert('Copied to clipboard!');
            });
        }

        function copyInstruction(instruction) {
            navigator.clipboard.writeText(instruction).then(() => {
                alert(`Copied: "${instruction}"`);
            });
        }
    </script>
</body>
</html>

/**
 * Pinecone Inference API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
/**
 * Embedding of a single input
 * @export
 * @interface Embedding
 */
export interface Embedding {
    /**
     * The embedding values.
     * @type {Array<number>}
     * @memberof Embedding
     */
    values?: Array<number>;
}
/**
 * Check if a given object implements the Embedding interface.
 */
export declare function instanceOfEmbedding(value: object): boolean;
export declare function EmbeddingFromJSON(json: any): Embedding;
export declare function EmbeddingFromJSONTyped(json: any, ignoreDiscriminator: boolean): Embedding;
export declare function EmbeddingToJSON(value?: Embedding | null): any;

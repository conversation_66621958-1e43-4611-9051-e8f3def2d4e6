/**
 * Pinecone Inference API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
/**
 * Usage statistics for the model inference.
 * @export
 * @interface EmbeddingsListUsage
 */
export interface EmbeddingsListUsage {
    /**
     * Total number of tokens consumed across all inputs.
     * @type {number}
     * @memberof EmbeddingsListUsage
     */
    totalTokens?: number;
}
/**
 * Check if a given object implements the EmbeddingsListUsage interface.
 */
export declare function instanceOfEmbeddingsListUsage(value: object): boolean;
export declare function EmbeddingsListUsageFromJSON(json: any): EmbeddingsListUsage;
export declare function EmbeddingsListUsageFromJSONTyped(json: any, ignoreDiscriminator: boolean): EmbeddingsListUsage;
export declare function EmbeddingsListUsageToJSON(value?: EmbeddingsListUsage | null): any;

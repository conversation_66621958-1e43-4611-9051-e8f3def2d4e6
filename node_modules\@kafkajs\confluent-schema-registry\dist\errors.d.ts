declare class ConfluentSchemaRegistryError extends Error {
    constructor(error: any);
}
declare class ConfluentSchemaRegistryArgumentError extends ConfluentSchemaRegistryError {
}
declare class ConfluentSchemaRegistryCompatibilityError extends ConfluentSchemaRegistryError {
}
declare class ConfluentSchemaRegistryInvalidSchemaError extends ConfluentSchemaRegistryError {
}
declare class ConfluentSchemaRegistryValidationError extends ConfluentSchemaRegistryError {
    paths: string[][];
    constructor(error: any, paths: string[][]);
}
export { ConfluentSchemaRegistryError, ConfluentSchemaRegistryArgumentError, ConfluentSchemaRegistryCompatibilityError, ConfluentSchemaRegistryInvalidSchemaError, ConfluentSchemaRegistryValidationError, };

"use strict";
/* tslint:disable */
/* eslint-disable */
/**
 * Pinecone Inference API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmbeddingsListUsageToJSON = exports.EmbeddingsListUsageFromJSONTyped = exports.EmbeddingsListUsageFromJSON = exports.instanceOfEmbeddingsListUsage = void 0;
var runtime_1 = require("../runtime");
/**
 * Check if a given object implements the EmbeddingsListUsage interface.
 */
function instanceOfEmbeddingsListUsage(value) {
    var isInstance = true;
    return isInstance;
}
exports.instanceOfEmbeddingsListUsage = instanceOfEmbeddingsListUsage;
function EmbeddingsListUsageFromJSON(json) {
    return EmbeddingsListUsageFromJSONTyped(json, false);
}
exports.EmbeddingsListUsageFromJSON = EmbeddingsListUsageFromJSON;
function EmbeddingsListUsageFromJSONTyped(json, ignoreDiscriminator) {
    if ((json === undefined) || (json === null)) {
        return json;
    }
    return {
        'totalTokens': !(0, runtime_1.exists)(json, 'total_tokens') ? undefined : json['total_tokens'],
    };
}
exports.EmbeddingsListUsageFromJSONTyped = EmbeddingsListUsageFromJSONTyped;
function EmbeddingsListUsageToJSON(value) {
    if (value === undefined) {
        return undefined;
    }
    if (value === null) {
        return null;
    }
    return {
        'total_tokens': value.totalTokens,
    };
}
exports.EmbeddingsListUsageToJSON = EmbeddingsListUsageToJSON;
//# sourceMappingURL=EmbeddingsListUsage.js.map
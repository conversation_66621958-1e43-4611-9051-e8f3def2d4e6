{"version": 3, "file": "BufferScheduler.browser.js", "sourceRoot": "", "sources": ["../../../../storage-common/src/BufferScheduler.browser.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,6EAA6E;AAC7E,0CAA0C;AAC1C,+DAA+D;AAC/D,MAAM,OAAO,eAAe;CAAG", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\n// This file is used as a shim of \"BufferScheduler\" for some browser bundlers\n// when trying to bundle \"BufferScheduler\"\n// \"BufferScheduler\" class is only available in Node.js runtime\nexport class BufferScheduler {}\n"]}
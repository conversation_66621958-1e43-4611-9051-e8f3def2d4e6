{"name": "@petamoriken/float16", "version": "3.9.2", "description": "IEEE 754 half-precision floating-point for JavaScript", "keywords": ["float16", "binary16", "FP16", "half-precision", "ieee754", "Float16Array", "TypedArray", "DataView", "getFloat16", "setFloat16", "f16round", "ponyfill", "shim"], "homepage": "https://github.com/petamoriken/float16", "bugs": {"url": "https://github.com/petamoriken/float16/issues"}, "repository": {"type": "git", "url": "https://github.com/petamoriken/float16.git"}, "license": "MIT", "author": "<PERSON><PERSON> <<EMAIL>> (https://moriken.dev)", "sideEffects": false, "exports": {".": {"require": "./lib/index.cjs", "import": "./src/index.mjs", "types": "./index.d.ts"}, "./inspect": {"require": "./inspect/node.cjs", "import": "./inspect/node.mjs"}}, "main": "./lib/index.cjs", "browser": "./browser/float16.js", "module": "./src/index.mjs", "types": "./index.d.ts", "typesVersions": {">=5.7": {"index.d.ts": ["index.v5.7.d.ts"]}, ">=5.6": {"index.d.ts": ["index.v5.6.d.ts"]}}, "files": ["src", "lib", "browser", "inspect/node.mjs", "inspect/node.cjs", "index.d.ts", "index.v5.6.d.ts", "index.v5.7.d.ts"], "scripts": {"build": "concurrently \"yarn:build:*\"", "build:browser": "rollup -c", "build:lib": "babel src -d lib --out-file-extension .cjs", "clean": "rm -rf lib browser docs coverage .nyc_output", "coverage": "nyc --reporter=lcov mocha test/*.js", "docs": "mkdir -p docs && cp README.md docs/index.md && yarn run docs:test", "docs:test": "mkdir -p docs/test && concurrently \"yarn:docs:test:*\"", "docs:test:build": "cp test/*.js docs/test && tools/power", "docs:test:copy": "cp browser/float16.js docs/test/float16.js && cp test/browser/*.html docs/test", "docs:test:dependencies": "cp node_modules/mocha/mocha.js node_modules/mocha/mocha.css node_modules/power-assert/build/power-assert.js docs/test", "lint": "concurrently \"yarn:lint:*\"", "lint:eslint": "eslint src/**/*.mjs", "lint:unused": "find-unused-exports --module-glob 'src/**/*.mjs'", "prepublishOnly": "yarn run lint && yarn test", "refresh": "yarn run clean && yarn run build && yarn run docs", "setup-test-browser": "http-server docs/test -p 8000 > /dev/null 2>&1 &", "test": "mocha test/*.js", "test-browser": "nightwatch -e chrome,chrome_old,firefox,firefox_old,firefox_esr,safari,safari_old"}, "devDependencies": {"@babel/cli": "^7.26.4", "@babel/core": "^7.26.9", "@babel/plugin-transform-modules-commonjs": "^7.26.3", "@types/nightwatch": "^3.0.1", "@types/node": "^22.13.9", "babel-plugin-replace-import-extension": "^1.1.5", "browserslist": "^4.24.4", "concurrently": "^9.1.2", "eslint": "^8.57.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsdoc": "^50.6.3", "espower-cli": "^1.1.0", "espower-loader": "^1.2.2", "exorcist": "^2.0.0", "find-unused-exports": "^7.1.1", "http-server": "^14.1.1", "mocha": "^11.1.0", "nightwatch": "^3.12.0", "nightwatch-saucelabs-endsauce": "^2.4.0", "nyc": "^17.1.0", "power-assert": "^1.4.2", "rollup": "^4.34.9", "rollup-plugin-cleanup": "^3.2.1", "source-map-support": "^0.5.21"}, "packageManager": "yarn@1.22.22"}
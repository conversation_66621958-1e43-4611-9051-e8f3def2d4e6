{"version": 3, "file": "middleware.js", "sourceRoot": "", "sources": ["../../src/utils/middleware.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,wEAGmD;AACnD,oCAA2C;AAE3C,IAAM,eAAe,GAAiB,EAAE,CAAC;AAEzC,IAAM,KAAK,GAAG,UAAC,GAAG,EAAE,KAAK;IACvB,IAAM,MAAM,GAAG;QACb,IAAI,EAAE,UAAU;QAChB,GAAG,EAAE,UAAU;QACf,KAAK,EAAE,UAAU;QACjB,MAAM,EAAE,UAAU;KACnB,CAAC;IAEF,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,UAAU,CAAC;AAC1C,CAAC,CAAC;AAEF;;;;;GAKG;AACH,IACE,OAAO,OAAO,KAAK,WAAW;IAC9B,OAAO;IACP,OAAO,CAAC,GAAG;IACX,OAAO,CAAC,GAAG,CAAC,cAAc,EAC1B;IACA,IAAM,kBAAkB,GAAG;QACzB,GAAG,EAAE,UAAO,OAAO;;;gBACjB,OAAO,CAAC,KAAK,CACX,KAAK,CAAC,uBAAgB,OAAO,CAAC,IAAI,CAAC,MAAM,cAAI,OAAO,CAAC,GAAG,CAAE,EAAE,MAAM,CAAC,CACpE,CAAC;gBAEI,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;gBACjE,OAAO,CAAC,SAAS,CAAC,GAAG,gBAAgB,CAAC;gBACtC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,uBAAgB,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAE,EAAE,MAAM,CAAC,CAAC,CAAC;gBAExE,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE;oBACrB,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,oBAAa,OAAO,CAAC,IAAI,CAAC,IAAI,CAAE,EAAE,MAAM,CAAC,CAAC,CAAC;iBAChE;gBACD,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;;;aACnB;QAED,IAAI,EAAE,UAAO,OAAO;;;;;wBAClB,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,sBAAe,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAE,EAAE,OAAO,CAAC,CAAC,CAAC;wBACxE,KAAA,CAAA,KAAA,OAAO,CAAA,CAAC,KAAK,CAAA;wBACX,KAAA,KAAK,CAAA;;wBAAc,qBAAM,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE,EAAA;;wBADlD,cACE,kBAAM,wBAAa,SAA6B,EAAE,EAAE,OAAO,EAAC,EAC7D,CAAC;wBACF,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;;;;aACnB;KACF,CAAC;IAEF,eAAe,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;CAC1C;AAED;;;;GAIG;AACH,IACE,OAAO,OAAO,KAAK,WAAW;IAC9B,OAAO;IACP,OAAO,CAAC,GAAG;IACX,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAC/B;IACA,IAAM,mBAAmB,GAAG;QAC1B,IAAI,EAAE,UAAO,OAAO;;;gBACd,OAAO,GAAG,wBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,IAAI,EAAE,CAAC,CAAC,SAAS,CAAC,OAAG,CAAC;gBACzE,IAAI,OAAO,CAAC,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,EAAE;oBAChE,OAAO,IAAI,8BAAsB,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,OAAG,CAAC;iBAC1E;gBACK,GAAG,GAAG,kBAAW,OAAO,CAAC,IAAI,CAAC,MAAM,cAAI,OAAO,CAAC,GAAG,cAAI,OAAO,cAClE,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,cAAO,OAAO,CAAC,IAAI,CAAC,IAAI,MAAG,CAAC,CAAC,CAAC,EAAE,CACpD,CAAC;gBACH,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC;gBACjC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;;;aACnB;KACF,CAAC;IACF,eAAe,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;CAC3C;AAEY,QAAA,UAAU,mCAClB,eAAe;IAClB;QACE,OAAO,EAAE,UAAO,OAAO;;;;4BACT,qBAAM,IAAA,uBAAc,EAAC,OAAO,CAAC,KAAK,EAAE,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,EAAA;;wBAAjE,GAAG,GAAG,SAA2D;wBACvE,MAAM,GAAG,CAAC;;;aACX;QAED,IAAI,EAAE,UAAO,OAAO;;;;;wBACV,QAAQ,GAAK,OAAO,SAAZ,CAAa;6BAEzB,CAAA,QAAQ,CAAC,MAAM,IAAI,GAAG,IAAI,QAAQ,CAAC,MAAM,GAAG,GAAG,CAAA,EAA/C,wBAA+C;wBACjD,sBAAO,QAAQ,EAAC;4BAEJ,qBAAM,IAAA,uBAAc,EAC9B,IAAI,0BAAa,CAAC,QAAQ,EAAE,4BAA4B,CAAC,EACzD,SAAS,EACT,OAAO,CAAC,GAAG,CACZ,EAAA;;wBAJK,GAAG,GAAG,SAIX;wBACD,MAAM,GAAG,CAAC;;;aAEb;KACF;UACD"}
{"version": 3, "file": "user-agent.js", "sourceRoot": "", "sources": ["../../src/utils/user-agent.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,6CAAuC;AAEvC,2DAA+C;AAExC,IAAM,cAAc,GAAG,UAAC,MAA6B;IAC1D,yDAAyD;IACzD,yDAAyD;IACzD,+CAA+C;IAC/C,IAAM,cAAc,GAAG;QACrB,UAAG,WAAW,CAAC,IAAI,eAAK,WAAW,CAAC,OAAO,CAAE;QAC7C,iBAAiB;KAClB,CAAC;IAEF,IAAI,IAAA,oBAAM,GAAE,EAAE;QACZ,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;KACrC;IAED,8DAA8D;IAC9D,IAAI,OAAO,OAAO,KAAK,WAAW,IAAI,OAAO,IAAI,OAAO,CAAC,OAAO,EAAE;QAChE,cAAc,CAAC,IAAI,CAAC,eAAQ,OAAO,CAAC,OAAO,CAAE,CAAC,CAAC;KAChD;IAED,IAAI,MAAM,CAAC,SAAS,EAAE;QACpB,cAAc,CAAC,IAAI,CAAC,qBAAc,kBAAkB,CAAC,MAAM,CAAC,SAAS,CAAC,CAAE,CAAC,CAAC;KAC3E;IAED,OAAO,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACnC,CAAC,CAAC;AAvBW,QAAA,cAAc,kBAuBzB;AAEF,IAAM,kBAAkB,GAAG,UAAC,SAAiB;IAC3C,IAAI,CAAC,SAAS,EAAE;QACd,OAAO;KACR;IAED;;;;;;OAMG;IACH,OAAO,SAAS;SACb,WAAW,EAAE;SACb,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC;SAC5B,IAAI,EAAE;SACN,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;AAC3B,CAAC,CAAC"}
#!/usr/bin/env node

// SUPER SIMPLE AI AGENT CREATOR - Guaranteed to work!

const axios = require('axios');

const API_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI0NDgyZmY2Yi0xNzcwLTRlZGQtOTY2Yy1hNTVjZTNkZDZmZGQiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzQ4NTAyNTMyfQ.Si6LRA-uLi8HIdWZIX6tKNXzrSJDd6gBqiRHwtf8K8M';

async function createSimpleAgent() {
    console.log('🤖 Creating super simple AI agent...');
    
    const workflow = {
        name: "Super Simple Chatbot",
        nodes: [
            {
                parameters: {
                    httpMethod: "POST",
                    path: "simple-chatbot"
                },
                id: "webhook",
                name: "Webhook",
                type: "n8n-nodes-base.webhook",
                typeVersion: 2,
                position: [300, 300]
            },
            {
                parameters: {
                    respondWith: "json",
                    responseBody: '{"success": true, "message": "Hello! I am your simple chatbot! You said: {{ $json.body.message || \'Hello\' }}", "webhook": "http://localhost:2410/webhook/simple-chatbot"}'
                },
                id: "respond",
                name: "Respond to Webhook",
                type: "n8n-nodes-base.respondToWebhook",
                typeVersion: 1,
                position: [500, 300]
            }
        ],
        connections: {
            "Webhook": {
                main: [[{
                    node: "Respond to Webhook",
                    type: "main",
                    index: 0
                }]]
            }
        },
        settings: {
            timezone: "UTC"
        }
    };

    try {
        // Create workflow
        console.log('📤 Creating workflow...');
        const response = await axios.post('http://localhost:2410/api/v1/workflows', workflow, {
            headers: {
                'Content-Type': 'application/json',
                'X-N8N-API-KEY': API_KEY
            }
        });

        console.log('✅ Workflow created! ID:', response.data.id);

        // Activate workflow
        console.log('⚡ Activating workflow...');
        await axios.post(`http://localhost:2410/api/v1/workflows/${response.data.id}/activate`, {}, {
            headers: {
                'X-N8N-API-KEY': API_KEY
            }
        });

        console.log('✅ Workflow activated!');
        console.log('');
        console.log('🎉 SUCCESS! Your simple chatbot is ready!');
        console.log('');
        console.log('📋 Agent Details:');
        console.log('   Name: Super Simple Chatbot');
        console.log('   Webhook: http://localhost:2410/webhook/simple-chatbot');
        console.log('');
        console.log('🧪 Test your agent:');
        console.log('Copy and paste this in PowerShell:');
        console.log('');
        console.log('$headers = @{"Content-Type" = "application/json"}');
        console.log('$body = @{message = "Hello chatbot!"} | ConvertTo-Json');
        console.log('Invoke-RestMethod -Uri "http://localhost:2410/webhook/simple-chatbot" -Method POST -Headers $headers -Body $body');

    } catch (error) {
        console.error('❌ Error:', error.response?.data || error.message);
    }
}

createSimpleAgent();

"use strict";
/* tslint:disable */
/* eslint-disable */
/**
 * Pinecone Inference API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.RerankRequestToJSON = exports.RerankRequestFromJSONTyped = exports.RerankRequestFromJSON = exports.instanceOfRerankRequest = void 0;
var runtime_1 = require("../runtime");
/**
 * Check if a given object implements the RerankRequest interface.
 */
function instanceOfRerankRequest(value) {
    var isInstance = true;
    isInstance = isInstance && "model" in value;
    isInstance = isInstance && "query" in value;
    isInstance = isInstance && "documents" in value;
    return isInstance;
}
exports.instanceOfRerankRequest = instanceOfRerankRequest;
function RerankRequestFromJSON(json) {
    return RerankRequestFromJSONTyped(json, false);
}
exports.RerankRequestFromJSON = RerankRequestFromJSON;
function RerankRequestFromJSONTyped(json, ignoreDiscriminator) {
    if ((json === undefined) || (json === null)) {
        return json;
    }
    return {
        'model': json['model'],
        'query': json['query'],
        'topN': !(0, runtime_1.exists)(json, 'top_n') ? undefined : json['top_n'],
        'returnDocuments': !(0, runtime_1.exists)(json, 'return_documents') ? undefined : json['return_documents'],
        'rankFields': !(0, runtime_1.exists)(json, 'rank_fields') ? undefined : json['rank_fields'],
        'documents': json['documents'],
        'parameters': !(0, runtime_1.exists)(json, 'parameters') ? undefined : json['parameters'],
    };
}
exports.RerankRequestFromJSONTyped = RerankRequestFromJSONTyped;
function RerankRequestToJSON(value) {
    if (value === undefined) {
        return undefined;
    }
    if (value === null) {
        return null;
    }
    return {
        'model': value.model,
        'query': value.query,
        'top_n': value.topN,
        'return_documents': value.returnDocuments,
        'rank_fields': value.rankFields,
        'documents': value.documents,
        'parameters': value.parameters,
    };
}
exports.RerankRequestToJSON = RerankRequestToJSON;
//# sourceMappingURL=RerankRequest.js.map
{"name": "Complete Meta AI Agent Builder", "nodes": [{"parameters": {"httpMethod": "POST", "path": "create-agent", "options": {}}, "id": "webhook-start", "name": "Webhook Start", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [240, 300], "webhookId": "create-agent"}, {"parameters": {"jsCode": "// Extract and validate the instruction\nconst body = $input.first().json.body || $input.first().json;\nconst instruction = body.instruction || body.prompt || body.request;\n\nif (!instruction) {\n  throw new Error('No instruction provided. Please include an \"instruction\" field in your request.');\n}\n\n// Clean and prepare the instruction\nconst cleanInstruction = instruction.trim();\n\nreturn {\n  json: {\n    instruction: cleanInstruction,\n    timestamp: new Date().toISOString(),\n    requestId: Math.random().toString(36).substring(7)\n  }\n};"}, "id": "process-input", "name": "Process Input", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"model": "gpt-4", "messages": {"messageType": "multipleMessages", "messages": [{"role": "system", "message": "You are an expert n8n workflow builder. Create complete, functional n8n workflows based on user instructions. Always respond with valid JSON that can be imported into n8n.\n\nIMPORTANT RULES:\n1. Include proper node IDs and positions\n2. Use realistic node configurations\n3. Include proper connections between nodes\n4. Add error handling where appropriate\n5. Make workflows production-ready\n6. Use common n8n node types\n\nReturn ONLY the workflow JSON, no explanations or markdown."}, {"role": "user", "message": "Create an n8n workflow for: {{ $json.instruction }}\n\nMake it complete and ready to use. Include:\n- Appropriate trigger nodes\n- Processing/logic nodes\n- Output/response nodes\n- Proper node connections\n- Realistic parameters\n\nWorkflow JSON:"}]}, "options": {"temperature": 0.3}}, "id": "ai-generator", "name": "AI Workflow Generator", "type": "n8n-nodes-base.openAi", "typeVersion": 1, "position": [680, 300], "credentials": {"openAiApi": {"id": "JxCgiyoxMKH952aV", "name": "OpenAI for Meta Agent"}}}, {"parameters": {"jsCode": "// Parse AI response and create workflow\nconst aiResponse = $input.first().json.message.content;\nconst originalInstruction = $('Process Input').first().json.instruction;\n\ntry {\n  // Clean the AI response\n  let cleanJson = aiResponse\n    .replace(/```json\\n?/g, '')\n    .replace(/```\\n?/g, '')\n    .replace(/^[^{]*/, '')\n    .replace(/[^}]*$/, '')\n    .trim();\n  \n  // Parse the workflow\n  const workflowData = JSON.parse(cleanJson);\n  \n  // Add metadata and ensure proper structure\n  const finalWorkflow = {\n    name: workflowData.name || `AI Agent: ${originalInstruction.substring(0, 50)}...`,\n    nodes: workflowData.nodes || [],\n    connections: workflowData.connections || {},\n    active: false,\n    settings: {\n      timezone: 'UTC',\n      saveManualExecutions: true\n    },\n    tags: ['ai-generated', 'meta-agent', new Date().toISOString().split('T')[0]]\n  };\n  \n  return {\n    json: {\n      success: true,\n      workflow: finalWorkflow,\n      instruction: originalInstruction,\n      aiResponse: aiResponse.substring(0, 500) + '...'\n    }\n  };\n  \n} catch (error) {\n  return {\n    json: {\n      success: false,\n      error: 'Failed to parse AI response',\n      details: error.message,\n      instruction: originalInstruction,\n      aiResponse: aiResponse\n    }\n  };\n}"}, "id": "parse-workflow", "name": "Parse Workflow", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 300]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "success-check", "leftValue": "={{ $json.success }}", "rightValue": true, "operator": {"type": "boolean", "operation": "equal"}}], "combinator": "and"}}, "id": "check-success", "name": "Check Success", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [1120, 300]}, {"parameters": {"url": "http://localhost:2410/api/v1/workflows", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "X-N8N-API-KEY", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI0NDgyZmY2Yi0xNzcwLTRlZGQtOTY2Yy1hNTVjZTNkZDZmZGQiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzQ4NTAyNTMyfQ.Si6LRA-uLi8HIdWZIX6tKNXzrSJDd6gBqiRHwtf8K8M"}]}, "sendBody": true, "contentType": "json", "body": "={{ JSON.stringify($json.workflow) }}"}, "id": "create-workflow-api", "name": "Create Workflow", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1340, 200]}, {"parameters": {"respondWith": "json", "responseBody": "={{ {\n  \"success\": true,\n  \"message\": \"🎉 AI Agent created successfully!\",\n  \"workflowId\": $json.id,\n  \"workflowName\": $json.name,\n  \"instruction\": $('Process Input').first().json.instruction,\n  \"webhookUrl\": $json.webhookUrl || \"Check workflow for webhook URL\",\n  \"nextSteps\": [\n    \"1. Go to http://localhost:2410\",\n    \"2. Find your new workflow: \" + $json.name,\n    \"3. Configure any required credentials\",\n    \"4. Activate the workflow\",\n    \"5. Start using your AI agent!\"\n  ],\n  \"tip\": \"Your AI agent is ready! Check the n8n interface to see your new workflow.\"\n} }}"}, "id": "success-response", "name": "Success Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1560, 200]}, {"parameters": {"respondWith": "json", "responseBody": "={{ {\n  \"success\": false,\n  \"message\": \"❌ Failed to create AI agent\",\n  \"error\": $json.error || \"Unknown error\",\n  \"instruction\": $json.instruction,\n  \"details\": $json.details || \"No additional details\",\n  \"suggestion\": \"Try rephrasing your instruction or check the logs\",\n  \"examples\": [\n    \"Create a chatbot that answers customer questions\",\n    \"Build an email automation for new subscribers\",\n    \"Make a social media content generator\"\n  ]\n} }}"}, "id": "error-response", "name": "Error Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1560, 400]}], "connections": {"Webhook Start": {"main": [[{"node": "Process Input", "type": "main", "index": 0}]]}, "Process Input": {"main": [[{"node": "AI Workflow Generator", "type": "main", "index": 0}]]}, "AI Workflow Generator": {"main": [[{"node": "Parse Workflow", "type": "main", "index": 0}]]}, "Parse Workflow": {"main": [[{"node": "Check Success", "type": "main", "index": 0}]]}, "Check Success": {"main": [[{"node": "Create Workflow", "type": "main", "index": 0}], [{"node": "Error Response", "type": "main", "index": 0}]]}, "Create Workflow": {"main": [[{"node": "Success Response", "type": "main", "index": 0}]]}}, "active": false, "settings": {"timezone": "UTC", "saveManualExecutions": true}, "tags": ["meta-agent", "ai-builder", "complete"]}
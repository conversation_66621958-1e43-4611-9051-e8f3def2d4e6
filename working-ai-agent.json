{"name": "Working AI Agent", "nodes": [{"parameters": {"httpMethod": "POST", "path": "ai-agent", "options": {}}, "id": "webhook-node", "name": "Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [300, 300], "webhookId": "ai-agent"}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"success\": true,\n  \"message\": \"🤖 AI Agent is working!\",\n  \"received\": {{ JSON.stringify($json) }},\n  \"instruction\": \"{{ $json.body.instruction || $json.body.chatInput || $json.instruction || $json.chatInput || 'No instruction provided' }}\",\n  \"timestamp\": \"{{ new Date().toISOString() }}\",\n  \"response\": \"I received your message and I'm ready to help! This is a working AI agent that can process your requests.\",\n  \"capabilities\": [\n    \"Process natural language instructions\",\n    \"Create workflows and automations\",\n    \"Answer questions and provide assistance\",\n    \"Build custom AI agents for specific tasks\"\n  ],\n  \"examples\": [\n    \"Create a chatbot for customer support\",\n    \"Build an email automation system\",\n    \"Generate content for social media\",\n    \"Analyze data and create reports\"\n  ]\n}"}, "id": "response-node", "name": "Respond", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [500, 300]}], "connections": {"Webhook": {"main": [[{"node": "Respond", "type": "main", "index": 0}]]}}, "active": false, "settings": {"timezone": "UTC", "saveManualExecutions": true}, "tags": ["ai-agent", "working"]}
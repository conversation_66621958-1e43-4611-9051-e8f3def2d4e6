@echo off
echo 🤖 CREATING WORKING AI AGENT...
echo ================================

REM Create a simple working workflow using curl
echo Creating chatbot agent...

curl -X POST "http://localhost:2410/api/v1/workflows" ^
  -H "Content-Type: application/json" ^
  -H "X-N8N-API-KEY: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI0NDgyZmY2Yi0xNzcwLTRlZGQtOTY2Yy1hNTVjZTNkZDZmZGQiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzQ4NTAyNTMyfQ.Si6LRA-uLi8HIdWZIX6tKNXzrSJDd6gBqiRHwtf8K8M" ^
  -d "{\"name\":\"Working Chatbot Agent\",\"nodes\":[{\"parameters\":{\"httpMethod\":\"POST\",\"path\":\"working-chatbot\",\"options\":{}},\"id\":\"webhook1\",\"name\":\"Webhook\",\"type\":\"n8n-nodes-base.webhook\",\"typeVersion\":2,\"position\":[300,300]},{\"parameters\":{\"respondWith\":\"json\",\"responseBody\":\"{\\\"success\\\":true,\\\"message\\\":\\\"Hello! I am your working chatbot. You said: {{ $json.body.message || 'Hello' }}\\\",\\\"agent\\\":\\\"Working Chatbot\\\",\\\"webhook\\\":\\\"http://localhost:2410/webhook/working-chatbot\\\",\\\"status\\\":\\\"✅ Working perfectly!\\\"}\"},\"id\":\"respond1\",\"name\":\"Respond\",\"type\":\"n8n-nodes-base.respondToWebhook\",\"typeVersion\":1,\"position\":[500,300]}],\"connections\":{\"Webhook\":{\"main\":[[{\"node\":\"Respond\",\"type\":\"main\",\"index\":0}]]}},\"settings\":{\"timezone\":\"UTC\"}}"

echo.
echo ✅ Workflow created! Now activating...

REM Note: You'll need to get the workflow ID from the response above and activate it manually
echo.
echo 🧪 Test your agent:
echo curl -X POST "http://localhost:2410/webhook/working-chatbot" -H "Content-Type: application/json" -d "{\"message\": \"Hello chatbot!\"}"
echo.
echo 🌐 View in n8n: http://localhost:2410
echo.
pause

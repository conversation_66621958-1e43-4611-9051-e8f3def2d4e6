{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,4CAK4B;AAC5B,8CAAgE;AAEhE,6DAA6D;AAC7D,8DAA8D;AAC9D,4EAA4E;AAC5E,SAAS,kBAAkB,CAAC,KAAa;IACvC,MAAM,2BAA2B,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IACxD,IAAI,2BAA2B,IAAI,CAAC,EAAE;QACpC,OAAO,IAAI,CAAC;KACb;IAED,MAAM,wBAAwB,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IACrD,IAAI,wBAAwB,GAAG,CAAC,EAAE;QAChC,OAAO,KAAK,CAAC;KACd;IAED,MAAM,wBAAwB,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IACrD,OAAO,2BAA2B,GAAG,wBAAwB,CAAC;AAChE,CAAC;AAED,+FAA+F;AAC/F,wGAAwG;AACxG,gFAAgF;AAChF,gDAAgD;AAChD,sGAAsG;AACtG,SAAS,uBAAuB,CAAC,GAAW;IAC1C,OAAO,kBAAkB,CAAC,GAAG,CAAC,CAAC,OAAO,CACpC,UAAU,EACV,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE,EAAE,CACtD,CAAC;AACJ,CAAC;AAED,SAAgB,sBAAsB,CAAC,IAAU,EAAE,KAAa;IAC9D,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;QACnD,OAAO,KAAK,CAAC;KACd;IAED,oFAAoF;IACpF,eAAe;IACf,IAAI,kBAAkB,CAAC,KAAK,CAAC,EAAE;QAC7B,OAAO,KAAK,CAAC;KACd;IAED,MAAM,UAAU,GAAG,IAAI,gCAAyB,EAAE,CAAC;IACnD,MAAM,OAAO,GAA8B,EAAE,CAAC;IAC9C,UAAU,CAAC,MAAM,CACf,WAAK,CAAC,OAAO,CAAC,kBAAY,EAAE,IAAI,CAAC,EACjC,OAAO,EACP,0BAAoB,CACrB,CAAC;IAEF,gFAAgF;IAChF,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,CAAC;IAE/C,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;QAC3B,OAAO,KAAK,CAAC;KACd;IAED,MAAM,aAAa,GAAG,UAAU;SAC7B,GAAG,CAAC,GAAG,CAAC,EAAE;QACT,MAAM,YAAY,GAAG,uBAAuB,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;QAC3D,OAAO,GAAG,GAAG,KAAK,YAAY,GAAG,CAAC;IACpC,CAAC,CAAC;SACD,IAAI,CAAC,GAAG,CAAC,CAAC;IAEb,OAAO,GAAG,KAAK,MAAM,aAAa,IAAI,CAAC;AACzC,CAAC;AAlCD,wDAkCC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  trace,\n  Span,\n  ROOT_CONTEXT,\n  defaultTextMapSetter,\n} from '@opentelemetry/api';\nimport { W3CTraceContextPropagator } from '@opentelemetry/core';\n\n// NOTE: This function currently is returning false-positives\n// in cases where comment characters appear in string literals\n// (\"SELECT '-- not a comment';\" would return true, although has no comment)\nfunction hasValidSqlComment(query: string): boolean {\n  const indexOpeningDashDashComment = query.indexOf('--');\n  if (indexOpeningDashDashComment >= 0) {\n    return true;\n  }\n\n  const indexOpeningSlashComment = query.indexOf('/*');\n  if (indexOpeningSlashComment < 0) {\n    return false;\n  }\n\n  const indexClosingSlashComment = query.indexOf('*/');\n  return indexOpeningDashDashComment < indexClosingSlashComment;\n}\n\n// sqlcommenter specification (https://google.github.io/sqlcommenter/spec/#value-serialization)\n// expects us to URL encode based on the RFC 3986 spec (https://en.wikipedia.org/wiki/Percent-encoding),\n// but encodeURIComponent does not handle some characters correctly (! ' ( ) *),\n// which means we need special handling for this\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/encodeURIComponent\nfunction fixedEncodeURIComponent(str: string) {\n  return encodeURIComponent(str).replace(\n    /[!'()*]/g,\n    c => `%${c.charCodeAt(0).toString(16).toUpperCase()}`\n  );\n}\n\nexport function addSqlCommenterComment(span: Span, query: string): string {\n  if (typeof query !== 'string' || query.length === 0) {\n    return query;\n  }\n\n  // As per sqlcommenter spec we shall not add a comment if there already is a comment\n  // in the query\n  if (hasValidSqlComment(query)) {\n    return query;\n  }\n\n  const propagator = new W3CTraceContextPropagator();\n  const headers: { [key: string]: string } = {};\n  propagator.inject(\n    trace.setSpan(ROOT_CONTEXT, span),\n    headers,\n    defaultTextMapSetter\n  );\n\n  // sqlcommenter spec requires keys in the comment to be sorted lexicographically\n  const sortedKeys = Object.keys(headers).sort();\n\n  if (sortedKeys.length === 0) {\n    return query;\n  }\n\n  const commentString = sortedKeys\n    .map(key => {\n      const encodedValue = fixedEncodeURIComponent(headers[key]);\n      return `${key}='${encodedValue}'`;\n    })\n    .join(',');\n\n  return `${query} /*${commentString}*/`;\n}\n"]}
/*
 * Copyright (c) Microsoft Corporation.
 * Licensed under the MIT License.
 *
 * Code generated by Microsoft (R) AutoRest Code Generator.
 * Changes may cause incorrect behavior and will be lost if the code is regenerated.
 */
export const BlobServiceProperties = {
    serializedName: "BlobServiceProperties",
    xmlName: "StorageServiceProperties",
    type: {
        name: "Composite",
        className: "BlobServiceProperties",
        modelProperties: {
            blobAnalyticsLogging: {
                serializedName: "Logging",
                xmlName: "Logging",
                type: {
                    name: "Composite",
                    className: "Logging",
                },
            },
            hourMetrics: {
                serializedName: "HourMetrics",
                xmlName: "HourMetrics",
                type: {
                    name: "Composite",
                    className: "Metrics",
                },
            },
            minuteMetrics: {
                serializedName: "MinuteMetrics",
                xmlName: "MinuteMetrics",
                type: {
                    name: "Composite",
                    className: "Metrics",
                },
            },
            cors: {
                serializedName: "Cors",
                xmlName: "Cors",
                xmlIsWrapped: true,
                xmlElementName: "CorsRule",
                type: {
                    name: "Sequence",
                    element: {
                        type: {
                            name: "Composite",
                            className: "CorsRule",
                        },
                    },
                },
            },
            defaultServiceVersion: {
                serializedName: "DefaultServiceVersion",
                xmlName: "DefaultServiceVersion",
                type: {
                    name: "String",
                },
            },
            deleteRetentionPolicy: {
                serializedName: "DeleteRetentionPolicy",
                xmlName: "DeleteRetentionPolicy",
                type: {
                    name: "Composite",
                    className: "RetentionPolicy",
                },
            },
            staticWebsite: {
                serializedName: "StaticWebsite",
                xmlName: "StaticWebsite",
                type: {
                    name: "Composite",
                    className: "StaticWebsite",
                },
            },
        },
    },
};
export const Logging = {
    serializedName: "Logging",
    type: {
        name: "Composite",
        className: "Logging",
        modelProperties: {
            version: {
                serializedName: "Version",
                required: true,
                xmlName: "Version",
                type: {
                    name: "String",
                },
            },
            deleteProperty: {
                serializedName: "Delete",
                required: true,
                xmlName: "Delete",
                type: {
                    name: "Boolean",
                },
            },
            read: {
                serializedName: "Read",
                required: true,
                xmlName: "Read",
                type: {
                    name: "Boolean",
                },
            },
            write: {
                serializedName: "Write",
                required: true,
                xmlName: "Write",
                type: {
                    name: "Boolean",
                },
            },
            retentionPolicy: {
                serializedName: "RetentionPolicy",
                xmlName: "RetentionPolicy",
                type: {
                    name: "Composite",
                    className: "RetentionPolicy",
                },
            },
        },
    },
};
export const RetentionPolicy = {
    serializedName: "RetentionPolicy",
    type: {
        name: "Composite",
        className: "RetentionPolicy",
        modelProperties: {
            enabled: {
                serializedName: "Enabled",
                required: true,
                xmlName: "Enabled",
                type: {
                    name: "Boolean",
                },
            },
            days: {
                constraints: {
                    InclusiveMinimum: 1,
                },
                serializedName: "Days",
                xmlName: "Days",
                type: {
                    name: "Number",
                },
            },
        },
    },
};
export const Metrics = {
    serializedName: "Metrics",
    type: {
        name: "Composite",
        className: "Metrics",
        modelProperties: {
            version: {
                serializedName: "Version",
                xmlName: "Version",
                type: {
                    name: "String",
                },
            },
            enabled: {
                serializedName: "Enabled",
                required: true,
                xmlName: "Enabled",
                type: {
                    name: "Boolean",
                },
            },
            includeAPIs: {
                serializedName: "IncludeAPIs",
                xmlName: "IncludeAPIs",
                type: {
                    name: "Boolean",
                },
            },
            retentionPolicy: {
                serializedName: "RetentionPolicy",
                xmlName: "RetentionPolicy",
                type: {
                    name: "Composite",
                    className: "RetentionPolicy",
                },
            },
        },
    },
};
export const CorsRule = {
    serializedName: "CorsRule",
    type: {
        name: "Composite",
        className: "CorsRule",
        modelProperties: {
            allowedOrigins: {
                serializedName: "AllowedOrigins",
                required: true,
                xmlName: "AllowedOrigins",
                type: {
                    name: "String",
                },
            },
            allowedMethods: {
                serializedName: "AllowedMethods",
                required: true,
                xmlName: "AllowedMethods",
                type: {
                    name: "String",
                },
            },
            allowedHeaders: {
                serializedName: "AllowedHeaders",
                required: true,
                xmlName: "AllowedHeaders",
                type: {
                    name: "String",
                },
            },
            exposedHeaders: {
                serializedName: "ExposedHeaders",
                required: true,
                xmlName: "ExposedHeaders",
                type: {
                    name: "String",
                },
            },
            maxAgeInSeconds: {
                constraints: {
                    InclusiveMinimum: 0,
                },
                serializedName: "MaxAgeInSeconds",
                required: true,
                xmlName: "MaxAgeInSeconds",
                type: {
                    name: "Number",
                },
            },
        },
    },
};
export const StaticWebsite = {
    serializedName: "StaticWebsite",
    type: {
        name: "Composite",
        className: "StaticWebsite",
        modelProperties: {
            enabled: {
                serializedName: "Enabled",
                required: true,
                xmlName: "Enabled",
                type: {
                    name: "Boolean",
                },
            },
            indexDocument: {
                serializedName: "IndexDocument",
                xmlName: "IndexDocument",
                type: {
                    name: "String",
                },
            },
            errorDocument404Path: {
                serializedName: "ErrorDocument404Path",
                xmlName: "ErrorDocument404Path",
                type: {
                    name: "String",
                },
            },
            defaultIndexDocumentPath: {
                serializedName: "DefaultIndexDocumentPath",
                xmlName: "DefaultIndexDocumentPath",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const StorageError = {
    serializedName: "StorageError",
    type: {
        name: "Composite",
        className: "StorageError",
        modelProperties: {
            message: {
                serializedName: "Message",
                xmlName: "Message",
                type: {
                    name: "String",
                },
            },
            code: {
                serializedName: "Code",
                xmlName: "Code",
                type: {
                    name: "String",
                },
            },
            authenticationErrorDetail: {
                serializedName: "AuthenticationErrorDetail",
                xmlName: "AuthenticationErrorDetail",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const BlobServiceStatistics = {
    serializedName: "BlobServiceStatistics",
    xmlName: "StorageServiceStats",
    type: {
        name: "Composite",
        className: "BlobServiceStatistics",
        modelProperties: {
            geoReplication: {
                serializedName: "GeoReplication",
                xmlName: "GeoReplication",
                type: {
                    name: "Composite",
                    className: "GeoReplication",
                },
            },
        },
    },
};
export const GeoReplication = {
    serializedName: "GeoReplication",
    type: {
        name: "Composite",
        className: "GeoReplication",
        modelProperties: {
            status: {
                serializedName: "Status",
                required: true,
                xmlName: "Status",
                type: {
                    name: "Enum",
                    allowedValues: ["live", "bootstrap", "unavailable"],
                },
            },
            lastSyncOn: {
                serializedName: "LastSyncTime",
                required: true,
                xmlName: "LastSyncTime",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
        },
    },
};
export const ListContainersSegmentResponse = {
    serializedName: "ListContainersSegmentResponse",
    xmlName: "EnumerationResults",
    type: {
        name: "Composite",
        className: "ListContainersSegmentResponse",
        modelProperties: {
            serviceEndpoint: {
                serializedName: "ServiceEndpoint",
                required: true,
                xmlName: "ServiceEndpoint",
                xmlIsAttribute: true,
                type: {
                    name: "String",
                },
            },
            prefix: {
                serializedName: "Prefix",
                xmlName: "Prefix",
                type: {
                    name: "String",
                },
            },
            marker: {
                serializedName: "Marker",
                xmlName: "Marker",
                type: {
                    name: "String",
                },
            },
            maxPageSize: {
                serializedName: "MaxResults",
                xmlName: "MaxResults",
                type: {
                    name: "Number",
                },
            },
            containerItems: {
                serializedName: "ContainerItems",
                required: true,
                xmlName: "Containers",
                xmlIsWrapped: true,
                xmlElementName: "Container",
                type: {
                    name: "Sequence",
                    element: {
                        type: {
                            name: "Composite",
                            className: "ContainerItem",
                        },
                    },
                },
            },
            continuationToken: {
                serializedName: "NextMarker",
                xmlName: "NextMarker",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const ContainerItem = {
    serializedName: "ContainerItem",
    xmlName: "Container",
    type: {
        name: "Composite",
        className: "ContainerItem",
        modelProperties: {
            name: {
                serializedName: "Name",
                required: true,
                xmlName: "Name",
                type: {
                    name: "String",
                },
            },
            deleted: {
                serializedName: "Deleted",
                xmlName: "Deleted",
                type: {
                    name: "Boolean",
                },
            },
            version: {
                serializedName: "Version",
                xmlName: "Version",
                type: {
                    name: "String",
                },
            },
            properties: {
                serializedName: "Properties",
                xmlName: "Properties",
                type: {
                    name: "Composite",
                    className: "ContainerProperties",
                },
            },
            metadata: {
                serializedName: "Metadata",
                xmlName: "Metadata",
                type: {
                    name: "Dictionary",
                    value: { type: { name: "String" } },
                },
            },
        },
    },
};
export const ContainerProperties = {
    serializedName: "ContainerProperties",
    type: {
        name: "Composite",
        className: "ContainerProperties",
        modelProperties: {
            lastModified: {
                serializedName: "Last-Modified",
                required: true,
                xmlName: "Last-Modified",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            etag: {
                serializedName: "Etag",
                required: true,
                xmlName: "Etag",
                type: {
                    name: "String",
                },
            },
            leaseStatus: {
                serializedName: "LeaseStatus",
                xmlName: "LeaseStatus",
                type: {
                    name: "Enum",
                    allowedValues: ["locked", "unlocked"],
                },
            },
            leaseState: {
                serializedName: "LeaseState",
                xmlName: "LeaseState",
                type: {
                    name: "Enum",
                    allowedValues: [
                        "available",
                        "leased",
                        "expired",
                        "breaking",
                        "broken",
                    ],
                },
            },
            leaseDuration: {
                serializedName: "LeaseDuration",
                xmlName: "LeaseDuration",
                type: {
                    name: "Enum",
                    allowedValues: ["infinite", "fixed"],
                },
            },
            publicAccess: {
                serializedName: "PublicAccess",
                xmlName: "PublicAccess",
                type: {
                    name: "Enum",
                    allowedValues: ["container", "blob"],
                },
            },
            hasImmutabilityPolicy: {
                serializedName: "HasImmutabilityPolicy",
                xmlName: "HasImmutabilityPolicy",
                type: {
                    name: "Boolean",
                },
            },
            hasLegalHold: {
                serializedName: "HasLegalHold",
                xmlName: "HasLegalHold",
                type: {
                    name: "Boolean",
                },
            },
            defaultEncryptionScope: {
                serializedName: "DefaultEncryptionScope",
                xmlName: "DefaultEncryptionScope",
                type: {
                    name: "String",
                },
            },
            preventEncryptionScopeOverride: {
                serializedName: "DenyEncryptionScopeOverride",
                xmlName: "DenyEncryptionScopeOverride",
                type: {
                    name: "Boolean",
                },
            },
            deletedOn: {
                serializedName: "DeletedTime",
                xmlName: "DeletedTime",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            remainingRetentionDays: {
                serializedName: "RemainingRetentionDays",
                xmlName: "RemainingRetentionDays",
                type: {
                    name: "Number",
                },
            },
            isImmutableStorageWithVersioningEnabled: {
                serializedName: "ImmutableStorageWithVersioningEnabled",
                xmlName: "ImmutableStorageWithVersioningEnabled",
                type: {
                    name: "Boolean",
                },
            },
        },
    },
};
export const KeyInfo = {
    serializedName: "KeyInfo",
    type: {
        name: "Composite",
        className: "KeyInfo",
        modelProperties: {
            startsOn: {
                serializedName: "Start",
                required: true,
                xmlName: "Start",
                type: {
                    name: "String",
                },
            },
            expiresOn: {
                serializedName: "Expiry",
                required: true,
                xmlName: "Expiry",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const UserDelegationKey = {
    serializedName: "UserDelegationKey",
    type: {
        name: "Composite",
        className: "UserDelegationKey",
        modelProperties: {
            signedObjectId: {
                serializedName: "SignedOid",
                required: true,
                xmlName: "SignedOid",
                type: {
                    name: "String",
                },
            },
            signedTenantId: {
                serializedName: "SignedTid",
                required: true,
                xmlName: "SignedTid",
                type: {
                    name: "String",
                },
            },
            signedStartsOn: {
                serializedName: "SignedStart",
                required: true,
                xmlName: "SignedStart",
                type: {
                    name: "String",
                },
            },
            signedExpiresOn: {
                serializedName: "SignedExpiry",
                required: true,
                xmlName: "SignedExpiry",
                type: {
                    name: "String",
                },
            },
            signedService: {
                serializedName: "SignedService",
                required: true,
                xmlName: "SignedService",
                type: {
                    name: "String",
                },
            },
            signedVersion: {
                serializedName: "SignedVersion",
                required: true,
                xmlName: "SignedVersion",
                type: {
                    name: "String",
                },
            },
            value: {
                serializedName: "Value",
                required: true,
                xmlName: "Value",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const FilterBlobSegment = {
    serializedName: "FilterBlobSegment",
    xmlName: "EnumerationResults",
    type: {
        name: "Composite",
        className: "FilterBlobSegment",
        modelProperties: {
            serviceEndpoint: {
                serializedName: "ServiceEndpoint",
                required: true,
                xmlName: "ServiceEndpoint",
                xmlIsAttribute: true,
                type: {
                    name: "String",
                },
            },
            where: {
                serializedName: "Where",
                required: true,
                xmlName: "Where",
                type: {
                    name: "String",
                },
            },
            blobs: {
                serializedName: "Blobs",
                required: true,
                xmlName: "Blobs",
                xmlIsWrapped: true,
                xmlElementName: "Blob",
                type: {
                    name: "Sequence",
                    element: {
                        type: {
                            name: "Composite",
                            className: "FilterBlobItem",
                        },
                    },
                },
            },
            continuationToken: {
                serializedName: "NextMarker",
                xmlName: "NextMarker",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const FilterBlobItem = {
    serializedName: "FilterBlobItem",
    xmlName: "Blob",
    type: {
        name: "Composite",
        className: "FilterBlobItem",
        modelProperties: {
            name: {
                serializedName: "Name",
                required: true,
                xmlName: "Name",
                type: {
                    name: "String",
                },
            },
            containerName: {
                serializedName: "ContainerName",
                required: true,
                xmlName: "ContainerName",
                type: {
                    name: "String",
                },
            },
            tags: {
                serializedName: "Tags",
                xmlName: "Tags",
                type: {
                    name: "Composite",
                    className: "BlobTags",
                },
            },
        },
    },
};
export const BlobTags = {
    serializedName: "BlobTags",
    xmlName: "Tags",
    type: {
        name: "Composite",
        className: "BlobTags",
        modelProperties: {
            blobTagSet: {
                serializedName: "BlobTagSet",
                required: true,
                xmlName: "TagSet",
                xmlIsWrapped: true,
                xmlElementName: "Tag",
                type: {
                    name: "Sequence",
                    element: {
                        type: {
                            name: "Composite",
                            className: "BlobTag",
                        },
                    },
                },
            },
        },
    },
};
export const BlobTag = {
    serializedName: "BlobTag",
    xmlName: "Tag",
    type: {
        name: "Composite",
        className: "BlobTag",
        modelProperties: {
            key: {
                serializedName: "Key",
                required: true,
                xmlName: "Key",
                type: {
                    name: "String",
                },
            },
            value: {
                serializedName: "Value",
                required: true,
                xmlName: "Value",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const SignedIdentifier = {
    serializedName: "SignedIdentifier",
    xmlName: "SignedIdentifier",
    type: {
        name: "Composite",
        className: "SignedIdentifier",
        modelProperties: {
            id: {
                serializedName: "Id",
                required: true,
                xmlName: "Id",
                type: {
                    name: "String",
                },
            },
            accessPolicy: {
                serializedName: "AccessPolicy",
                xmlName: "AccessPolicy",
                type: {
                    name: "Composite",
                    className: "AccessPolicy",
                },
            },
        },
    },
};
export const AccessPolicy = {
    serializedName: "AccessPolicy",
    type: {
        name: "Composite",
        className: "AccessPolicy",
        modelProperties: {
            startsOn: {
                serializedName: "Start",
                xmlName: "Start",
                type: {
                    name: "String",
                },
            },
            expiresOn: {
                serializedName: "Expiry",
                xmlName: "Expiry",
                type: {
                    name: "String",
                },
            },
            permissions: {
                serializedName: "Permission",
                xmlName: "Permission",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const ListBlobsFlatSegmentResponse = {
    serializedName: "ListBlobsFlatSegmentResponse",
    xmlName: "EnumerationResults",
    type: {
        name: "Composite",
        className: "ListBlobsFlatSegmentResponse",
        modelProperties: {
            serviceEndpoint: {
                serializedName: "ServiceEndpoint",
                required: true,
                xmlName: "ServiceEndpoint",
                xmlIsAttribute: true,
                type: {
                    name: "String",
                },
            },
            containerName: {
                serializedName: "ContainerName",
                required: true,
                xmlName: "ContainerName",
                xmlIsAttribute: true,
                type: {
                    name: "String",
                },
            },
            prefix: {
                serializedName: "Prefix",
                xmlName: "Prefix",
                type: {
                    name: "String",
                },
            },
            marker: {
                serializedName: "Marker",
                xmlName: "Marker",
                type: {
                    name: "String",
                },
            },
            maxPageSize: {
                serializedName: "MaxResults",
                xmlName: "MaxResults",
                type: {
                    name: "Number",
                },
            },
            segment: {
                serializedName: "Segment",
                xmlName: "Blobs",
                type: {
                    name: "Composite",
                    className: "BlobFlatListSegment",
                },
            },
            continuationToken: {
                serializedName: "NextMarker",
                xmlName: "NextMarker",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const BlobFlatListSegment = {
    serializedName: "BlobFlatListSegment",
    xmlName: "Blobs",
    type: {
        name: "Composite",
        className: "BlobFlatListSegment",
        modelProperties: {
            blobItems: {
                serializedName: "BlobItems",
                required: true,
                xmlName: "BlobItems",
                xmlElementName: "Blob",
                type: {
                    name: "Sequence",
                    element: {
                        type: {
                            name: "Composite",
                            className: "BlobItemInternal",
                        },
                    },
                },
            },
        },
    },
};
export const BlobItemInternal = {
    serializedName: "BlobItemInternal",
    xmlName: "Blob",
    type: {
        name: "Composite",
        className: "BlobItemInternal",
        modelProperties: {
            name: {
                serializedName: "Name",
                xmlName: "Name",
                type: {
                    name: "Composite",
                    className: "BlobName",
                },
            },
            deleted: {
                serializedName: "Deleted",
                required: true,
                xmlName: "Deleted",
                type: {
                    name: "Boolean",
                },
            },
            snapshot: {
                serializedName: "Snapshot",
                required: true,
                xmlName: "Snapshot",
                type: {
                    name: "String",
                },
            },
            versionId: {
                serializedName: "VersionId",
                xmlName: "VersionId",
                type: {
                    name: "String",
                },
            },
            isCurrentVersion: {
                serializedName: "IsCurrentVersion",
                xmlName: "IsCurrentVersion",
                type: {
                    name: "Boolean",
                },
            },
            properties: {
                serializedName: "Properties",
                xmlName: "Properties",
                type: {
                    name: "Composite",
                    className: "BlobPropertiesInternal",
                },
            },
            metadata: {
                serializedName: "Metadata",
                xmlName: "Metadata",
                type: {
                    name: "Dictionary",
                    value: { type: { name: "String" } },
                },
            },
            blobTags: {
                serializedName: "BlobTags",
                xmlName: "Tags",
                type: {
                    name: "Composite",
                    className: "BlobTags",
                },
            },
            objectReplicationMetadata: {
                serializedName: "ObjectReplicationMetadata",
                xmlName: "OrMetadata",
                type: {
                    name: "Dictionary",
                    value: { type: { name: "String" } },
                },
            },
            hasVersionsOnly: {
                serializedName: "HasVersionsOnly",
                xmlName: "HasVersionsOnly",
                type: {
                    name: "Boolean",
                },
            },
        },
    },
};
export const BlobName = {
    serializedName: "BlobName",
    type: {
        name: "Composite",
        className: "BlobName",
        modelProperties: {
            encoded: {
                serializedName: "Encoded",
                xmlName: "Encoded",
                xmlIsAttribute: true,
                type: {
                    name: "Boolean",
                },
            },
            content: {
                serializedName: "content",
                xmlName: "content",
                xmlIsMsText: true,
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const BlobPropertiesInternal = {
    serializedName: "BlobPropertiesInternal",
    xmlName: "Properties",
    type: {
        name: "Composite",
        className: "BlobPropertiesInternal",
        modelProperties: {
            createdOn: {
                serializedName: "Creation-Time",
                xmlName: "Creation-Time",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            lastModified: {
                serializedName: "Last-Modified",
                required: true,
                xmlName: "Last-Modified",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            etag: {
                serializedName: "Etag",
                required: true,
                xmlName: "Etag",
                type: {
                    name: "String",
                },
            },
            contentLength: {
                serializedName: "Content-Length",
                xmlName: "Content-Length",
                type: {
                    name: "Number",
                },
            },
            contentType: {
                serializedName: "Content-Type",
                xmlName: "Content-Type",
                type: {
                    name: "String",
                },
            },
            contentEncoding: {
                serializedName: "Content-Encoding",
                xmlName: "Content-Encoding",
                type: {
                    name: "String",
                },
            },
            contentLanguage: {
                serializedName: "Content-Language",
                xmlName: "Content-Language",
                type: {
                    name: "String",
                },
            },
            contentMD5: {
                serializedName: "Content-MD5",
                xmlName: "Content-MD5",
                type: {
                    name: "ByteArray",
                },
            },
            contentDisposition: {
                serializedName: "Content-Disposition",
                xmlName: "Content-Disposition",
                type: {
                    name: "String",
                },
            },
            cacheControl: {
                serializedName: "Cache-Control",
                xmlName: "Cache-Control",
                type: {
                    name: "String",
                },
            },
            blobSequenceNumber: {
                serializedName: "x-ms-blob-sequence-number",
                xmlName: "x-ms-blob-sequence-number",
                type: {
                    name: "Number",
                },
            },
            blobType: {
                serializedName: "BlobType",
                xmlName: "BlobType",
                type: {
                    name: "Enum",
                    allowedValues: ["BlockBlob", "PageBlob", "AppendBlob"],
                },
            },
            leaseStatus: {
                serializedName: "LeaseStatus",
                xmlName: "LeaseStatus",
                type: {
                    name: "Enum",
                    allowedValues: ["locked", "unlocked"],
                },
            },
            leaseState: {
                serializedName: "LeaseState",
                xmlName: "LeaseState",
                type: {
                    name: "Enum",
                    allowedValues: [
                        "available",
                        "leased",
                        "expired",
                        "breaking",
                        "broken",
                    ],
                },
            },
            leaseDuration: {
                serializedName: "LeaseDuration",
                xmlName: "LeaseDuration",
                type: {
                    name: "Enum",
                    allowedValues: ["infinite", "fixed"],
                },
            },
            copyId: {
                serializedName: "CopyId",
                xmlName: "CopyId",
                type: {
                    name: "String",
                },
            },
            copyStatus: {
                serializedName: "CopyStatus",
                xmlName: "CopyStatus",
                type: {
                    name: "Enum",
                    allowedValues: ["pending", "success", "aborted", "failed"],
                },
            },
            copySource: {
                serializedName: "CopySource",
                xmlName: "CopySource",
                type: {
                    name: "String",
                },
            },
            copyProgress: {
                serializedName: "CopyProgress",
                xmlName: "CopyProgress",
                type: {
                    name: "String",
                },
            },
            copyCompletedOn: {
                serializedName: "CopyCompletionTime",
                xmlName: "CopyCompletionTime",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            copyStatusDescription: {
                serializedName: "CopyStatusDescription",
                xmlName: "CopyStatusDescription",
                type: {
                    name: "String",
                },
            },
            serverEncrypted: {
                serializedName: "ServerEncrypted",
                xmlName: "ServerEncrypted",
                type: {
                    name: "Boolean",
                },
            },
            incrementalCopy: {
                serializedName: "IncrementalCopy",
                xmlName: "IncrementalCopy",
                type: {
                    name: "Boolean",
                },
            },
            destinationSnapshot: {
                serializedName: "DestinationSnapshot",
                xmlName: "DestinationSnapshot",
                type: {
                    name: "String",
                },
            },
            deletedOn: {
                serializedName: "DeletedTime",
                xmlName: "DeletedTime",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            remainingRetentionDays: {
                serializedName: "RemainingRetentionDays",
                xmlName: "RemainingRetentionDays",
                type: {
                    name: "Number",
                },
            },
            accessTier: {
                serializedName: "AccessTier",
                xmlName: "AccessTier",
                type: {
                    name: "Enum",
                    allowedValues: [
                        "P4",
                        "P6",
                        "P10",
                        "P15",
                        "P20",
                        "P30",
                        "P40",
                        "P50",
                        "P60",
                        "P70",
                        "P80",
                        "Hot",
                        "Cool",
                        "Archive",
                        "Cold",
                    ],
                },
            },
            accessTierInferred: {
                serializedName: "AccessTierInferred",
                xmlName: "AccessTierInferred",
                type: {
                    name: "Boolean",
                },
            },
            archiveStatus: {
                serializedName: "ArchiveStatus",
                xmlName: "ArchiveStatus",
                type: {
                    name: "Enum",
                    allowedValues: [
                        "rehydrate-pending-to-hot",
                        "rehydrate-pending-to-cool",
                        "rehydrate-pending-to-cold",
                    ],
                },
            },
            customerProvidedKeySha256: {
                serializedName: "CustomerProvidedKeySha256",
                xmlName: "CustomerProvidedKeySha256",
                type: {
                    name: "String",
                },
            },
            encryptionScope: {
                serializedName: "EncryptionScope",
                xmlName: "EncryptionScope",
                type: {
                    name: "String",
                },
            },
            accessTierChangedOn: {
                serializedName: "AccessTierChangeTime",
                xmlName: "AccessTierChangeTime",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            tagCount: {
                serializedName: "TagCount",
                xmlName: "TagCount",
                type: {
                    name: "Number",
                },
            },
            expiresOn: {
                serializedName: "Expiry-Time",
                xmlName: "Expiry-Time",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            isSealed: {
                serializedName: "Sealed",
                xmlName: "Sealed",
                type: {
                    name: "Boolean",
                },
            },
            rehydratePriority: {
                serializedName: "RehydratePriority",
                xmlName: "RehydratePriority",
                type: {
                    name: "Enum",
                    allowedValues: ["High", "Standard"],
                },
            },
            lastAccessedOn: {
                serializedName: "LastAccessTime",
                xmlName: "LastAccessTime",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            immutabilityPolicyExpiresOn: {
                serializedName: "ImmutabilityPolicyUntilDate",
                xmlName: "ImmutabilityPolicyUntilDate",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            immutabilityPolicyMode: {
                serializedName: "ImmutabilityPolicyMode",
                xmlName: "ImmutabilityPolicyMode",
                type: {
                    name: "Enum",
                    allowedValues: ["Mutable", "Unlocked", "Locked"],
                },
            },
            legalHold: {
                serializedName: "LegalHold",
                xmlName: "LegalHold",
                type: {
                    name: "Boolean",
                },
            },
        },
    },
};
export const ListBlobsHierarchySegmentResponse = {
    serializedName: "ListBlobsHierarchySegmentResponse",
    xmlName: "EnumerationResults",
    type: {
        name: "Composite",
        className: "ListBlobsHierarchySegmentResponse",
        modelProperties: {
            serviceEndpoint: {
                serializedName: "ServiceEndpoint",
                required: true,
                xmlName: "ServiceEndpoint",
                xmlIsAttribute: true,
                type: {
                    name: "String",
                },
            },
            containerName: {
                serializedName: "ContainerName",
                required: true,
                xmlName: "ContainerName",
                xmlIsAttribute: true,
                type: {
                    name: "String",
                },
            },
            prefix: {
                serializedName: "Prefix",
                xmlName: "Prefix",
                type: {
                    name: "String",
                },
            },
            marker: {
                serializedName: "Marker",
                xmlName: "Marker",
                type: {
                    name: "String",
                },
            },
            maxPageSize: {
                serializedName: "MaxResults",
                xmlName: "MaxResults",
                type: {
                    name: "Number",
                },
            },
            delimiter: {
                serializedName: "Delimiter",
                xmlName: "Delimiter",
                type: {
                    name: "String",
                },
            },
            segment: {
                serializedName: "Segment",
                xmlName: "Blobs",
                type: {
                    name: "Composite",
                    className: "BlobHierarchyListSegment",
                },
            },
            continuationToken: {
                serializedName: "NextMarker",
                xmlName: "NextMarker",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const BlobHierarchyListSegment = {
    serializedName: "BlobHierarchyListSegment",
    xmlName: "Blobs",
    type: {
        name: "Composite",
        className: "BlobHierarchyListSegment",
        modelProperties: {
            blobPrefixes: {
                serializedName: "BlobPrefixes",
                xmlName: "BlobPrefixes",
                xmlElementName: "BlobPrefix",
                type: {
                    name: "Sequence",
                    element: {
                        type: {
                            name: "Composite",
                            className: "BlobPrefix",
                        },
                    },
                },
            },
            blobItems: {
                serializedName: "BlobItems",
                required: true,
                xmlName: "BlobItems",
                xmlElementName: "Blob",
                type: {
                    name: "Sequence",
                    element: {
                        type: {
                            name: "Composite",
                            className: "BlobItemInternal",
                        },
                    },
                },
            },
        },
    },
};
export const BlobPrefix = {
    serializedName: "BlobPrefix",
    type: {
        name: "Composite",
        className: "BlobPrefix",
        modelProperties: {
            name: {
                serializedName: "Name",
                xmlName: "Name",
                type: {
                    name: "Composite",
                    className: "BlobName",
                },
            },
        },
    },
};
export const BlockLookupList = {
    serializedName: "BlockLookupList",
    xmlName: "BlockList",
    type: {
        name: "Composite",
        className: "BlockLookupList",
        modelProperties: {
            committed: {
                serializedName: "Committed",
                xmlName: "Committed",
                xmlElementName: "Committed",
                type: {
                    name: "Sequence",
                    element: {
                        type: {
                            name: "String",
                        },
                    },
                },
            },
            uncommitted: {
                serializedName: "Uncommitted",
                xmlName: "Uncommitted",
                xmlElementName: "Uncommitted",
                type: {
                    name: "Sequence",
                    element: {
                        type: {
                            name: "String",
                        },
                    },
                },
            },
            latest: {
                serializedName: "Latest",
                xmlName: "Latest",
                xmlElementName: "Latest",
                type: {
                    name: "Sequence",
                    element: {
                        type: {
                            name: "String",
                        },
                    },
                },
            },
        },
    },
};
export const BlockList = {
    serializedName: "BlockList",
    type: {
        name: "Composite",
        className: "BlockList",
        modelProperties: {
            committedBlocks: {
                serializedName: "CommittedBlocks",
                xmlName: "CommittedBlocks",
                xmlIsWrapped: true,
                xmlElementName: "Block",
                type: {
                    name: "Sequence",
                    element: {
                        type: {
                            name: "Composite",
                            className: "Block",
                        },
                    },
                },
            },
            uncommittedBlocks: {
                serializedName: "UncommittedBlocks",
                xmlName: "UncommittedBlocks",
                xmlIsWrapped: true,
                xmlElementName: "Block",
                type: {
                    name: "Sequence",
                    element: {
                        type: {
                            name: "Composite",
                            className: "Block",
                        },
                    },
                },
            },
        },
    },
};
export const Block = {
    serializedName: "Block",
    type: {
        name: "Composite",
        className: "Block",
        modelProperties: {
            name: {
                serializedName: "Name",
                required: true,
                xmlName: "Name",
                type: {
                    name: "String",
                },
            },
            size: {
                serializedName: "Size",
                required: true,
                xmlName: "Size",
                type: {
                    name: "Number",
                },
            },
        },
    },
};
export const PageList = {
    serializedName: "PageList",
    type: {
        name: "Composite",
        className: "PageList",
        modelProperties: {
            pageRange: {
                serializedName: "PageRange",
                xmlName: "PageRange",
                xmlElementName: "PageRange",
                type: {
                    name: "Sequence",
                    element: {
                        type: {
                            name: "Composite",
                            className: "PageRange",
                        },
                    },
                },
            },
            clearRange: {
                serializedName: "ClearRange",
                xmlName: "ClearRange",
                xmlElementName: "ClearRange",
                type: {
                    name: "Sequence",
                    element: {
                        type: {
                            name: "Composite",
                            className: "ClearRange",
                        },
                    },
                },
            },
            continuationToken: {
                serializedName: "NextMarker",
                xmlName: "NextMarker",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const PageRange = {
    serializedName: "PageRange",
    xmlName: "PageRange",
    type: {
        name: "Composite",
        className: "PageRange",
        modelProperties: {
            start: {
                serializedName: "Start",
                required: true,
                xmlName: "Start",
                type: {
                    name: "Number",
                },
            },
            end: {
                serializedName: "End",
                required: true,
                xmlName: "End",
                type: {
                    name: "Number",
                },
            },
        },
    },
};
export const ClearRange = {
    serializedName: "ClearRange",
    xmlName: "ClearRange",
    type: {
        name: "Composite",
        className: "ClearRange",
        modelProperties: {
            start: {
                serializedName: "Start",
                required: true,
                xmlName: "Start",
                type: {
                    name: "Number",
                },
            },
            end: {
                serializedName: "End",
                required: true,
                xmlName: "End",
                type: {
                    name: "Number",
                },
            },
        },
    },
};
export const QueryRequest = {
    serializedName: "QueryRequest",
    xmlName: "QueryRequest",
    type: {
        name: "Composite",
        className: "QueryRequest",
        modelProperties: {
            queryType: {
                serializedName: "QueryType",
                required: true,
                xmlName: "QueryType",
                type: {
                    name: "String",
                },
            },
            expression: {
                serializedName: "Expression",
                required: true,
                xmlName: "Expression",
                type: {
                    name: "String",
                },
            },
            inputSerialization: {
                serializedName: "InputSerialization",
                xmlName: "InputSerialization",
                type: {
                    name: "Composite",
                    className: "QuerySerialization",
                },
            },
            outputSerialization: {
                serializedName: "OutputSerialization",
                xmlName: "OutputSerialization",
                type: {
                    name: "Composite",
                    className: "QuerySerialization",
                },
            },
        },
    },
};
export const QuerySerialization = {
    serializedName: "QuerySerialization",
    type: {
        name: "Composite",
        className: "QuerySerialization",
        modelProperties: {
            format: {
                serializedName: "Format",
                xmlName: "Format",
                type: {
                    name: "Composite",
                    className: "QueryFormat",
                },
            },
        },
    },
};
export const QueryFormat = {
    serializedName: "QueryFormat",
    type: {
        name: "Composite",
        className: "QueryFormat",
        modelProperties: {
            type: {
                serializedName: "Type",
                required: true,
                xmlName: "Type",
                type: {
                    name: "Enum",
                    allowedValues: ["delimited", "json", "arrow", "parquet"],
                },
            },
            delimitedTextConfiguration: {
                serializedName: "DelimitedTextConfiguration",
                xmlName: "DelimitedTextConfiguration",
                type: {
                    name: "Composite",
                    className: "DelimitedTextConfiguration",
                },
            },
            jsonTextConfiguration: {
                serializedName: "JsonTextConfiguration",
                xmlName: "JsonTextConfiguration",
                type: {
                    name: "Composite",
                    className: "JsonTextConfiguration",
                },
            },
            arrowConfiguration: {
                serializedName: "ArrowConfiguration",
                xmlName: "ArrowConfiguration",
                type: {
                    name: "Composite",
                    className: "ArrowConfiguration",
                },
            },
            parquetTextConfiguration: {
                serializedName: "ParquetTextConfiguration",
                xmlName: "ParquetTextConfiguration",
                type: {
                    name: "Dictionary",
                    value: { type: { name: "any" } },
                },
            },
        },
    },
};
export const DelimitedTextConfiguration = {
    serializedName: "DelimitedTextConfiguration",
    xmlName: "DelimitedTextConfiguration",
    type: {
        name: "Composite",
        className: "DelimitedTextConfiguration",
        modelProperties: {
            columnSeparator: {
                serializedName: "ColumnSeparator",
                xmlName: "ColumnSeparator",
                type: {
                    name: "String",
                },
            },
            fieldQuote: {
                serializedName: "FieldQuote",
                xmlName: "FieldQuote",
                type: {
                    name: "String",
                },
            },
            recordSeparator: {
                serializedName: "RecordSeparator",
                xmlName: "RecordSeparator",
                type: {
                    name: "String",
                },
            },
            escapeChar: {
                serializedName: "EscapeChar",
                xmlName: "EscapeChar",
                type: {
                    name: "String",
                },
            },
            headersPresent: {
                serializedName: "HeadersPresent",
                xmlName: "HasHeaders",
                type: {
                    name: "Boolean",
                },
            },
        },
    },
};
export const JsonTextConfiguration = {
    serializedName: "JsonTextConfiguration",
    xmlName: "JsonTextConfiguration",
    type: {
        name: "Composite",
        className: "JsonTextConfiguration",
        modelProperties: {
            recordSeparator: {
                serializedName: "RecordSeparator",
                xmlName: "RecordSeparator",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const ArrowConfiguration = {
    serializedName: "ArrowConfiguration",
    xmlName: "ArrowConfiguration",
    type: {
        name: "Composite",
        className: "ArrowConfiguration",
        modelProperties: {
            schema: {
                serializedName: "Schema",
                required: true,
                xmlName: "Schema",
                xmlIsWrapped: true,
                xmlElementName: "Field",
                type: {
                    name: "Sequence",
                    element: {
                        type: {
                            name: "Composite",
                            className: "ArrowField",
                        },
                    },
                },
            },
        },
    },
};
export const ArrowField = {
    serializedName: "ArrowField",
    xmlName: "Field",
    type: {
        name: "Composite",
        className: "ArrowField",
        modelProperties: {
            type: {
                serializedName: "Type",
                required: true,
                xmlName: "Type",
                type: {
                    name: "String",
                },
            },
            name: {
                serializedName: "Name",
                xmlName: "Name",
                type: {
                    name: "String",
                },
            },
            precision: {
                serializedName: "Precision",
                xmlName: "Precision",
                type: {
                    name: "Number",
                },
            },
            scale: {
                serializedName: "Scale",
                xmlName: "Scale",
                type: {
                    name: "Number",
                },
            },
        },
    },
};
export const ServiceSetPropertiesHeaders = {
    serializedName: "Service_setPropertiesHeaders",
    type: {
        name: "Composite",
        className: "ServiceSetPropertiesHeaders",
        modelProperties: {
            clientRequestId: {
                serializedName: "x-ms-client-request-id",
                xmlName: "x-ms-client-request-id",
                type: {
                    name: "String",
                },
            },
            requestId: {
                serializedName: "x-ms-request-id",
                xmlName: "x-ms-request-id",
                type: {
                    name: "String",
                },
            },
            version: {
                serializedName: "x-ms-version",
                xmlName: "x-ms-version",
                type: {
                    name: "String",
                },
            },
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const ServiceSetPropertiesExceptionHeaders = {
    serializedName: "Service_setPropertiesExceptionHeaders",
    type: {
        name: "Composite",
        className: "ServiceSetPropertiesExceptionHeaders",
        modelProperties: {
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const ServiceGetPropertiesHeaders = {
    serializedName: "Service_getPropertiesHeaders",
    type: {
        name: "Composite",
        className: "ServiceGetPropertiesHeaders",
        modelProperties: {
            clientRequestId: {
                serializedName: "x-ms-client-request-id",
                xmlName: "x-ms-client-request-id",
                type: {
                    name: "String",
                },
            },
            requestId: {
                serializedName: "x-ms-request-id",
                xmlName: "x-ms-request-id",
                type: {
                    name: "String",
                },
            },
            version: {
                serializedName: "x-ms-version",
                xmlName: "x-ms-version",
                type: {
                    name: "String",
                },
            },
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const ServiceGetPropertiesExceptionHeaders = {
    serializedName: "Service_getPropertiesExceptionHeaders",
    type: {
        name: "Composite",
        className: "ServiceGetPropertiesExceptionHeaders",
        modelProperties: {
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const ServiceGetStatisticsHeaders = {
    serializedName: "Service_getStatisticsHeaders",
    type: {
        name: "Composite",
        className: "ServiceGetStatisticsHeaders",
        modelProperties: {
            clientRequestId: {
                serializedName: "x-ms-client-request-id",
                xmlName: "x-ms-client-request-id",
                type: {
                    name: "String",
                },
            },
            requestId: {
                serializedName: "x-ms-request-id",
                xmlName: "x-ms-request-id",
                type: {
                    name: "String",
                },
            },
            version: {
                serializedName: "x-ms-version",
                xmlName: "x-ms-version",
                type: {
                    name: "String",
                },
            },
            date: {
                serializedName: "date",
                xmlName: "date",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const ServiceGetStatisticsExceptionHeaders = {
    serializedName: "Service_getStatisticsExceptionHeaders",
    type: {
        name: "Composite",
        className: "ServiceGetStatisticsExceptionHeaders",
        modelProperties: {
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const ServiceListContainersSegmentHeaders = {
    serializedName: "Service_listContainersSegmentHeaders",
    type: {
        name: "Composite",
        className: "ServiceListContainersSegmentHeaders",
        modelProperties: {
            clientRequestId: {
                serializedName: "x-ms-client-request-id",
                xmlName: "x-ms-client-request-id",
                type: {
                    name: "String",
                },
            },
            requestId: {
                serializedName: "x-ms-request-id",
                xmlName: "x-ms-request-id",
                type: {
                    name: "String",
                },
            },
            version: {
                serializedName: "x-ms-version",
                xmlName: "x-ms-version",
                type: {
                    name: "String",
                },
            },
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const ServiceListContainersSegmentExceptionHeaders = {
    serializedName: "Service_listContainersSegmentExceptionHeaders",
    type: {
        name: "Composite",
        className: "ServiceListContainersSegmentExceptionHeaders",
        modelProperties: {
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const ServiceGetUserDelegationKeyHeaders = {
    serializedName: "Service_getUserDelegationKeyHeaders",
    type: {
        name: "Composite",
        className: "ServiceGetUserDelegationKeyHeaders",
        modelProperties: {
            clientRequestId: {
                serializedName: "x-ms-client-request-id",
                xmlName: "x-ms-client-request-id",
                type: {
                    name: "String",
                },
            },
            requestId: {
                serializedName: "x-ms-request-id",
                xmlName: "x-ms-request-id",
                type: {
                    name: "String",
                },
            },
            version: {
                serializedName: "x-ms-version",
                xmlName: "x-ms-version",
                type: {
                    name: "String",
                },
            },
            date: {
                serializedName: "date",
                xmlName: "date",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const ServiceGetUserDelegationKeyExceptionHeaders = {
    serializedName: "Service_getUserDelegationKeyExceptionHeaders",
    type: {
        name: "Composite",
        className: "ServiceGetUserDelegationKeyExceptionHeaders",
        modelProperties: {
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const ServiceGetAccountInfoHeaders = {
    serializedName: "Service_getAccountInfoHeaders",
    type: {
        name: "Composite",
        className: "ServiceGetAccountInfoHeaders",
        modelProperties: {
            clientRequestId: {
                serializedName: "x-ms-client-request-id",
                xmlName: "x-ms-client-request-id",
                type: {
                    name: "String",
                },
            },
            requestId: {
                serializedName: "x-ms-request-id",
                xmlName: "x-ms-request-id",
                type: {
                    name: "String",
                },
            },
            version: {
                serializedName: "x-ms-version",
                xmlName: "x-ms-version",
                type: {
                    name: "String",
                },
            },
            date: {
                serializedName: "date",
                xmlName: "date",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            skuName: {
                serializedName: "x-ms-sku-name",
                xmlName: "x-ms-sku-name",
                type: {
                    name: "Enum",
                    allowedValues: [
                        "Standard_LRS",
                        "Standard_GRS",
                        "Standard_RAGRS",
                        "Standard_ZRS",
                        "Premium_LRS",
                    ],
                },
            },
            accountKind: {
                serializedName: "x-ms-account-kind",
                xmlName: "x-ms-account-kind",
                type: {
                    name: "Enum",
                    allowedValues: [
                        "Storage",
                        "BlobStorage",
                        "StorageV2",
                        "FileStorage",
                        "BlockBlobStorage",
                    ],
                },
            },
            isHierarchicalNamespaceEnabled: {
                serializedName: "x-ms-is-hns-enabled",
                xmlName: "x-ms-is-hns-enabled",
                type: {
                    name: "Boolean",
                },
            },
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const ServiceGetAccountInfoExceptionHeaders = {
    serializedName: "Service_getAccountInfoExceptionHeaders",
    type: {
        name: "Composite",
        className: "ServiceGetAccountInfoExceptionHeaders",
        modelProperties: {
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const ServiceSubmitBatchHeaders = {
    serializedName: "Service_submitBatchHeaders",
    type: {
        name: "Composite",
        className: "ServiceSubmitBatchHeaders",
        modelProperties: {
            contentType: {
                serializedName: "content-type",
                xmlName: "content-type",
                type: {
                    name: "String",
                },
            },
            requestId: {
                serializedName: "x-ms-request-id",
                xmlName: "x-ms-request-id",
                type: {
                    name: "String",
                },
            },
            version: {
                serializedName: "x-ms-version",
                xmlName: "x-ms-version",
                type: {
                    name: "String",
                },
            },
            clientRequestId: {
                serializedName: "x-ms-client-request-id",
                xmlName: "x-ms-client-request-id",
                type: {
                    name: "String",
                },
            },
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const ServiceSubmitBatchExceptionHeaders = {
    serializedName: "Service_submitBatchExceptionHeaders",
    type: {
        name: "Composite",
        className: "ServiceSubmitBatchExceptionHeaders",
        modelProperties: {
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const ServiceFilterBlobsHeaders = {
    serializedName: "Service_filterBlobsHeaders",
    type: {
        name: "Composite",
        className: "ServiceFilterBlobsHeaders",
        modelProperties: {
            clientRequestId: {
                serializedName: "x-ms-client-request-id",
                xmlName: "x-ms-client-request-id",
                type: {
                    name: "String",
                },
            },
            requestId: {
                serializedName: "x-ms-request-id",
                xmlName: "x-ms-request-id",
                type: {
                    name: "String",
                },
            },
            version: {
                serializedName: "x-ms-version",
                xmlName: "x-ms-version",
                type: {
                    name: "String",
                },
            },
            date: {
                serializedName: "date",
                xmlName: "date",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const ServiceFilterBlobsExceptionHeaders = {
    serializedName: "Service_filterBlobsExceptionHeaders",
    type: {
        name: "Composite",
        className: "ServiceFilterBlobsExceptionHeaders",
        modelProperties: {
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const ContainerCreateHeaders = {
    serializedName: "Container_createHeaders",
    type: {
        name: "Composite",
        className: "ContainerCreateHeaders",
        modelProperties: {
            etag: {
                serializedName: "etag",
                xmlName: "etag",
                type: {
                    name: "String",
                },
            },
            lastModified: {
                serializedName: "last-modified",
                xmlName: "last-modified",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            clientRequestId: {
                serializedName: "x-ms-client-request-id",
                xmlName: "x-ms-client-request-id",
                type: {
                    name: "String",
                },
            },
            requestId: {
                serializedName: "x-ms-request-id",
                xmlName: "x-ms-request-id",
                type: {
                    name: "String",
                },
            },
            version: {
                serializedName: "x-ms-version",
                xmlName: "x-ms-version",
                type: {
                    name: "String",
                },
            },
            date: {
                serializedName: "date",
                xmlName: "date",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const ContainerCreateExceptionHeaders = {
    serializedName: "Container_createExceptionHeaders",
    type: {
        name: "Composite",
        className: "ContainerCreateExceptionHeaders",
        modelProperties: {
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const ContainerGetPropertiesHeaders = {
    serializedName: "Container_getPropertiesHeaders",
    type: {
        name: "Composite",
        className: "ContainerGetPropertiesHeaders",
        modelProperties: {
            metadata: {
                serializedName: "x-ms-meta",
                headerCollectionPrefix: "x-ms-meta-",
                xmlName: "x-ms-meta",
                type: {
                    name: "Dictionary",
                    value: { type: { name: "String" } },
                },
            },
            etag: {
                serializedName: "etag",
                xmlName: "etag",
                type: {
                    name: "String",
                },
            },
            lastModified: {
                serializedName: "last-modified",
                xmlName: "last-modified",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            leaseDuration: {
                serializedName: "x-ms-lease-duration",
                xmlName: "x-ms-lease-duration",
                type: {
                    name: "Enum",
                    allowedValues: ["infinite", "fixed"],
                },
            },
            leaseState: {
                serializedName: "x-ms-lease-state",
                xmlName: "x-ms-lease-state",
                type: {
                    name: "Enum",
                    allowedValues: [
                        "available",
                        "leased",
                        "expired",
                        "breaking",
                        "broken",
                    ],
                },
            },
            leaseStatus: {
                serializedName: "x-ms-lease-status",
                xmlName: "x-ms-lease-status",
                type: {
                    name: "Enum",
                    allowedValues: ["locked", "unlocked"],
                },
            },
            clientRequestId: {
                serializedName: "x-ms-client-request-id",
                xmlName: "x-ms-client-request-id",
                type: {
                    name: "String",
                },
            },
            requestId: {
                serializedName: "x-ms-request-id",
                xmlName: "x-ms-request-id",
                type: {
                    name: "String",
                },
            },
            version: {
                serializedName: "x-ms-version",
                xmlName: "x-ms-version",
                type: {
                    name: "String",
                },
            },
            date: {
                serializedName: "date",
                xmlName: "date",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            blobPublicAccess: {
                serializedName: "x-ms-blob-public-access",
                xmlName: "x-ms-blob-public-access",
                type: {
                    name: "Enum",
                    allowedValues: ["container", "blob"],
                },
            },
            hasImmutabilityPolicy: {
                serializedName: "x-ms-has-immutability-policy",
                xmlName: "x-ms-has-immutability-policy",
                type: {
                    name: "Boolean",
                },
            },
            hasLegalHold: {
                serializedName: "x-ms-has-legal-hold",
                xmlName: "x-ms-has-legal-hold",
                type: {
                    name: "Boolean",
                },
            },
            defaultEncryptionScope: {
                serializedName: "x-ms-default-encryption-scope",
                xmlName: "x-ms-default-encryption-scope",
                type: {
                    name: "String",
                },
            },
            denyEncryptionScopeOverride: {
                serializedName: "x-ms-deny-encryption-scope-override",
                xmlName: "x-ms-deny-encryption-scope-override",
                type: {
                    name: "Boolean",
                },
            },
            isImmutableStorageWithVersioningEnabled: {
                serializedName: "x-ms-immutable-storage-with-versioning-enabled",
                xmlName: "x-ms-immutable-storage-with-versioning-enabled",
                type: {
                    name: "Boolean",
                },
            },
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const ContainerGetPropertiesExceptionHeaders = {
    serializedName: "Container_getPropertiesExceptionHeaders",
    type: {
        name: "Composite",
        className: "ContainerGetPropertiesExceptionHeaders",
        modelProperties: {
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const ContainerDeleteHeaders = {
    serializedName: "Container_deleteHeaders",
    type: {
        name: "Composite",
        className: "ContainerDeleteHeaders",
        modelProperties: {
            clientRequestId: {
                serializedName: "x-ms-client-request-id",
                xmlName: "x-ms-client-request-id",
                type: {
                    name: "String",
                },
            },
            requestId: {
                serializedName: "x-ms-request-id",
                xmlName: "x-ms-request-id",
                type: {
                    name: "String",
                },
            },
            version: {
                serializedName: "x-ms-version",
                xmlName: "x-ms-version",
                type: {
                    name: "String",
                },
            },
            date: {
                serializedName: "date",
                xmlName: "date",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const ContainerDeleteExceptionHeaders = {
    serializedName: "Container_deleteExceptionHeaders",
    type: {
        name: "Composite",
        className: "ContainerDeleteExceptionHeaders",
        modelProperties: {
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const ContainerSetMetadataHeaders = {
    serializedName: "Container_setMetadataHeaders",
    type: {
        name: "Composite",
        className: "ContainerSetMetadataHeaders",
        modelProperties: {
            etag: {
                serializedName: "etag",
                xmlName: "etag",
                type: {
                    name: "String",
                },
            },
            lastModified: {
                serializedName: "last-modified",
                xmlName: "last-modified",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            clientRequestId: {
                serializedName: "x-ms-client-request-id",
                xmlName: "x-ms-client-request-id",
                type: {
                    name: "String",
                },
            },
            requestId: {
                serializedName: "x-ms-request-id",
                xmlName: "x-ms-request-id",
                type: {
                    name: "String",
                },
            },
            version: {
                serializedName: "x-ms-version",
                xmlName: "x-ms-version",
                type: {
                    name: "String",
                },
            },
            date: {
                serializedName: "date",
                xmlName: "date",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const ContainerSetMetadataExceptionHeaders = {
    serializedName: "Container_setMetadataExceptionHeaders",
    type: {
        name: "Composite",
        className: "ContainerSetMetadataExceptionHeaders",
        modelProperties: {
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const ContainerGetAccessPolicyHeaders = {
    serializedName: "Container_getAccessPolicyHeaders",
    type: {
        name: "Composite",
        className: "ContainerGetAccessPolicyHeaders",
        modelProperties: {
            blobPublicAccess: {
                serializedName: "x-ms-blob-public-access",
                xmlName: "x-ms-blob-public-access",
                type: {
                    name: "Enum",
                    allowedValues: ["container", "blob"],
                },
            },
            etag: {
                serializedName: "etag",
                xmlName: "etag",
                type: {
                    name: "String",
                },
            },
            lastModified: {
                serializedName: "last-modified",
                xmlName: "last-modified",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            clientRequestId: {
                serializedName: "x-ms-client-request-id",
                xmlName: "x-ms-client-request-id",
                type: {
                    name: "String",
                },
            },
            requestId: {
                serializedName: "x-ms-request-id",
                xmlName: "x-ms-request-id",
                type: {
                    name: "String",
                },
            },
            version: {
                serializedName: "x-ms-version",
                xmlName: "x-ms-version",
                type: {
                    name: "String",
                },
            },
            date: {
                serializedName: "date",
                xmlName: "date",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const ContainerGetAccessPolicyExceptionHeaders = {
    serializedName: "Container_getAccessPolicyExceptionHeaders",
    type: {
        name: "Composite",
        className: "ContainerGetAccessPolicyExceptionHeaders",
        modelProperties: {
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const ContainerSetAccessPolicyHeaders = {
    serializedName: "Container_setAccessPolicyHeaders",
    type: {
        name: "Composite",
        className: "ContainerSetAccessPolicyHeaders",
        modelProperties: {
            etag: {
                serializedName: "etag",
                xmlName: "etag",
                type: {
                    name: "String",
                },
            },
            lastModified: {
                serializedName: "last-modified",
                xmlName: "last-modified",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            clientRequestId: {
                serializedName: "x-ms-client-request-id",
                xmlName: "x-ms-client-request-id",
                type: {
                    name: "String",
                },
            },
            requestId: {
                serializedName: "x-ms-request-id",
                xmlName: "x-ms-request-id",
                type: {
                    name: "String",
                },
            },
            version: {
                serializedName: "x-ms-version",
                xmlName: "x-ms-version",
                type: {
                    name: "String",
                },
            },
            date: {
                serializedName: "date",
                xmlName: "date",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const ContainerSetAccessPolicyExceptionHeaders = {
    serializedName: "Container_setAccessPolicyExceptionHeaders",
    type: {
        name: "Composite",
        className: "ContainerSetAccessPolicyExceptionHeaders",
        modelProperties: {
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const ContainerRestoreHeaders = {
    serializedName: "Container_restoreHeaders",
    type: {
        name: "Composite",
        className: "ContainerRestoreHeaders",
        modelProperties: {
            clientRequestId: {
                serializedName: "x-ms-client-request-id",
                xmlName: "x-ms-client-request-id",
                type: {
                    name: "String",
                },
            },
            requestId: {
                serializedName: "x-ms-request-id",
                xmlName: "x-ms-request-id",
                type: {
                    name: "String",
                },
            },
            version: {
                serializedName: "x-ms-version",
                xmlName: "x-ms-version",
                type: {
                    name: "String",
                },
            },
            date: {
                serializedName: "date",
                xmlName: "date",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const ContainerRestoreExceptionHeaders = {
    serializedName: "Container_restoreExceptionHeaders",
    type: {
        name: "Composite",
        className: "ContainerRestoreExceptionHeaders",
        modelProperties: {
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const ContainerRenameHeaders = {
    serializedName: "Container_renameHeaders",
    type: {
        name: "Composite",
        className: "ContainerRenameHeaders",
        modelProperties: {
            clientRequestId: {
                serializedName: "x-ms-client-request-id",
                xmlName: "x-ms-client-request-id",
                type: {
                    name: "String",
                },
            },
            requestId: {
                serializedName: "x-ms-request-id",
                xmlName: "x-ms-request-id",
                type: {
                    name: "String",
                },
            },
            version: {
                serializedName: "x-ms-version",
                xmlName: "x-ms-version",
                type: {
                    name: "String",
                },
            },
            date: {
                serializedName: "date",
                xmlName: "date",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const ContainerRenameExceptionHeaders = {
    serializedName: "Container_renameExceptionHeaders",
    type: {
        name: "Composite",
        className: "ContainerRenameExceptionHeaders",
        modelProperties: {
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const ContainerSubmitBatchHeaders = {
    serializedName: "Container_submitBatchHeaders",
    type: {
        name: "Composite",
        className: "ContainerSubmitBatchHeaders",
        modelProperties: {
            contentType: {
                serializedName: "content-type",
                xmlName: "content-type",
                type: {
                    name: "String",
                },
            },
            requestId: {
                serializedName: "x-ms-request-id",
                xmlName: "x-ms-request-id",
                type: {
                    name: "String",
                },
            },
            version: {
                serializedName: "x-ms-version",
                xmlName: "x-ms-version",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const ContainerSubmitBatchExceptionHeaders = {
    serializedName: "Container_submitBatchExceptionHeaders",
    type: {
        name: "Composite",
        className: "ContainerSubmitBatchExceptionHeaders",
        modelProperties: {
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const ContainerFilterBlobsHeaders = {
    serializedName: "Container_filterBlobsHeaders",
    type: {
        name: "Composite",
        className: "ContainerFilterBlobsHeaders",
        modelProperties: {
            clientRequestId: {
                serializedName: "x-ms-client-request-id",
                xmlName: "x-ms-client-request-id",
                type: {
                    name: "String",
                },
            },
            requestId: {
                serializedName: "x-ms-request-id",
                xmlName: "x-ms-request-id",
                type: {
                    name: "String",
                },
            },
            version: {
                serializedName: "x-ms-version",
                xmlName: "x-ms-version",
                type: {
                    name: "String",
                },
            },
            date: {
                serializedName: "date",
                xmlName: "date",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
        },
    },
};
export const ContainerFilterBlobsExceptionHeaders = {
    serializedName: "Container_filterBlobsExceptionHeaders",
    type: {
        name: "Composite",
        className: "ContainerFilterBlobsExceptionHeaders",
        modelProperties: {
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const ContainerAcquireLeaseHeaders = {
    serializedName: "Container_acquireLeaseHeaders",
    type: {
        name: "Composite",
        className: "ContainerAcquireLeaseHeaders",
        modelProperties: {
            etag: {
                serializedName: "etag",
                xmlName: "etag",
                type: {
                    name: "String",
                },
            },
            lastModified: {
                serializedName: "last-modified",
                xmlName: "last-modified",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            leaseId: {
                serializedName: "x-ms-lease-id",
                xmlName: "x-ms-lease-id",
                type: {
                    name: "String",
                },
            },
            clientRequestId: {
                serializedName: "x-ms-client-request-id",
                xmlName: "x-ms-client-request-id",
                type: {
                    name: "String",
                },
            },
            requestId: {
                serializedName: "x-ms-request-id",
                xmlName: "x-ms-request-id",
                type: {
                    name: "String",
                },
            },
            version: {
                serializedName: "x-ms-version",
                xmlName: "x-ms-version",
                type: {
                    name: "String",
                },
            },
            date: {
                serializedName: "date",
                xmlName: "date",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
        },
    },
};
export const ContainerAcquireLeaseExceptionHeaders = {
    serializedName: "Container_acquireLeaseExceptionHeaders",
    type: {
        name: "Composite",
        className: "ContainerAcquireLeaseExceptionHeaders",
        modelProperties: {
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const ContainerReleaseLeaseHeaders = {
    serializedName: "Container_releaseLeaseHeaders",
    type: {
        name: "Composite",
        className: "ContainerReleaseLeaseHeaders",
        modelProperties: {
            etag: {
                serializedName: "etag",
                xmlName: "etag",
                type: {
                    name: "String",
                },
            },
            lastModified: {
                serializedName: "last-modified",
                xmlName: "last-modified",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            clientRequestId: {
                serializedName: "x-ms-client-request-id",
                xmlName: "x-ms-client-request-id",
                type: {
                    name: "String",
                },
            },
            requestId: {
                serializedName: "x-ms-request-id",
                xmlName: "x-ms-request-id",
                type: {
                    name: "String",
                },
            },
            version: {
                serializedName: "x-ms-version",
                xmlName: "x-ms-version",
                type: {
                    name: "String",
                },
            },
            date: {
                serializedName: "date",
                xmlName: "date",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
        },
    },
};
export const ContainerReleaseLeaseExceptionHeaders = {
    serializedName: "Container_releaseLeaseExceptionHeaders",
    type: {
        name: "Composite",
        className: "ContainerReleaseLeaseExceptionHeaders",
        modelProperties: {
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const ContainerRenewLeaseHeaders = {
    serializedName: "Container_renewLeaseHeaders",
    type: {
        name: "Composite",
        className: "ContainerRenewLeaseHeaders",
        modelProperties: {
            etag: {
                serializedName: "etag",
                xmlName: "etag",
                type: {
                    name: "String",
                },
            },
            lastModified: {
                serializedName: "last-modified",
                xmlName: "last-modified",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            leaseId: {
                serializedName: "x-ms-lease-id",
                xmlName: "x-ms-lease-id",
                type: {
                    name: "String",
                },
            },
            clientRequestId: {
                serializedName: "x-ms-client-request-id",
                xmlName: "x-ms-client-request-id",
                type: {
                    name: "String",
                },
            },
            requestId: {
                serializedName: "x-ms-request-id",
                xmlName: "x-ms-request-id",
                type: {
                    name: "String",
                },
            },
            version: {
                serializedName: "x-ms-version",
                xmlName: "x-ms-version",
                type: {
                    name: "String",
                },
            },
            date: {
                serializedName: "date",
                xmlName: "date",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
        },
    },
};
export const ContainerRenewLeaseExceptionHeaders = {
    serializedName: "Container_renewLeaseExceptionHeaders",
    type: {
        name: "Composite",
        className: "ContainerRenewLeaseExceptionHeaders",
        modelProperties: {
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const ContainerBreakLeaseHeaders = {
    serializedName: "Container_breakLeaseHeaders",
    type: {
        name: "Composite",
        className: "ContainerBreakLeaseHeaders",
        modelProperties: {
            etag: {
                serializedName: "etag",
                xmlName: "etag",
                type: {
                    name: "String",
                },
            },
            lastModified: {
                serializedName: "last-modified",
                xmlName: "last-modified",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            leaseTime: {
                serializedName: "x-ms-lease-time",
                xmlName: "x-ms-lease-time",
                type: {
                    name: "Number",
                },
            },
            clientRequestId: {
                serializedName: "x-ms-client-request-id",
                xmlName: "x-ms-client-request-id",
                type: {
                    name: "String",
                },
            },
            requestId: {
                serializedName: "x-ms-request-id",
                xmlName: "x-ms-request-id",
                type: {
                    name: "String",
                },
            },
            version: {
                serializedName: "x-ms-version",
                xmlName: "x-ms-version",
                type: {
                    name: "String",
                },
            },
            date: {
                serializedName: "date",
                xmlName: "date",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
        },
    },
};
export const ContainerBreakLeaseExceptionHeaders = {
    serializedName: "Container_breakLeaseExceptionHeaders",
    type: {
        name: "Composite",
        className: "ContainerBreakLeaseExceptionHeaders",
        modelProperties: {
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const ContainerChangeLeaseHeaders = {
    serializedName: "Container_changeLeaseHeaders",
    type: {
        name: "Composite",
        className: "ContainerChangeLeaseHeaders",
        modelProperties: {
            etag: {
                serializedName: "etag",
                xmlName: "etag",
                type: {
                    name: "String",
                },
            },
            lastModified: {
                serializedName: "last-modified",
                xmlName: "last-modified",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            leaseId: {
                serializedName: "x-ms-lease-id",
                xmlName: "x-ms-lease-id",
                type: {
                    name: "String",
                },
            },
            clientRequestId: {
                serializedName: "x-ms-client-request-id",
                xmlName: "x-ms-client-request-id",
                type: {
                    name: "String",
                },
            },
            requestId: {
                serializedName: "x-ms-request-id",
                xmlName: "x-ms-request-id",
                type: {
                    name: "String",
                },
            },
            version: {
                serializedName: "x-ms-version",
                xmlName: "x-ms-version",
                type: {
                    name: "String",
                },
            },
            date: {
                serializedName: "date",
                xmlName: "date",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
        },
    },
};
export const ContainerChangeLeaseExceptionHeaders = {
    serializedName: "Container_changeLeaseExceptionHeaders",
    type: {
        name: "Composite",
        className: "ContainerChangeLeaseExceptionHeaders",
        modelProperties: {
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const ContainerListBlobFlatSegmentHeaders = {
    serializedName: "Container_listBlobFlatSegmentHeaders",
    type: {
        name: "Composite",
        className: "ContainerListBlobFlatSegmentHeaders",
        modelProperties: {
            contentType: {
                serializedName: "content-type",
                xmlName: "content-type",
                type: {
                    name: "String",
                },
            },
            clientRequestId: {
                serializedName: "x-ms-client-request-id",
                xmlName: "x-ms-client-request-id",
                type: {
                    name: "String",
                },
            },
            requestId: {
                serializedName: "x-ms-request-id",
                xmlName: "x-ms-request-id",
                type: {
                    name: "String",
                },
            },
            version: {
                serializedName: "x-ms-version",
                xmlName: "x-ms-version",
                type: {
                    name: "String",
                },
            },
            date: {
                serializedName: "date",
                xmlName: "date",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const ContainerListBlobFlatSegmentExceptionHeaders = {
    serializedName: "Container_listBlobFlatSegmentExceptionHeaders",
    type: {
        name: "Composite",
        className: "ContainerListBlobFlatSegmentExceptionHeaders",
        modelProperties: {
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const ContainerListBlobHierarchySegmentHeaders = {
    serializedName: "Container_listBlobHierarchySegmentHeaders",
    type: {
        name: "Composite",
        className: "ContainerListBlobHierarchySegmentHeaders",
        modelProperties: {
            contentType: {
                serializedName: "content-type",
                xmlName: "content-type",
                type: {
                    name: "String",
                },
            },
            clientRequestId: {
                serializedName: "x-ms-client-request-id",
                xmlName: "x-ms-client-request-id",
                type: {
                    name: "String",
                },
            },
            requestId: {
                serializedName: "x-ms-request-id",
                xmlName: "x-ms-request-id",
                type: {
                    name: "String",
                },
            },
            version: {
                serializedName: "x-ms-version",
                xmlName: "x-ms-version",
                type: {
                    name: "String",
                },
            },
            date: {
                serializedName: "date",
                xmlName: "date",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const ContainerListBlobHierarchySegmentExceptionHeaders = {
    serializedName: "Container_listBlobHierarchySegmentExceptionHeaders",
    type: {
        name: "Composite",
        className: "ContainerListBlobHierarchySegmentExceptionHeaders",
        modelProperties: {
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const ContainerGetAccountInfoHeaders = {
    serializedName: "Container_getAccountInfoHeaders",
    type: {
        name: "Composite",
        className: "ContainerGetAccountInfoHeaders",
        modelProperties: {
            clientRequestId: {
                serializedName: "x-ms-client-request-id",
                xmlName: "x-ms-client-request-id",
                type: {
                    name: "String",
                },
            },
            requestId: {
                serializedName: "x-ms-request-id",
                xmlName: "x-ms-request-id",
                type: {
                    name: "String",
                },
            },
            version: {
                serializedName: "x-ms-version",
                xmlName: "x-ms-version",
                type: {
                    name: "String",
                },
            },
            date: {
                serializedName: "date",
                xmlName: "date",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            skuName: {
                serializedName: "x-ms-sku-name",
                xmlName: "x-ms-sku-name",
                type: {
                    name: "Enum",
                    allowedValues: [
                        "Standard_LRS",
                        "Standard_GRS",
                        "Standard_RAGRS",
                        "Standard_ZRS",
                        "Premium_LRS",
                    ],
                },
            },
            accountKind: {
                serializedName: "x-ms-account-kind",
                xmlName: "x-ms-account-kind",
                type: {
                    name: "Enum",
                    allowedValues: [
                        "Storage",
                        "BlobStorage",
                        "StorageV2",
                        "FileStorage",
                        "BlockBlobStorage",
                    ],
                },
            },
            isHierarchicalNamespaceEnabled: {
                serializedName: "x-ms-is-hns-enabled",
                xmlName: "x-ms-is-hns-enabled",
                type: {
                    name: "Boolean",
                },
            },
        },
    },
};
export const ContainerGetAccountInfoExceptionHeaders = {
    serializedName: "Container_getAccountInfoExceptionHeaders",
    type: {
        name: "Composite",
        className: "ContainerGetAccountInfoExceptionHeaders",
        modelProperties: {
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const BlobDownloadHeaders = {
    serializedName: "Blob_downloadHeaders",
    type: {
        name: "Composite",
        className: "BlobDownloadHeaders",
        modelProperties: {
            lastModified: {
                serializedName: "last-modified",
                xmlName: "last-modified",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            createdOn: {
                serializedName: "x-ms-creation-time",
                xmlName: "x-ms-creation-time",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            metadata: {
                serializedName: "x-ms-meta",
                headerCollectionPrefix: "x-ms-meta-",
                xmlName: "x-ms-meta",
                type: {
                    name: "Dictionary",
                    value: { type: { name: "String" } },
                },
            },
            objectReplicationPolicyId: {
                serializedName: "x-ms-or-policy-id",
                xmlName: "x-ms-or-policy-id",
                type: {
                    name: "String",
                },
            },
            objectReplicationRules: {
                serializedName: "x-ms-or",
                headerCollectionPrefix: "x-ms-or-",
                xmlName: "x-ms-or",
                type: {
                    name: "Dictionary",
                    value: { type: { name: "String" } },
                },
            },
            contentLength: {
                serializedName: "content-length",
                xmlName: "content-length",
                type: {
                    name: "Number",
                },
            },
            contentType: {
                serializedName: "content-type",
                xmlName: "content-type",
                type: {
                    name: "String",
                },
            },
            contentRange: {
                serializedName: "content-range",
                xmlName: "content-range",
                type: {
                    name: "String",
                },
            },
            etag: {
                serializedName: "etag",
                xmlName: "etag",
                type: {
                    name: "String",
                },
            },
            contentMD5: {
                serializedName: "content-md5",
                xmlName: "content-md5",
                type: {
                    name: "ByteArray",
                },
            },
            contentEncoding: {
                serializedName: "content-encoding",
                xmlName: "content-encoding",
                type: {
                    name: "String",
                },
            },
            cacheControl: {
                serializedName: "cache-control",
                xmlName: "cache-control",
                type: {
                    name: "String",
                },
            },
            contentDisposition: {
                serializedName: "content-disposition",
                xmlName: "content-disposition",
                type: {
                    name: "String",
                },
            },
            contentLanguage: {
                serializedName: "content-language",
                xmlName: "content-language",
                type: {
                    name: "String",
                },
            },
            blobSequenceNumber: {
                serializedName: "x-ms-blob-sequence-number",
                xmlName: "x-ms-blob-sequence-number",
                type: {
                    name: "Number",
                },
            },
            blobType: {
                serializedName: "x-ms-blob-type",
                xmlName: "x-ms-blob-type",
                type: {
                    name: "Enum",
                    allowedValues: ["BlockBlob", "PageBlob", "AppendBlob"],
                },
            },
            copyCompletedOn: {
                serializedName: "x-ms-copy-completion-time",
                xmlName: "x-ms-copy-completion-time",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            copyStatusDescription: {
                serializedName: "x-ms-copy-status-description",
                xmlName: "x-ms-copy-status-description",
                type: {
                    name: "String",
                },
            },
            copyId: {
                serializedName: "x-ms-copy-id",
                xmlName: "x-ms-copy-id",
                type: {
                    name: "String",
                },
            },
            copyProgress: {
                serializedName: "x-ms-copy-progress",
                xmlName: "x-ms-copy-progress",
                type: {
                    name: "String",
                },
            },
            copySource: {
                serializedName: "x-ms-copy-source",
                xmlName: "x-ms-copy-source",
                type: {
                    name: "String",
                },
            },
            copyStatus: {
                serializedName: "x-ms-copy-status",
                xmlName: "x-ms-copy-status",
                type: {
                    name: "Enum",
                    allowedValues: ["pending", "success", "aborted", "failed"],
                },
            },
            leaseDuration: {
                serializedName: "x-ms-lease-duration",
                xmlName: "x-ms-lease-duration",
                type: {
                    name: "Enum",
                    allowedValues: ["infinite", "fixed"],
                },
            },
            leaseState: {
                serializedName: "x-ms-lease-state",
                xmlName: "x-ms-lease-state",
                type: {
                    name: "Enum",
                    allowedValues: [
                        "available",
                        "leased",
                        "expired",
                        "breaking",
                        "broken",
                    ],
                },
            },
            leaseStatus: {
                serializedName: "x-ms-lease-status",
                xmlName: "x-ms-lease-status",
                type: {
                    name: "Enum",
                    allowedValues: ["locked", "unlocked"],
                },
            },
            clientRequestId: {
                serializedName: "x-ms-client-request-id",
                xmlName: "x-ms-client-request-id",
                type: {
                    name: "String",
                },
            },
            requestId: {
                serializedName: "x-ms-request-id",
                xmlName: "x-ms-request-id",
                type: {
                    name: "String",
                },
            },
            version: {
                serializedName: "x-ms-version",
                xmlName: "x-ms-version",
                type: {
                    name: "String",
                },
            },
            versionId: {
                serializedName: "x-ms-version-id",
                xmlName: "x-ms-version-id",
                type: {
                    name: "String",
                },
            },
            isCurrentVersion: {
                serializedName: "x-ms-is-current-version",
                xmlName: "x-ms-is-current-version",
                type: {
                    name: "Boolean",
                },
            },
            acceptRanges: {
                serializedName: "accept-ranges",
                xmlName: "accept-ranges",
                type: {
                    name: "String",
                },
            },
            date: {
                serializedName: "date",
                xmlName: "date",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            blobCommittedBlockCount: {
                serializedName: "x-ms-blob-committed-block-count",
                xmlName: "x-ms-blob-committed-block-count",
                type: {
                    name: "Number",
                },
            },
            isServerEncrypted: {
                serializedName: "x-ms-server-encrypted",
                xmlName: "x-ms-server-encrypted",
                type: {
                    name: "Boolean",
                },
            },
            encryptionKeySha256: {
                serializedName: "x-ms-encryption-key-sha256",
                xmlName: "x-ms-encryption-key-sha256",
                type: {
                    name: "String",
                },
            },
            encryptionScope: {
                serializedName: "x-ms-encryption-scope",
                xmlName: "x-ms-encryption-scope",
                type: {
                    name: "String",
                },
            },
            blobContentMD5: {
                serializedName: "x-ms-blob-content-md5",
                xmlName: "x-ms-blob-content-md5",
                type: {
                    name: "ByteArray",
                },
            },
            tagCount: {
                serializedName: "x-ms-tag-count",
                xmlName: "x-ms-tag-count",
                type: {
                    name: "Number",
                },
            },
            isSealed: {
                serializedName: "x-ms-blob-sealed",
                xmlName: "x-ms-blob-sealed",
                type: {
                    name: "Boolean",
                },
            },
            lastAccessed: {
                serializedName: "x-ms-last-access-time",
                xmlName: "x-ms-last-access-time",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            immutabilityPolicyExpiresOn: {
                serializedName: "x-ms-immutability-policy-until-date",
                xmlName: "x-ms-immutability-policy-until-date",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            immutabilityPolicyMode: {
                serializedName: "x-ms-immutability-policy-mode",
                xmlName: "x-ms-immutability-policy-mode",
                type: {
                    name: "Enum",
                    allowedValues: ["Mutable", "Unlocked", "Locked"],
                },
            },
            legalHold: {
                serializedName: "x-ms-legal-hold",
                xmlName: "x-ms-legal-hold",
                type: {
                    name: "Boolean",
                },
            },
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
            contentCrc64: {
                serializedName: "x-ms-content-crc64",
                xmlName: "x-ms-content-crc64",
                type: {
                    name: "ByteArray",
                },
            },
        },
    },
};
export const BlobDownloadExceptionHeaders = {
    serializedName: "Blob_downloadExceptionHeaders",
    type: {
        name: "Composite",
        className: "BlobDownloadExceptionHeaders",
        modelProperties: {
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const BlobGetPropertiesHeaders = {
    serializedName: "Blob_getPropertiesHeaders",
    type: {
        name: "Composite",
        className: "BlobGetPropertiesHeaders",
        modelProperties: {
            lastModified: {
                serializedName: "last-modified",
                xmlName: "last-modified",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            createdOn: {
                serializedName: "x-ms-creation-time",
                xmlName: "x-ms-creation-time",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            metadata: {
                serializedName: "x-ms-meta",
                headerCollectionPrefix: "x-ms-meta-",
                xmlName: "x-ms-meta",
                type: {
                    name: "Dictionary",
                    value: { type: { name: "String" } },
                },
            },
            objectReplicationPolicyId: {
                serializedName: "x-ms-or-policy-id",
                xmlName: "x-ms-or-policy-id",
                type: {
                    name: "String",
                },
            },
            objectReplicationRules: {
                serializedName: "x-ms-or",
                headerCollectionPrefix: "x-ms-or-",
                xmlName: "x-ms-or",
                type: {
                    name: "Dictionary",
                    value: { type: { name: "String" } },
                },
            },
            blobType: {
                serializedName: "x-ms-blob-type",
                xmlName: "x-ms-blob-type",
                type: {
                    name: "Enum",
                    allowedValues: ["BlockBlob", "PageBlob", "AppendBlob"],
                },
            },
            copyCompletedOn: {
                serializedName: "x-ms-copy-completion-time",
                xmlName: "x-ms-copy-completion-time",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            copyStatusDescription: {
                serializedName: "x-ms-copy-status-description",
                xmlName: "x-ms-copy-status-description",
                type: {
                    name: "String",
                },
            },
            copyId: {
                serializedName: "x-ms-copy-id",
                xmlName: "x-ms-copy-id",
                type: {
                    name: "String",
                },
            },
            copyProgress: {
                serializedName: "x-ms-copy-progress",
                xmlName: "x-ms-copy-progress",
                type: {
                    name: "String",
                },
            },
            copySource: {
                serializedName: "x-ms-copy-source",
                xmlName: "x-ms-copy-source",
                type: {
                    name: "String",
                },
            },
            copyStatus: {
                serializedName: "x-ms-copy-status",
                xmlName: "x-ms-copy-status",
                type: {
                    name: "Enum",
                    allowedValues: ["pending", "success", "aborted", "failed"],
                },
            },
            isIncrementalCopy: {
                serializedName: "x-ms-incremental-copy",
                xmlName: "x-ms-incremental-copy",
                type: {
                    name: "Boolean",
                },
            },
            destinationSnapshot: {
                serializedName: "x-ms-copy-destination-snapshot",
                xmlName: "x-ms-copy-destination-snapshot",
                type: {
                    name: "String",
                },
            },
            leaseDuration: {
                serializedName: "x-ms-lease-duration",
                xmlName: "x-ms-lease-duration",
                type: {
                    name: "Enum",
                    allowedValues: ["infinite", "fixed"],
                },
            },
            leaseState: {
                serializedName: "x-ms-lease-state",
                xmlName: "x-ms-lease-state",
                type: {
                    name: "Enum",
                    allowedValues: [
                        "available",
                        "leased",
                        "expired",
                        "breaking",
                        "broken",
                    ],
                },
            },
            leaseStatus: {
                serializedName: "x-ms-lease-status",
                xmlName: "x-ms-lease-status",
                type: {
                    name: "Enum",
                    allowedValues: ["locked", "unlocked"],
                },
            },
            contentLength: {
                serializedName: "content-length",
                xmlName: "content-length",
                type: {
                    name: "Number",
                },
            },
            contentType: {
                serializedName: "content-type",
                xmlName: "content-type",
                type: {
                    name: "String",
                },
            },
            etag: {
                serializedName: "etag",
                xmlName: "etag",
                type: {
                    name: "String",
                },
            },
            contentMD5: {
                serializedName: "content-md5",
                xmlName: "content-md5",
                type: {
                    name: "ByteArray",
                },
            },
            contentEncoding: {
                serializedName: "content-encoding",
                xmlName: "content-encoding",
                type: {
                    name: "String",
                },
            },
            contentDisposition: {
                serializedName: "content-disposition",
                xmlName: "content-disposition",
                type: {
                    name: "String",
                },
            },
            contentLanguage: {
                serializedName: "content-language",
                xmlName: "content-language",
                type: {
                    name: "String",
                },
            },
            cacheControl: {
                serializedName: "cache-control",
                xmlName: "cache-control",
                type: {
                    name: "String",
                },
            },
            blobSequenceNumber: {
                serializedName: "x-ms-blob-sequence-number",
                xmlName: "x-ms-blob-sequence-number",
                type: {
                    name: "Number",
                },
            },
            clientRequestId: {
                serializedName: "x-ms-client-request-id",
                xmlName: "x-ms-client-request-id",
                type: {
                    name: "String",
                },
            },
            requestId: {
                serializedName: "x-ms-request-id",
                xmlName: "x-ms-request-id",
                type: {
                    name: "String",
                },
            },
            version: {
                serializedName: "x-ms-version",
                xmlName: "x-ms-version",
                type: {
                    name: "String",
                },
            },
            date: {
                serializedName: "date",
                xmlName: "date",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            acceptRanges: {
                serializedName: "accept-ranges",
                xmlName: "accept-ranges",
                type: {
                    name: "String",
                },
            },
            blobCommittedBlockCount: {
                serializedName: "x-ms-blob-committed-block-count",
                xmlName: "x-ms-blob-committed-block-count",
                type: {
                    name: "Number",
                },
            },
            isServerEncrypted: {
                serializedName: "x-ms-server-encrypted",
                xmlName: "x-ms-server-encrypted",
                type: {
                    name: "Boolean",
                },
            },
            encryptionKeySha256: {
                serializedName: "x-ms-encryption-key-sha256",
                xmlName: "x-ms-encryption-key-sha256",
                type: {
                    name: "String",
                },
            },
            encryptionScope: {
                serializedName: "x-ms-encryption-scope",
                xmlName: "x-ms-encryption-scope",
                type: {
                    name: "String",
                },
            },
            accessTier: {
                serializedName: "x-ms-access-tier",
                xmlName: "x-ms-access-tier",
                type: {
                    name: "String",
                },
            },
            accessTierInferred: {
                serializedName: "x-ms-access-tier-inferred",
                xmlName: "x-ms-access-tier-inferred",
                type: {
                    name: "Boolean",
                },
            },
            archiveStatus: {
                serializedName: "x-ms-archive-status",
                xmlName: "x-ms-archive-status",
                type: {
                    name: "String",
                },
            },
            accessTierChangedOn: {
                serializedName: "x-ms-access-tier-change-time",
                xmlName: "x-ms-access-tier-change-time",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            versionId: {
                serializedName: "x-ms-version-id",
                xmlName: "x-ms-version-id",
                type: {
                    name: "String",
                },
            },
            isCurrentVersion: {
                serializedName: "x-ms-is-current-version",
                xmlName: "x-ms-is-current-version",
                type: {
                    name: "Boolean",
                },
            },
            tagCount: {
                serializedName: "x-ms-tag-count",
                xmlName: "x-ms-tag-count",
                type: {
                    name: "Number",
                },
            },
            expiresOn: {
                serializedName: "x-ms-expiry-time",
                xmlName: "x-ms-expiry-time",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            isSealed: {
                serializedName: "x-ms-blob-sealed",
                xmlName: "x-ms-blob-sealed",
                type: {
                    name: "Boolean",
                },
            },
            rehydratePriority: {
                serializedName: "x-ms-rehydrate-priority",
                xmlName: "x-ms-rehydrate-priority",
                type: {
                    name: "Enum",
                    allowedValues: ["High", "Standard"],
                },
            },
            lastAccessed: {
                serializedName: "x-ms-last-access-time",
                xmlName: "x-ms-last-access-time",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            immutabilityPolicyExpiresOn: {
                serializedName: "x-ms-immutability-policy-until-date",
                xmlName: "x-ms-immutability-policy-until-date",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            immutabilityPolicyMode: {
                serializedName: "x-ms-immutability-policy-mode",
                xmlName: "x-ms-immutability-policy-mode",
                type: {
                    name: "Enum",
                    allowedValues: ["Mutable", "Unlocked", "Locked"],
                },
            },
            legalHold: {
                serializedName: "x-ms-legal-hold",
                xmlName: "x-ms-legal-hold",
                type: {
                    name: "Boolean",
                },
            },
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const BlobGetPropertiesExceptionHeaders = {
    serializedName: "Blob_getPropertiesExceptionHeaders",
    type: {
        name: "Composite",
        className: "BlobGetPropertiesExceptionHeaders",
        modelProperties: {
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const BlobDeleteHeaders = {
    serializedName: "Blob_deleteHeaders",
    type: {
        name: "Composite",
        className: "BlobDeleteHeaders",
        modelProperties: {
            clientRequestId: {
                serializedName: "x-ms-client-request-id",
                xmlName: "x-ms-client-request-id",
                type: {
                    name: "String",
                },
            },
            requestId: {
                serializedName: "x-ms-request-id",
                xmlName: "x-ms-request-id",
                type: {
                    name: "String",
                },
            },
            version: {
                serializedName: "x-ms-version",
                xmlName: "x-ms-version",
                type: {
                    name: "String",
                },
            },
            date: {
                serializedName: "date",
                xmlName: "date",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const BlobDeleteExceptionHeaders = {
    serializedName: "Blob_deleteExceptionHeaders",
    type: {
        name: "Composite",
        className: "BlobDeleteExceptionHeaders",
        modelProperties: {
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const BlobUndeleteHeaders = {
    serializedName: "Blob_undeleteHeaders",
    type: {
        name: "Composite",
        className: "BlobUndeleteHeaders",
        modelProperties: {
            clientRequestId: {
                serializedName: "x-ms-client-request-id",
                xmlName: "x-ms-client-request-id",
                type: {
                    name: "String",
                },
            },
            requestId: {
                serializedName: "x-ms-request-id",
                xmlName: "x-ms-request-id",
                type: {
                    name: "String",
                },
            },
            version: {
                serializedName: "x-ms-version",
                xmlName: "x-ms-version",
                type: {
                    name: "String",
                },
            },
            date: {
                serializedName: "date",
                xmlName: "date",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const BlobUndeleteExceptionHeaders = {
    serializedName: "Blob_undeleteExceptionHeaders",
    type: {
        name: "Composite",
        className: "BlobUndeleteExceptionHeaders",
        modelProperties: {
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const BlobSetExpiryHeaders = {
    serializedName: "Blob_setExpiryHeaders",
    type: {
        name: "Composite",
        className: "BlobSetExpiryHeaders",
        modelProperties: {
            etag: {
                serializedName: "etag",
                xmlName: "etag",
                type: {
                    name: "String",
                },
            },
            lastModified: {
                serializedName: "last-modified",
                xmlName: "last-modified",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            clientRequestId: {
                serializedName: "x-ms-client-request-id",
                xmlName: "x-ms-client-request-id",
                type: {
                    name: "String",
                },
            },
            requestId: {
                serializedName: "x-ms-request-id",
                xmlName: "x-ms-request-id",
                type: {
                    name: "String",
                },
            },
            version: {
                serializedName: "x-ms-version",
                xmlName: "x-ms-version",
                type: {
                    name: "String",
                },
            },
            date: {
                serializedName: "date",
                xmlName: "date",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
        },
    },
};
export const BlobSetExpiryExceptionHeaders = {
    serializedName: "Blob_setExpiryExceptionHeaders",
    type: {
        name: "Composite",
        className: "BlobSetExpiryExceptionHeaders",
        modelProperties: {
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const BlobSetHttpHeadersHeaders = {
    serializedName: "Blob_setHttpHeadersHeaders",
    type: {
        name: "Composite",
        className: "BlobSetHttpHeadersHeaders",
        modelProperties: {
            etag: {
                serializedName: "etag",
                xmlName: "etag",
                type: {
                    name: "String",
                },
            },
            lastModified: {
                serializedName: "last-modified",
                xmlName: "last-modified",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            blobSequenceNumber: {
                serializedName: "x-ms-blob-sequence-number",
                xmlName: "x-ms-blob-sequence-number",
                type: {
                    name: "Number",
                },
            },
            clientRequestId: {
                serializedName: "x-ms-client-request-id",
                xmlName: "x-ms-client-request-id",
                type: {
                    name: "String",
                },
            },
            requestId: {
                serializedName: "x-ms-request-id",
                xmlName: "x-ms-request-id",
                type: {
                    name: "String",
                },
            },
            version: {
                serializedName: "x-ms-version",
                xmlName: "x-ms-version",
                type: {
                    name: "String",
                },
            },
            date: {
                serializedName: "date",
                xmlName: "date",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const BlobSetHttpHeadersExceptionHeaders = {
    serializedName: "Blob_setHttpHeadersExceptionHeaders",
    type: {
        name: "Composite",
        className: "BlobSetHttpHeadersExceptionHeaders",
        modelProperties: {
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const BlobSetImmutabilityPolicyHeaders = {
    serializedName: "Blob_setImmutabilityPolicyHeaders",
    type: {
        name: "Composite",
        className: "BlobSetImmutabilityPolicyHeaders",
        modelProperties: {
            clientRequestId: {
                serializedName: "x-ms-client-request-id",
                xmlName: "x-ms-client-request-id",
                type: {
                    name: "String",
                },
            },
            requestId: {
                serializedName: "x-ms-request-id",
                xmlName: "x-ms-request-id",
                type: {
                    name: "String",
                },
            },
            version: {
                serializedName: "x-ms-version",
                xmlName: "x-ms-version",
                type: {
                    name: "String",
                },
            },
            date: {
                serializedName: "date",
                xmlName: "date",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            immutabilityPolicyExpiry: {
                serializedName: "x-ms-immutability-policy-until-date",
                xmlName: "x-ms-immutability-policy-until-date",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            immutabilityPolicyMode: {
                serializedName: "x-ms-immutability-policy-mode",
                xmlName: "x-ms-immutability-policy-mode",
                type: {
                    name: "Enum",
                    allowedValues: ["Mutable", "Unlocked", "Locked"],
                },
            },
        },
    },
};
export const BlobSetImmutabilityPolicyExceptionHeaders = {
    serializedName: "Blob_setImmutabilityPolicyExceptionHeaders",
    type: {
        name: "Composite",
        className: "BlobSetImmutabilityPolicyExceptionHeaders",
        modelProperties: {
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const BlobDeleteImmutabilityPolicyHeaders = {
    serializedName: "Blob_deleteImmutabilityPolicyHeaders",
    type: {
        name: "Composite",
        className: "BlobDeleteImmutabilityPolicyHeaders",
        modelProperties: {
            clientRequestId: {
                serializedName: "x-ms-client-request-id",
                xmlName: "x-ms-client-request-id",
                type: {
                    name: "String",
                },
            },
            requestId: {
                serializedName: "x-ms-request-id",
                xmlName: "x-ms-request-id",
                type: {
                    name: "String",
                },
            },
            version: {
                serializedName: "x-ms-version",
                xmlName: "x-ms-version",
                type: {
                    name: "String",
                },
            },
            date: {
                serializedName: "date",
                xmlName: "date",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
        },
    },
};
export const BlobDeleteImmutabilityPolicyExceptionHeaders = {
    serializedName: "Blob_deleteImmutabilityPolicyExceptionHeaders",
    type: {
        name: "Composite",
        className: "BlobDeleteImmutabilityPolicyExceptionHeaders",
        modelProperties: {
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const BlobSetLegalHoldHeaders = {
    serializedName: "Blob_setLegalHoldHeaders",
    type: {
        name: "Composite",
        className: "BlobSetLegalHoldHeaders",
        modelProperties: {
            clientRequestId: {
                serializedName: "x-ms-client-request-id",
                xmlName: "x-ms-client-request-id",
                type: {
                    name: "String",
                },
            },
            requestId: {
                serializedName: "x-ms-request-id",
                xmlName: "x-ms-request-id",
                type: {
                    name: "String",
                },
            },
            version: {
                serializedName: "x-ms-version",
                xmlName: "x-ms-version",
                type: {
                    name: "String",
                },
            },
            date: {
                serializedName: "date",
                xmlName: "date",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            legalHold: {
                serializedName: "x-ms-legal-hold",
                xmlName: "x-ms-legal-hold",
                type: {
                    name: "Boolean",
                },
            },
        },
    },
};
export const BlobSetLegalHoldExceptionHeaders = {
    serializedName: "Blob_setLegalHoldExceptionHeaders",
    type: {
        name: "Composite",
        className: "BlobSetLegalHoldExceptionHeaders",
        modelProperties: {
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const BlobSetMetadataHeaders = {
    serializedName: "Blob_setMetadataHeaders",
    type: {
        name: "Composite",
        className: "BlobSetMetadataHeaders",
        modelProperties: {
            etag: {
                serializedName: "etag",
                xmlName: "etag",
                type: {
                    name: "String",
                },
            },
            lastModified: {
                serializedName: "last-modified",
                xmlName: "last-modified",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            clientRequestId: {
                serializedName: "x-ms-client-request-id",
                xmlName: "x-ms-client-request-id",
                type: {
                    name: "String",
                },
            },
            requestId: {
                serializedName: "x-ms-request-id",
                xmlName: "x-ms-request-id",
                type: {
                    name: "String",
                },
            },
            version: {
                serializedName: "x-ms-version",
                xmlName: "x-ms-version",
                type: {
                    name: "String",
                },
            },
            versionId: {
                serializedName: "x-ms-version-id",
                xmlName: "x-ms-version-id",
                type: {
                    name: "String",
                },
            },
            date: {
                serializedName: "date",
                xmlName: "date",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            isServerEncrypted: {
                serializedName: "x-ms-request-server-encrypted",
                xmlName: "x-ms-request-server-encrypted",
                type: {
                    name: "Boolean",
                },
            },
            encryptionKeySha256: {
                serializedName: "x-ms-encryption-key-sha256",
                xmlName: "x-ms-encryption-key-sha256",
                type: {
                    name: "String",
                },
            },
            encryptionScope: {
                serializedName: "x-ms-encryption-scope",
                xmlName: "x-ms-encryption-scope",
                type: {
                    name: "String",
                },
            },
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const BlobSetMetadataExceptionHeaders = {
    serializedName: "Blob_setMetadataExceptionHeaders",
    type: {
        name: "Composite",
        className: "BlobSetMetadataExceptionHeaders",
        modelProperties: {
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const BlobAcquireLeaseHeaders = {
    serializedName: "Blob_acquireLeaseHeaders",
    type: {
        name: "Composite",
        className: "BlobAcquireLeaseHeaders",
        modelProperties: {
            etag: {
                serializedName: "etag",
                xmlName: "etag",
                type: {
                    name: "String",
                },
            },
            lastModified: {
                serializedName: "last-modified",
                xmlName: "last-modified",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            leaseId: {
                serializedName: "x-ms-lease-id",
                xmlName: "x-ms-lease-id",
                type: {
                    name: "String",
                },
            },
            clientRequestId: {
                serializedName: "x-ms-client-request-id",
                xmlName: "x-ms-client-request-id",
                type: {
                    name: "String",
                },
            },
            requestId: {
                serializedName: "x-ms-request-id",
                xmlName: "x-ms-request-id",
                type: {
                    name: "String",
                },
            },
            version: {
                serializedName: "x-ms-version",
                xmlName: "x-ms-version",
                type: {
                    name: "String",
                },
            },
            date: {
                serializedName: "date",
                xmlName: "date",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
        },
    },
};
export const BlobAcquireLeaseExceptionHeaders = {
    serializedName: "Blob_acquireLeaseExceptionHeaders",
    type: {
        name: "Composite",
        className: "BlobAcquireLeaseExceptionHeaders",
        modelProperties: {
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const BlobReleaseLeaseHeaders = {
    serializedName: "Blob_releaseLeaseHeaders",
    type: {
        name: "Composite",
        className: "BlobReleaseLeaseHeaders",
        modelProperties: {
            etag: {
                serializedName: "etag",
                xmlName: "etag",
                type: {
                    name: "String",
                },
            },
            lastModified: {
                serializedName: "last-modified",
                xmlName: "last-modified",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            clientRequestId: {
                serializedName: "x-ms-client-request-id",
                xmlName: "x-ms-client-request-id",
                type: {
                    name: "String",
                },
            },
            requestId: {
                serializedName: "x-ms-request-id",
                xmlName: "x-ms-request-id",
                type: {
                    name: "String",
                },
            },
            version: {
                serializedName: "x-ms-version",
                xmlName: "x-ms-version",
                type: {
                    name: "String",
                },
            },
            date: {
                serializedName: "date",
                xmlName: "date",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
        },
    },
};
export const BlobReleaseLeaseExceptionHeaders = {
    serializedName: "Blob_releaseLeaseExceptionHeaders",
    type: {
        name: "Composite",
        className: "BlobReleaseLeaseExceptionHeaders",
        modelProperties: {
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const BlobRenewLeaseHeaders = {
    serializedName: "Blob_renewLeaseHeaders",
    type: {
        name: "Composite",
        className: "BlobRenewLeaseHeaders",
        modelProperties: {
            etag: {
                serializedName: "etag",
                xmlName: "etag",
                type: {
                    name: "String",
                },
            },
            lastModified: {
                serializedName: "last-modified",
                xmlName: "last-modified",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            leaseId: {
                serializedName: "x-ms-lease-id",
                xmlName: "x-ms-lease-id",
                type: {
                    name: "String",
                },
            },
            clientRequestId: {
                serializedName: "x-ms-client-request-id",
                xmlName: "x-ms-client-request-id",
                type: {
                    name: "String",
                },
            },
            requestId: {
                serializedName: "x-ms-request-id",
                xmlName: "x-ms-request-id",
                type: {
                    name: "String",
                },
            },
            version: {
                serializedName: "x-ms-version",
                xmlName: "x-ms-version",
                type: {
                    name: "String",
                },
            },
            date: {
                serializedName: "date",
                xmlName: "date",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
        },
    },
};
export const BlobRenewLeaseExceptionHeaders = {
    serializedName: "Blob_renewLeaseExceptionHeaders",
    type: {
        name: "Composite",
        className: "BlobRenewLeaseExceptionHeaders",
        modelProperties: {
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const BlobChangeLeaseHeaders = {
    serializedName: "Blob_changeLeaseHeaders",
    type: {
        name: "Composite",
        className: "BlobChangeLeaseHeaders",
        modelProperties: {
            etag: {
                serializedName: "etag",
                xmlName: "etag",
                type: {
                    name: "String",
                },
            },
            lastModified: {
                serializedName: "last-modified",
                xmlName: "last-modified",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            clientRequestId: {
                serializedName: "x-ms-client-request-id",
                xmlName: "x-ms-client-request-id",
                type: {
                    name: "String",
                },
            },
            requestId: {
                serializedName: "x-ms-request-id",
                xmlName: "x-ms-request-id",
                type: {
                    name: "String",
                },
            },
            leaseId: {
                serializedName: "x-ms-lease-id",
                xmlName: "x-ms-lease-id",
                type: {
                    name: "String",
                },
            },
            version: {
                serializedName: "x-ms-version",
                xmlName: "x-ms-version",
                type: {
                    name: "String",
                },
            },
            date: {
                serializedName: "date",
                xmlName: "date",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
        },
    },
};
export const BlobChangeLeaseExceptionHeaders = {
    serializedName: "Blob_changeLeaseExceptionHeaders",
    type: {
        name: "Composite",
        className: "BlobChangeLeaseExceptionHeaders",
        modelProperties: {
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const BlobBreakLeaseHeaders = {
    serializedName: "Blob_breakLeaseHeaders",
    type: {
        name: "Composite",
        className: "BlobBreakLeaseHeaders",
        modelProperties: {
            etag: {
                serializedName: "etag",
                xmlName: "etag",
                type: {
                    name: "String",
                },
            },
            lastModified: {
                serializedName: "last-modified",
                xmlName: "last-modified",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            leaseTime: {
                serializedName: "x-ms-lease-time",
                xmlName: "x-ms-lease-time",
                type: {
                    name: "Number",
                },
            },
            clientRequestId: {
                serializedName: "x-ms-client-request-id",
                xmlName: "x-ms-client-request-id",
                type: {
                    name: "String",
                },
            },
            requestId: {
                serializedName: "x-ms-request-id",
                xmlName: "x-ms-request-id",
                type: {
                    name: "String",
                },
            },
            version: {
                serializedName: "x-ms-version",
                xmlName: "x-ms-version",
                type: {
                    name: "String",
                },
            },
            date: {
                serializedName: "date",
                xmlName: "date",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
        },
    },
};
export const BlobBreakLeaseExceptionHeaders = {
    serializedName: "Blob_breakLeaseExceptionHeaders",
    type: {
        name: "Composite",
        className: "BlobBreakLeaseExceptionHeaders",
        modelProperties: {
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const BlobCreateSnapshotHeaders = {
    serializedName: "Blob_createSnapshotHeaders",
    type: {
        name: "Composite",
        className: "BlobCreateSnapshotHeaders",
        modelProperties: {
            snapshot: {
                serializedName: "x-ms-snapshot",
                xmlName: "x-ms-snapshot",
                type: {
                    name: "String",
                },
            },
            etag: {
                serializedName: "etag",
                xmlName: "etag",
                type: {
                    name: "String",
                },
            },
            lastModified: {
                serializedName: "last-modified",
                xmlName: "last-modified",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            clientRequestId: {
                serializedName: "x-ms-client-request-id",
                xmlName: "x-ms-client-request-id",
                type: {
                    name: "String",
                },
            },
            requestId: {
                serializedName: "x-ms-request-id",
                xmlName: "x-ms-request-id",
                type: {
                    name: "String",
                },
            },
            version: {
                serializedName: "x-ms-version",
                xmlName: "x-ms-version",
                type: {
                    name: "String",
                },
            },
            versionId: {
                serializedName: "x-ms-version-id",
                xmlName: "x-ms-version-id",
                type: {
                    name: "String",
                },
            },
            date: {
                serializedName: "date",
                xmlName: "date",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            isServerEncrypted: {
                serializedName: "x-ms-request-server-encrypted",
                xmlName: "x-ms-request-server-encrypted",
                type: {
                    name: "Boolean",
                },
            },
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const BlobCreateSnapshotExceptionHeaders = {
    serializedName: "Blob_createSnapshotExceptionHeaders",
    type: {
        name: "Composite",
        className: "BlobCreateSnapshotExceptionHeaders",
        modelProperties: {
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const BlobStartCopyFromURLHeaders = {
    serializedName: "Blob_startCopyFromURLHeaders",
    type: {
        name: "Composite",
        className: "BlobStartCopyFromURLHeaders",
        modelProperties: {
            etag: {
                serializedName: "etag",
                xmlName: "etag",
                type: {
                    name: "String",
                },
            },
            lastModified: {
                serializedName: "last-modified",
                xmlName: "last-modified",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            clientRequestId: {
                serializedName: "x-ms-client-request-id",
                xmlName: "x-ms-client-request-id",
                type: {
                    name: "String",
                },
            },
            requestId: {
                serializedName: "x-ms-request-id",
                xmlName: "x-ms-request-id",
                type: {
                    name: "String",
                },
            },
            version: {
                serializedName: "x-ms-version",
                xmlName: "x-ms-version",
                type: {
                    name: "String",
                },
            },
            versionId: {
                serializedName: "x-ms-version-id",
                xmlName: "x-ms-version-id",
                type: {
                    name: "String",
                },
            },
            date: {
                serializedName: "date",
                xmlName: "date",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            copyId: {
                serializedName: "x-ms-copy-id",
                xmlName: "x-ms-copy-id",
                type: {
                    name: "String",
                },
            },
            copyStatus: {
                serializedName: "x-ms-copy-status",
                xmlName: "x-ms-copy-status",
                type: {
                    name: "Enum",
                    allowedValues: ["pending", "success", "aborted", "failed"],
                },
            },
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const BlobStartCopyFromURLExceptionHeaders = {
    serializedName: "Blob_startCopyFromURLExceptionHeaders",
    type: {
        name: "Composite",
        className: "BlobStartCopyFromURLExceptionHeaders",
        modelProperties: {
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const BlobCopyFromURLHeaders = {
    serializedName: "Blob_copyFromURLHeaders",
    type: {
        name: "Composite",
        className: "BlobCopyFromURLHeaders",
        modelProperties: {
            etag: {
                serializedName: "etag",
                xmlName: "etag",
                type: {
                    name: "String",
                },
            },
            lastModified: {
                serializedName: "last-modified",
                xmlName: "last-modified",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            clientRequestId: {
                serializedName: "x-ms-client-request-id",
                xmlName: "x-ms-client-request-id",
                type: {
                    name: "String",
                },
            },
            requestId: {
                serializedName: "x-ms-request-id",
                xmlName: "x-ms-request-id",
                type: {
                    name: "String",
                },
            },
            version: {
                serializedName: "x-ms-version",
                xmlName: "x-ms-version",
                type: {
                    name: "String",
                },
            },
            versionId: {
                serializedName: "x-ms-version-id",
                xmlName: "x-ms-version-id",
                type: {
                    name: "String",
                },
            },
            date: {
                serializedName: "date",
                xmlName: "date",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            copyId: {
                serializedName: "x-ms-copy-id",
                xmlName: "x-ms-copy-id",
                type: {
                    name: "String",
                },
            },
            copyStatus: {
                defaultValue: "success",
                isConstant: true,
                serializedName: "x-ms-copy-status",
                type: {
                    name: "String",
                },
            },
            contentMD5: {
                serializedName: "content-md5",
                xmlName: "content-md5",
                type: {
                    name: "ByteArray",
                },
            },
            xMsContentCrc64: {
                serializedName: "x-ms-content-crc64",
                xmlName: "x-ms-content-crc64",
                type: {
                    name: "ByteArray",
                },
            },
            encryptionScope: {
                serializedName: "x-ms-encryption-scope",
                xmlName: "x-ms-encryption-scope",
                type: {
                    name: "String",
                },
            },
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const BlobCopyFromURLExceptionHeaders = {
    serializedName: "Blob_copyFromURLExceptionHeaders",
    type: {
        name: "Composite",
        className: "BlobCopyFromURLExceptionHeaders",
        modelProperties: {
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const BlobAbortCopyFromURLHeaders = {
    serializedName: "Blob_abortCopyFromURLHeaders",
    type: {
        name: "Composite",
        className: "BlobAbortCopyFromURLHeaders",
        modelProperties: {
            clientRequestId: {
                serializedName: "x-ms-client-request-id",
                xmlName: "x-ms-client-request-id",
                type: {
                    name: "String",
                },
            },
            requestId: {
                serializedName: "x-ms-request-id",
                xmlName: "x-ms-request-id",
                type: {
                    name: "String",
                },
            },
            version: {
                serializedName: "x-ms-version",
                xmlName: "x-ms-version",
                type: {
                    name: "String",
                },
            },
            date: {
                serializedName: "date",
                xmlName: "date",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const BlobAbortCopyFromURLExceptionHeaders = {
    serializedName: "Blob_abortCopyFromURLExceptionHeaders",
    type: {
        name: "Composite",
        className: "BlobAbortCopyFromURLExceptionHeaders",
        modelProperties: {
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const BlobSetTierHeaders = {
    serializedName: "Blob_setTierHeaders",
    type: {
        name: "Composite",
        className: "BlobSetTierHeaders",
        modelProperties: {
            clientRequestId: {
                serializedName: "x-ms-client-request-id",
                xmlName: "x-ms-client-request-id",
                type: {
                    name: "String",
                },
            },
            requestId: {
                serializedName: "x-ms-request-id",
                xmlName: "x-ms-request-id",
                type: {
                    name: "String",
                },
            },
            version: {
                serializedName: "x-ms-version",
                xmlName: "x-ms-version",
                type: {
                    name: "String",
                },
            },
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const BlobSetTierExceptionHeaders = {
    serializedName: "Blob_setTierExceptionHeaders",
    type: {
        name: "Composite",
        className: "BlobSetTierExceptionHeaders",
        modelProperties: {
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const BlobGetAccountInfoHeaders = {
    serializedName: "Blob_getAccountInfoHeaders",
    type: {
        name: "Composite",
        className: "BlobGetAccountInfoHeaders",
        modelProperties: {
            clientRequestId: {
                serializedName: "x-ms-client-request-id",
                xmlName: "x-ms-client-request-id",
                type: {
                    name: "String",
                },
            },
            requestId: {
                serializedName: "x-ms-request-id",
                xmlName: "x-ms-request-id",
                type: {
                    name: "String",
                },
            },
            version: {
                serializedName: "x-ms-version",
                xmlName: "x-ms-version",
                type: {
                    name: "String",
                },
            },
            date: {
                serializedName: "date",
                xmlName: "date",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            skuName: {
                serializedName: "x-ms-sku-name",
                xmlName: "x-ms-sku-name",
                type: {
                    name: "Enum",
                    allowedValues: [
                        "Standard_LRS",
                        "Standard_GRS",
                        "Standard_RAGRS",
                        "Standard_ZRS",
                        "Premium_LRS",
                    ],
                },
            },
            accountKind: {
                serializedName: "x-ms-account-kind",
                xmlName: "x-ms-account-kind",
                type: {
                    name: "Enum",
                    allowedValues: [
                        "Storage",
                        "BlobStorage",
                        "StorageV2",
                        "FileStorage",
                        "BlockBlobStorage",
                    ],
                },
            },
            isHierarchicalNamespaceEnabled: {
                serializedName: "x-ms-is-hns-enabled",
                xmlName: "x-ms-is-hns-enabled",
                type: {
                    name: "Boolean",
                },
            },
        },
    },
};
export const BlobGetAccountInfoExceptionHeaders = {
    serializedName: "Blob_getAccountInfoExceptionHeaders",
    type: {
        name: "Composite",
        className: "BlobGetAccountInfoExceptionHeaders",
        modelProperties: {
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const BlobQueryHeaders = {
    serializedName: "Blob_queryHeaders",
    type: {
        name: "Composite",
        className: "BlobQueryHeaders",
        modelProperties: {
            lastModified: {
                serializedName: "last-modified",
                xmlName: "last-modified",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            metadata: {
                serializedName: "x-ms-meta",
                headerCollectionPrefix: "x-ms-meta-",
                xmlName: "x-ms-meta",
                type: {
                    name: "Dictionary",
                    value: { type: { name: "String" } },
                },
            },
            contentLength: {
                serializedName: "content-length",
                xmlName: "content-length",
                type: {
                    name: "Number",
                },
            },
            contentType: {
                serializedName: "content-type",
                xmlName: "content-type",
                type: {
                    name: "String",
                },
            },
            contentRange: {
                serializedName: "content-range",
                xmlName: "content-range",
                type: {
                    name: "String",
                },
            },
            etag: {
                serializedName: "etag",
                xmlName: "etag",
                type: {
                    name: "String",
                },
            },
            contentMD5: {
                serializedName: "content-md5",
                xmlName: "content-md5",
                type: {
                    name: "ByteArray",
                },
            },
            contentEncoding: {
                serializedName: "content-encoding",
                xmlName: "content-encoding",
                type: {
                    name: "String",
                },
            },
            cacheControl: {
                serializedName: "cache-control",
                xmlName: "cache-control",
                type: {
                    name: "String",
                },
            },
            contentDisposition: {
                serializedName: "content-disposition",
                xmlName: "content-disposition",
                type: {
                    name: "String",
                },
            },
            contentLanguage: {
                serializedName: "content-language",
                xmlName: "content-language",
                type: {
                    name: "String",
                },
            },
            blobSequenceNumber: {
                serializedName: "x-ms-blob-sequence-number",
                xmlName: "x-ms-blob-sequence-number",
                type: {
                    name: "Number",
                },
            },
            blobType: {
                serializedName: "x-ms-blob-type",
                xmlName: "x-ms-blob-type",
                type: {
                    name: "Enum",
                    allowedValues: ["BlockBlob", "PageBlob", "AppendBlob"],
                },
            },
            copyCompletionTime: {
                serializedName: "x-ms-copy-completion-time",
                xmlName: "x-ms-copy-completion-time",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            copyStatusDescription: {
                serializedName: "x-ms-copy-status-description",
                xmlName: "x-ms-copy-status-description",
                type: {
                    name: "String",
                },
            },
            copyId: {
                serializedName: "x-ms-copy-id",
                xmlName: "x-ms-copy-id",
                type: {
                    name: "String",
                },
            },
            copyProgress: {
                serializedName: "x-ms-copy-progress",
                xmlName: "x-ms-copy-progress",
                type: {
                    name: "String",
                },
            },
            copySource: {
                serializedName: "x-ms-copy-source",
                xmlName: "x-ms-copy-source",
                type: {
                    name: "String",
                },
            },
            copyStatus: {
                serializedName: "x-ms-copy-status",
                xmlName: "x-ms-copy-status",
                type: {
                    name: "Enum",
                    allowedValues: ["pending", "success", "aborted", "failed"],
                },
            },
            leaseDuration: {
                serializedName: "x-ms-lease-duration",
                xmlName: "x-ms-lease-duration",
                type: {
                    name: "Enum",
                    allowedValues: ["infinite", "fixed"],
                },
            },
            leaseState: {
                serializedName: "x-ms-lease-state",
                xmlName: "x-ms-lease-state",
                type: {
                    name: "Enum",
                    allowedValues: [
                        "available",
                        "leased",
                        "expired",
                        "breaking",
                        "broken",
                    ],
                },
            },
            leaseStatus: {
                serializedName: "x-ms-lease-status",
                xmlName: "x-ms-lease-status",
                type: {
                    name: "Enum",
                    allowedValues: ["locked", "unlocked"],
                },
            },
            clientRequestId: {
                serializedName: "x-ms-client-request-id",
                xmlName: "x-ms-client-request-id",
                type: {
                    name: "String",
                },
            },
            requestId: {
                serializedName: "x-ms-request-id",
                xmlName: "x-ms-request-id",
                type: {
                    name: "String",
                },
            },
            version: {
                serializedName: "x-ms-version",
                xmlName: "x-ms-version",
                type: {
                    name: "String",
                },
            },
            acceptRanges: {
                serializedName: "accept-ranges",
                xmlName: "accept-ranges",
                type: {
                    name: "String",
                },
            },
            date: {
                serializedName: "date",
                xmlName: "date",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            blobCommittedBlockCount: {
                serializedName: "x-ms-blob-committed-block-count",
                xmlName: "x-ms-blob-committed-block-count",
                type: {
                    name: "Number",
                },
            },
            isServerEncrypted: {
                serializedName: "x-ms-server-encrypted",
                xmlName: "x-ms-server-encrypted",
                type: {
                    name: "Boolean",
                },
            },
            encryptionKeySha256: {
                serializedName: "x-ms-encryption-key-sha256",
                xmlName: "x-ms-encryption-key-sha256",
                type: {
                    name: "String",
                },
            },
            encryptionScope: {
                serializedName: "x-ms-encryption-scope",
                xmlName: "x-ms-encryption-scope",
                type: {
                    name: "String",
                },
            },
            blobContentMD5: {
                serializedName: "x-ms-blob-content-md5",
                xmlName: "x-ms-blob-content-md5",
                type: {
                    name: "ByteArray",
                },
            },
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
            contentCrc64: {
                serializedName: "x-ms-content-crc64",
                xmlName: "x-ms-content-crc64",
                type: {
                    name: "ByteArray",
                },
            },
        },
    },
};
export const BlobQueryExceptionHeaders = {
    serializedName: "Blob_queryExceptionHeaders",
    type: {
        name: "Composite",
        className: "BlobQueryExceptionHeaders",
        modelProperties: {
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const BlobGetTagsHeaders = {
    serializedName: "Blob_getTagsHeaders",
    type: {
        name: "Composite",
        className: "BlobGetTagsHeaders",
        modelProperties: {
            clientRequestId: {
                serializedName: "x-ms-client-request-id",
                xmlName: "x-ms-client-request-id",
                type: {
                    name: "String",
                },
            },
            requestId: {
                serializedName: "x-ms-request-id",
                xmlName: "x-ms-request-id",
                type: {
                    name: "String",
                },
            },
            version: {
                serializedName: "x-ms-version",
                xmlName: "x-ms-version",
                type: {
                    name: "String",
                },
            },
            date: {
                serializedName: "date",
                xmlName: "date",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const BlobGetTagsExceptionHeaders = {
    serializedName: "Blob_getTagsExceptionHeaders",
    type: {
        name: "Composite",
        className: "BlobGetTagsExceptionHeaders",
        modelProperties: {
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const BlobSetTagsHeaders = {
    serializedName: "Blob_setTagsHeaders",
    type: {
        name: "Composite",
        className: "BlobSetTagsHeaders",
        modelProperties: {
            clientRequestId: {
                serializedName: "x-ms-client-request-id",
                xmlName: "x-ms-client-request-id",
                type: {
                    name: "String",
                },
            },
            requestId: {
                serializedName: "x-ms-request-id",
                xmlName: "x-ms-request-id",
                type: {
                    name: "String",
                },
            },
            version: {
                serializedName: "x-ms-version",
                xmlName: "x-ms-version",
                type: {
                    name: "String",
                },
            },
            date: {
                serializedName: "date",
                xmlName: "date",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const BlobSetTagsExceptionHeaders = {
    serializedName: "Blob_setTagsExceptionHeaders",
    type: {
        name: "Composite",
        className: "BlobSetTagsExceptionHeaders",
        modelProperties: {
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const PageBlobCreateHeaders = {
    serializedName: "PageBlob_createHeaders",
    type: {
        name: "Composite",
        className: "PageBlobCreateHeaders",
        modelProperties: {
            etag: {
                serializedName: "etag",
                xmlName: "etag",
                type: {
                    name: "String",
                },
            },
            lastModified: {
                serializedName: "last-modified",
                xmlName: "last-modified",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            contentMD5: {
                serializedName: "content-md5",
                xmlName: "content-md5",
                type: {
                    name: "ByteArray",
                },
            },
            clientRequestId: {
                serializedName: "x-ms-client-request-id",
                xmlName: "x-ms-client-request-id",
                type: {
                    name: "String",
                },
            },
            requestId: {
                serializedName: "x-ms-request-id",
                xmlName: "x-ms-request-id",
                type: {
                    name: "String",
                },
            },
            version: {
                serializedName: "x-ms-version",
                xmlName: "x-ms-version",
                type: {
                    name: "String",
                },
            },
            versionId: {
                serializedName: "x-ms-version-id",
                xmlName: "x-ms-version-id",
                type: {
                    name: "String",
                },
            },
            date: {
                serializedName: "date",
                xmlName: "date",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            isServerEncrypted: {
                serializedName: "x-ms-request-server-encrypted",
                xmlName: "x-ms-request-server-encrypted",
                type: {
                    name: "Boolean",
                },
            },
            encryptionKeySha256: {
                serializedName: "x-ms-encryption-key-sha256",
                xmlName: "x-ms-encryption-key-sha256",
                type: {
                    name: "String",
                },
            },
            encryptionScope: {
                serializedName: "x-ms-encryption-scope",
                xmlName: "x-ms-encryption-scope",
                type: {
                    name: "String",
                },
            },
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const PageBlobCreateExceptionHeaders = {
    serializedName: "PageBlob_createExceptionHeaders",
    type: {
        name: "Composite",
        className: "PageBlobCreateExceptionHeaders",
        modelProperties: {
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const PageBlobUploadPagesHeaders = {
    serializedName: "PageBlob_uploadPagesHeaders",
    type: {
        name: "Composite",
        className: "PageBlobUploadPagesHeaders",
        modelProperties: {
            etag: {
                serializedName: "etag",
                xmlName: "etag",
                type: {
                    name: "String",
                },
            },
            lastModified: {
                serializedName: "last-modified",
                xmlName: "last-modified",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            contentMD5: {
                serializedName: "content-md5",
                xmlName: "content-md5",
                type: {
                    name: "ByteArray",
                },
            },
            xMsContentCrc64: {
                serializedName: "x-ms-content-crc64",
                xmlName: "x-ms-content-crc64",
                type: {
                    name: "ByteArray",
                },
            },
            blobSequenceNumber: {
                serializedName: "x-ms-blob-sequence-number",
                xmlName: "x-ms-blob-sequence-number",
                type: {
                    name: "Number",
                },
            },
            clientRequestId: {
                serializedName: "x-ms-client-request-id",
                xmlName: "x-ms-client-request-id",
                type: {
                    name: "String",
                },
            },
            requestId: {
                serializedName: "x-ms-request-id",
                xmlName: "x-ms-request-id",
                type: {
                    name: "String",
                },
            },
            version: {
                serializedName: "x-ms-version",
                xmlName: "x-ms-version",
                type: {
                    name: "String",
                },
            },
            date: {
                serializedName: "date",
                xmlName: "date",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            isServerEncrypted: {
                serializedName: "x-ms-request-server-encrypted",
                xmlName: "x-ms-request-server-encrypted",
                type: {
                    name: "Boolean",
                },
            },
            encryptionKeySha256: {
                serializedName: "x-ms-encryption-key-sha256",
                xmlName: "x-ms-encryption-key-sha256",
                type: {
                    name: "String",
                },
            },
            encryptionScope: {
                serializedName: "x-ms-encryption-scope",
                xmlName: "x-ms-encryption-scope",
                type: {
                    name: "String",
                },
            },
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const PageBlobUploadPagesExceptionHeaders = {
    serializedName: "PageBlob_uploadPagesExceptionHeaders",
    type: {
        name: "Composite",
        className: "PageBlobUploadPagesExceptionHeaders",
        modelProperties: {
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const PageBlobClearPagesHeaders = {
    serializedName: "PageBlob_clearPagesHeaders",
    type: {
        name: "Composite",
        className: "PageBlobClearPagesHeaders",
        modelProperties: {
            etag: {
                serializedName: "etag",
                xmlName: "etag",
                type: {
                    name: "String",
                },
            },
            lastModified: {
                serializedName: "last-modified",
                xmlName: "last-modified",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            contentMD5: {
                serializedName: "content-md5",
                xmlName: "content-md5",
                type: {
                    name: "ByteArray",
                },
            },
            xMsContentCrc64: {
                serializedName: "x-ms-content-crc64",
                xmlName: "x-ms-content-crc64",
                type: {
                    name: "ByteArray",
                },
            },
            blobSequenceNumber: {
                serializedName: "x-ms-blob-sequence-number",
                xmlName: "x-ms-blob-sequence-number",
                type: {
                    name: "Number",
                },
            },
            clientRequestId: {
                serializedName: "x-ms-client-request-id",
                xmlName: "x-ms-client-request-id",
                type: {
                    name: "String",
                },
            },
            requestId: {
                serializedName: "x-ms-request-id",
                xmlName: "x-ms-request-id",
                type: {
                    name: "String",
                },
            },
            version: {
                serializedName: "x-ms-version",
                xmlName: "x-ms-version",
                type: {
                    name: "String",
                },
            },
            date: {
                serializedName: "date",
                xmlName: "date",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const PageBlobClearPagesExceptionHeaders = {
    serializedName: "PageBlob_clearPagesExceptionHeaders",
    type: {
        name: "Composite",
        className: "PageBlobClearPagesExceptionHeaders",
        modelProperties: {
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const PageBlobUploadPagesFromURLHeaders = {
    serializedName: "PageBlob_uploadPagesFromURLHeaders",
    type: {
        name: "Composite",
        className: "PageBlobUploadPagesFromURLHeaders",
        modelProperties: {
            etag: {
                serializedName: "etag",
                xmlName: "etag",
                type: {
                    name: "String",
                },
            },
            lastModified: {
                serializedName: "last-modified",
                xmlName: "last-modified",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            contentMD5: {
                serializedName: "content-md5",
                xmlName: "content-md5",
                type: {
                    name: "ByteArray",
                },
            },
            xMsContentCrc64: {
                serializedName: "x-ms-content-crc64",
                xmlName: "x-ms-content-crc64",
                type: {
                    name: "ByteArray",
                },
            },
            blobSequenceNumber: {
                serializedName: "x-ms-blob-sequence-number",
                xmlName: "x-ms-blob-sequence-number",
                type: {
                    name: "Number",
                },
            },
            requestId: {
                serializedName: "x-ms-request-id",
                xmlName: "x-ms-request-id",
                type: {
                    name: "String",
                },
            },
            version: {
                serializedName: "x-ms-version",
                xmlName: "x-ms-version",
                type: {
                    name: "String",
                },
            },
            date: {
                serializedName: "date",
                xmlName: "date",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            isServerEncrypted: {
                serializedName: "x-ms-request-server-encrypted",
                xmlName: "x-ms-request-server-encrypted",
                type: {
                    name: "Boolean",
                },
            },
            encryptionKeySha256: {
                serializedName: "x-ms-encryption-key-sha256",
                xmlName: "x-ms-encryption-key-sha256",
                type: {
                    name: "String",
                },
            },
            encryptionScope: {
                serializedName: "x-ms-encryption-scope",
                xmlName: "x-ms-encryption-scope",
                type: {
                    name: "String",
                },
            },
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const PageBlobUploadPagesFromURLExceptionHeaders = {
    serializedName: "PageBlob_uploadPagesFromURLExceptionHeaders",
    type: {
        name: "Composite",
        className: "PageBlobUploadPagesFromURLExceptionHeaders",
        modelProperties: {
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const PageBlobGetPageRangesHeaders = {
    serializedName: "PageBlob_getPageRangesHeaders",
    type: {
        name: "Composite",
        className: "PageBlobGetPageRangesHeaders",
        modelProperties: {
            lastModified: {
                serializedName: "last-modified",
                xmlName: "last-modified",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            etag: {
                serializedName: "etag",
                xmlName: "etag",
                type: {
                    name: "String",
                },
            },
            blobContentLength: {
                serializedName: "x-ms-blob-content-length",
                xmlName: "x-ms-blob-content-length",
                type: {
                    name: "Number",
                },
            },
            clientRequestId: {
                serializedName: "x-ms-client-request-id",
                xmlName: "x-ms-client-request-id",
                type: {
                    name: "String",
                },
            },
            requestId: {
                serializedName: "x-ms-request-id",
                xmlName: "x-ms-request-id",
                type: {
                    name: "String",
                },
            },
            version: {
                serializedName: "x-ms-version",
                xmlName: "x-ms-version",
                type: {
                    name: "String",
                },
            },
            date: {
                serializedName: "date",
                xmlName: "date",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const PageBlobGetPageRangesExceptionHeaders = {
    serializedName: "PageBlob_getPageRangesExceptionHeaders",
    type: {
        name: "Composite",
        className: "PageBlobGetPageRangesExceptionHeaders",
        modelProperties: {
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const PageBlobGetPageRangesDiffHeaders = {
    serializedName: "PageBlob_getPageRangesDiffHeaders",
    type: {
        name: "Composite",
        className: "PageBlobGetPageRangesDiffHeaders",
        modelProperties: {
            lastModified: {
                serializedName: "last-modified",
                xmlName: "last-modified",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            etag: {
                serializedName: "etag",
                xmlName: "etag",
                type: {
                    name: "String",
                },
            },
            blobContentLength: {
                serializedName: "x-ms-blob-content-length",
                xmlName: "x-ms-blob-content-length",
                type: {
                    name: "Number",
                },
            },
            clientRequestId: {
                serializedName: "x-ms-client-request-id",
                xmlName: "x-ms-client-request-id",
                type: {
                    name: "String",
                },
            },
            requestId: {
                serializedName: "x-ms-request-id",
                xmlName: "x-ms-request-id",
                type: {
                    name: "String",
                },
            },
            version: {
                serializedName: "x-ms-version",
                xmlName: "x-ms-version",
                type: {
                    name: "String",
                },
            },
            date: {
                serializedName: "date",
                xmlName: "date",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const PageBlobGetPageRangesDiffExceptionHeaders = {
    serializedName: "PageBlob_getPageRangesDiffExceptionHeaders",
    type: {
        name: "Composite",
        className: "PageBlobGetPageRangesDiffExceptionHeaders",
        modelProperties: {
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const PageBlobResizeHeaders = {
    serializedName: "PageBlob_resizeHeaders",
    type: {
        name: "Composite",
        className: "PageBlobResizeHeaders",
        modelProperties: {
            etag: {
                serializedName: "etag",
                xmlName: "etag",
                type: {
                    name: "String",
                },
            },
            lastModified: {
                serializedName: "last-modified",
                xmlName: "last-modified",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            blobSequenceNumber: {
                serializedName: "x-ms-blob-sequence-number",
                xmlName: "x-ms-blob-sequence-number",
                type: {
                    name: "Number",
                },
            },
            clientRequestId: {
                serializedName: "x-ms-client-request-id",
                xmlName: "x-ms-client-request-id",
                type: {
                    name: "String",
                },
            },
            requestId: {
                serializedName: "x-ms-request-id",
                xmlName: "x-ms-request-id",
                type: {
                    name: "String",
                },
            },
            version: {
                serializedName: "x-ms-version",
                xmlName: "x-ms-version",
                type: {
                    name: "String",
                },
            },
            date: {
                serializedName: "date",
                xmlName: "date",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const PageBlobResizeExceptionHeaders = {
    serializedName: "PageBlob_resizeExceptionHeaders",
    type: {
        name: "Composite",
        className: "PageBlobResizeExceptionHeaders",
        modelProperties: {
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const PageBlobUpdateSequenceNumberHeaders = {
    serializedName: "PageBlob_updateSequenceNumberHeaders",
    type: {
        name: "Composite",
        className: "PageBlobUpdateSequenceNumberHeaders",
        modelProperties: {
            etag: {
                serializedName: "etag",
                xmlName: "etag",
                type: {
                    name: "String",
                },
            },
            lastModified: {
                serializedName: "last-modified",
                xmlName: "last-modified",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            blobSequenceNumber: {
                serializedName: "x-ms-blob-sequence-number",
                xmlName: "x-ms-blob-sequence-number",
                type: {
                    name: "Number",
                },
            },
            clientRequestId: {
                serializedName: "x-ms-client-request-id",
                xmlName: "x-ms-client-request-id",
                type: {
                    name: "String",
                },
            },
            requestId: {
                serializedName: "x-ms-request-id",
                xmlName: "x-ms-request-id",
                type: {
                    name: "String",
                },
            },
            version: {
                serializedName: "x-ms-version",
                xmlName: "x-ms-version",
                type: {
                    name: "String",
                },
            },
            date: {
                serializedName: "date",
                xmlName: "date",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const PageBlobUpdateSequenceNumberExceptionHeaders = {
    serializedName: "PageBlob_updateSequenceNumberExceptionHeaders",
    type: {
        name: "Composite",
        className: "PageBlobUpdateSequenceNumberExceptionHeaders",
        modelProperties: {
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const PageBlobCopyIncrementalHeaders = {
    serializedName: "PageBlob_copyIncrementalHeaders",
    type: {
        name: "Composite",
        className: "PageBlobCopyIncrementalHeaders",
        modelProperties: {
            etag: {
                serializedName: "etag",
                xmlName: "etag",
                type: {
                    name: "String",
                },
            },
            lastModified: {
                serializedName: "last-modified",
                xmlName: "last-modified",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            clientRequestId: {
                serializedName: "x-ms-client-request-id",
                xmlName: "x-ms-client-request-id",
                type: {
                    name: "String",
                },
            },
            requestId: {
                serializedName: "x-ms-request-id",
                xmlName: "x-ms-request-id",
                type: {
                    name: "String",
                },
            },
            version: {
                serializedName: "x-ms-version",
                xmlName: "x-ms-version",
                type: {
                    name: "String",
                },
            },
            date: {
                serializedName: "date",
                xmlName: "date",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            copyId: {
                serializedName: "x-ms-copy-id",
                xmlName: "x-ms-copy-id",
                type: {
                    name: "String",
                },
            },
            copyStatus: {
                serializedName: "x-ms-copy-status",
                xmlName: "x-ms-copy-status",
                type: {
                    name: "Enum",
                    allowedValues: ["pending", "success", "aborted", "failed"],
                },
            },
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const PageBlobCopyIncrementalExceptionHeaders = {
    serializedName: "PageBlob_copyIncrementalExceptionHeaders",
    type: {
        name: "Composite",
        className: "PageBlobCopyIncrementalExceptionHeaders",
        modelProperties: {
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const AppendBlobCreateHeaders = {
    serializedName: "AppendBlob_createHeaders",
    type: {
        name: "Composite",
        className: "AppendBlobCreateHeaders",
        modelProperties: {
            etag: {
                serializedName: "etag",
                xmlName: "etag",
                type: {
                    name: "String",
                },
            },
            lastModified: {
                serializedName: "last-modified",
                xmlName: "last-modified",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            contentMD5: {
                serializedName: "content-md5",
                xmlName: "content-md5",
                type: {
                    name: "ByteArray",
                },
            },
            clientRequestId: {
                serializedName: "x-ms-client-request-id",
                xmlName: "x-ms-client-request-id",
                type: {
                    name: "String",
                },
            },
            requestId: {
                serializedName: "x-ms-request-id",
                xmlName: "x-ms-request-id",
                type: {
                    name: "String",
                },
            },
            version: {
                serializedName: "x-ms-version",
                xmlName: "x-ms-version",
                type: {
                    name: "String",
                },
            },
            versionId: {
                serializedName: "x-ms-version-id",
                xmlName: "x-ms-version-id",
                type: {
                    name: "String",
                },
            },
            date: {
                serializedName: "date",
                xmlName: "date",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            isServerEncrypted: {
                serializedName: "x-ms-request-server-encrypted",
                xmlName: "x-ms-request-server-encrypted",
                type: {
                    name: "Boolean",
                },
            },
            encryptionKeySha256: {
                serializedName: "x-ms-encryption-key-sha256",
                xmlName: "x-ms-encryption-key-sha256",
                type: {
                    name: "String",
                },
            },
            encryptionScope: {
                serializedName: "x-ms-encryption-scope",
                xmlName: "x-ms-encryption-scope",
                type: {
                    name: "String",
                },
            },
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const AppendBlobCreateExceptionHeaders = {
    serializedName: "AppendBlob_createExceptionHeaders",
    type: {
        name: "Composite",
        className: "AppendBlobCreateExceptionHeaders",
        modelProperties: {
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const AppendBlobAppendBlockHeaders = {
    serializedName: "AppendBlob_appendBlockHeaders",
    type: {
        name: "Composite",
        className: "AppendBlobAppendBlockHeaders",
        modelProperties: {
            etag: {
                serializedName: "etag",
                xmlName: "etag",
                type: {
                    name: "String",
                },
            },
            lastModified: {
                serializedName: "last-modified",
                xmlName: "last-modified",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            contentMD5: {
                serializedName: "content-md5",
                xmlName: "content-md5",
                type: {
                    name: "ByteArray",
                },
            },
            xMsContentCrc64: {
                serializedName: "x-ms-content-crc64",
                xmlName: "x-ms-content-crc64",
                type: {
                    name: "ByteArray",
                },
            },
            clientRequestId: {
                serializedName: "x-ms-client-request-id",
                xmlName: "x-ms-client-request-id",
                type: {
                    name: "String",
                },
            },
            requestId: {
                serializedName: "x-ms-request-id",
                xmlName: "x-ms-request-id",
                type: {
                    name: "String",
                },
            },
            version: {
                serializedName: "x-ms-version",
                xmlName: "x-ms-version",
                type: {
                    name: "String",
                },
            },
            date: {
                serializedName: "date",
                xmlName: "date",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            blobAppendOffset: {
                serializedName: "x-ms-blob-append-offset",
                xmlName: "x-ms-blob-append-offset",
                type: {
                    name: "String",
                },
            },
            blobCommittedBlockCount: {
                serializedName: "x-ms-blob-committed-block-count",
                xmlName: "x-ms-blob-committed-block-count",
                type: {
                    name: "Number",
                },
            },
            isServerEncrypted: {
                serializedName: "x-ms-request-server-encrypted",
                xmlName: "x-ms-request-server-encrypted",
                type: {
                    name: "Boolean",
                },
            },
            encryptionKeySha256: {
                serializedName: "x-ms-encryption-key-sha256",
                xmlName: "x-ms-encryption-key-sha256",
                type: {
                    name: "String",
                },
            },
            encryptionScope: {
                serializedName: "x-ms-encryption-scope",
                xmlName: "x-ms-encryption-scope",
                type: {
                    name: "String",
                },
            },
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const AppendBlobAppendBlockExceptionHeaders = {
    serializedName: "AppendBlob_appendBlockExceptionHeaders",
    type: {
        name: "Composite",
        className: "AppendBlobAppendBlockExceptionHeaders",
        modelProperties: {
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const AppendBlobAppendBlockFromUrlHeaders = {
    serializedName: "AppendBlob_appendBlockFromUrlHeaders",
    type: {
        name: "Composite",
        className: "AppendBlobAppendBlockFromUrlHeaders",
        modelProperties: {
            etag: {
                serializedName: "etag",
                xmlName: "etag",
                type: {
                    name: "String",
                },
            },
            lastModified: {
                serializedName: "last-modified",
                xmlName: "last-modified",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            contentMD5: {
                serializedName: "content-md5",
                xmlName: "content-md5",
                type: {
                    name: "ByteArray",
                },
            },
            xMsContentCrc64: {
                serializedName: "x-ms-content-crc64",
                xmlName: "x-ms-content-crc64",
                type: {
                    name: "ByteArray",
                },
            },
            requestId: {
                serializedName: "x-ms-request-id",
                xmlName: "x-ms-request-id",
                type: {
                    name: "String",
                },
            },
            version: {
                serializedName: "x-ms-version",
                xmlName: "x-ms-version",
                type: {
                    name: "String",
                },
            },
            date: {
                serializedName: "date",
                xmlName: "date",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            blobAppendOffset: {
                serializedName: "x-ms-blob-append-offset",
                xmlName: "x-ms-blob-append-offset",
                type: {
                    name: "String",
                },
            },
            blobCommittedBlockCount: {
                serializedName: "x-ms-blob-committed-block-count",
                xmlName: "x-ms-blob-committed-block-count",
                type: {
                    name: "Number",
                },
            },
            encryptionKeySha256: {
                serializedName: "x-ms-encryption-key-sha256",
                xmlName: "x-ms-encryption-key-sha256",
                type: {
                    name: "String",
                },
            },
            encryptionScope: {
                serializedName: "x-ms-encryption-scope",
                xmlName: "x-ms-encryption-scope",
                type: {
                    name: "String",
                },
            },
            isServerEncrypted: {
                serializedName: "x-ms-request-server-encrypted",
                xmlName: "x-ms-request-server-encrypted",
                type: {
                    name: "Boolean",
                },
            },
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const AppendBlobAppendBlockFromUrlExceptionHeaders = {
    serializedName: "AppendBlob_appendBlockFromUrlExceptionHeaders",
    type: {
        name: "Composite",
        className: "AppendBlobAppendBlockFromUrlExceptionHeaders",
        modelProperties: {
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const AppendBlobSealHeaders = {
    serializedName: "AppendBlob_sealHeaders",
    type: {
        name: "Composite",
        className: "AppendBlobSealHeaders",
        modelProperties: {
            etag: {
                serializedName: "etag",
                xmlName: "etag",
                type: {
                    name: "String",
                },
            },
            lastModified: {
                serializedName: "last-modified",
                xmlName: "last-modified",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            clientRequestId: {
                serializedName: "x-ms-client-request-id",
                xmlName: "x-ms-client-request-id",
                type: {
                    name: "String",
                },
            },
            requestId: {
                serializedName: "x-ms-request-id",
                xmlName: "x-ms-request-id",
                type: {
                    name: "String",
                },
            },
            version: {
                serializedName: "x-ms-version",
                xmlName: "x-ms-version",
                type: {
                    name: "String",
                },
            },
            date: {
                serializedName: "date",
                xmlName: "date",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            isSealed: {
                serializedName: "x-ms-blob-sealed",
                xmlName: "x-ms-blob-sealed",
                type: {
                    name: "Boolean",
                },
            },
        },
    },
};
export const AppendBlobSealExceptionHeaders = {
    serializedName: "AppendBlob_sealExceptionHeaders",
    type: {
        name: "Composite",
        className: "AppendBlobSealExceptionHeaders",
        modelProperties: {
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const BlockBlobUploadHeaders = {
    serializedName: "BlockBlob_uploadHeaders",
    type: {
        name: "Composite",
        className: "BlockBlobUploadHeaders",
        modelProperties: {
            etag: {
                serializedName: "etag",
                xmlName: "etag",
                type: {
                    name: "String",
                },
            },
            lastModified: {
                serializedName: "last-modified",
                xmlName: "last-modified",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            contentMD5: {
                serializedName: "content-md5",
                xmlName: "content-md5",
                type: {
                    name: "ByteArray",
                },
            },
            clientRequestId: {
                serializedName: "x-ms-client-request-id",
                xmlName: "x-ms-client-request-id",
                type: {
                    name: "String",
                },
            },
            requestId: {
                serializedName: "x-ms-request-id",
                xmlName: "x-ms-request-id",
                type: {
                    name: "String",
                },
            },
            version: {
                serializedName: "x-ms-version",
                xmlName: "x-ms-version",
                type: {
                    name: "String",
                },
            },
            versionId: {
                serializedName: "x-ms-version-id",
                xmlName: "x-ms-version-id",
                type: {
                    name: "String",
                },
            },
            date: {
                serializedName: "date",
                xmlName: "date",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            isServerEncrypted: {
                serializedName: "x-ms-request-server-encrypted",
                xmlName: "x-ms-request-server-encrypted",
                type: {
                    name: "Boolean",
                },
            },
            encryptionKeySha256: {
                serializedName: "x-ms-encryption-key-sha256",
                xmlName: "x-ms-encryption-key-sha256",
                type: {
                    name: "String",
                },
            },
            encryptionScope: {
                serializedName: "x-ms-encryption-scope",
                xmlName: "x-ms-encryption-scope",
                type: {
                    name: "String",
                },
            },
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const BlockBlobUploadExceptionHeaders = {
    serializedName: "BlockBlob_uploadExceptionHeaders",
    type: {
        name: "Composite",
        className: "BlockBlobUploadExceptionHeaders",
        modelProperties: {
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const BlockBlobPutBlobFromUrlHeaders = {
    serializedName: "BlockBlob_putBlobFromUrlHeaders",
    type: {
        name: "Composite",
        className: "BlockBlobPutBlobFromUrlHeaders",
        modelProperties: {
            etag: {
                serializedName: "etag",
                xmlName: "etag",
                type: {
                    name: "String",
                },
            },
            lastModified: {
                serializedName: "last-modified",
                xmlName: "last-modified",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            contentMD5: {
                serializedName: "content-md5",
                xmlName: "content-md5",
                type: {
                    name: "ByteArray",
                },
            },
            clientRequestId: {
                serializedName: "x-ms-client-request-id",
                xmlName: "x-ms-client-request-id",
                type: {
                    name: "String",
                },
            },
            requestId: {
                serializedName: "x-ms-request-id",
                xmlName: "x-ms-request-id",
                type: {
                    name: "String",
                },
            },
            version: {
                serializedName: "x-ms-version",
                xmlName: "x-ms-version",
                type: {
                    name: "String",
                },
            },
            versionId: {
                serializedName: "x-ms-version-id",
                xmlName: "x-ms-version-id",
                type: {
                    name: "String",
                },
            },
            date: {
                serializedName: "date",
                xmlName: "date",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            isServerEncrypted: {
                serializedName: "x-ms-request-server-encrypted",
                xmlName: "x-ms-request-server-encrypted",
                type: {
                    name: "Boolean",
                },
            },
            encryptionKeySha256: {
                serializedName: "x-ms-encryption-key-sha256",
                xmlName: "x-ms-encryption-key-sha256",
                type: {
                    name: "String",
                },
            },
            encryptionScope: {
                serializedName: "x-ms-encryption-scope",
                xmlName: "x-ms-encryption-scope",
                type: {
                    name: "String",
                },
            },
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const BlockBlobPutBlobFromUrlExceptionHeaders = {
    serializedName: "BlockBlob_putBlobFromUrlExceptionHeaders",
    type: {
        name: "Composite",
        className: "BlockBlobPutBlobFromUrlExceptionHeaders",
        modelProperties: {
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const BlockBlobStageBlockHeaders = {
    serializedName: "BlockBlob_stageBlockHeaders",
    type: {
        name: "Composite",
        className: "BlockBlobStageBlockHeaders",
        modelProperties: {
            contentMD5: {
                serializedName: "content-md5",
                xmlName: "content-md5",
                type: {
                    name: "ByteArray",
                },
            },
            clientRequestId: {
                serializedName: "x-ms-client-request-id",
                xmlName: "x-ms-client-request-id",
                type: {
                    name: "String",
                },
            },
            requestId: {
                serializedName: "x-ms-request-id",
                xmlName: "x-ms-request-id",
                type: {
                    name: "String",
                },
            },
            version: {
                serializedName: "x-ms-version",
                xmlName: "x-ms-version",
                type: {
                    name: "String",
                },
            },
            date: {
                serializedName: "date",
                xmlName: "date",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            xMsContentCrc64: {
                serializedName: "x-ms-content-crc64",
                xmlName: "x-ms-content-crc64",
                type: {
                    name: "ByteArray",
                },
            },
            isServerEncrypted: {
                serializedName: "x-ms-request-server-encrypted",
                xmlName: "x-ms-request-server-encrypted",
                type: {
                    name: "Boolean",
                },
            },
            encryptionKeySha256: {
                serializedName: "x-ms-encryption-key-sha256",
                xmlName: "x-ms-encryption-key-sha256",
                type: {
                    name: "String",
                },
            },
            encryptionScope: {
                serializedName: "x-ms-encryption-scope",
                xmlName: "x-ms-encryption-scope",
                type: {
                    name: "String",
                },
            },
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const BlockBlobStageBlockExceptionHeaders = {
    serializedName: "BlockBlob_stageBlockExceptionHeaders",
    type: {
        name: "Composite",
        className: "BlockBlobStageBlockExceptionHeaders",
        modelProperties: {
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const BlockBlobStageBlockFromURLHeaders = {
    serializedName: "BlockBlob_stageBlockFromURLHeaders",
    type: {
        name: "Composite",
        className: "BlockBlobStageBlockFromURLHeaders",
        modelProperties: {
            contentMD5: {
                serializedName: "content-md5",
                xmlName: "content-md5",
                type: {
                    name: "ByteArray",
                },
            },
            xMsContentCrc64: {
                serializedName: "x-ms-content-crc64",
                xmlName: "x-ms-content-crc64",
                type: {
                    name: "ByteArray",
                },
            },
            clientRequestId: {
                serializedName: "x-ms-client-request-id",
                xmlName: "x-ms-client-request-id",
                type: {
                    name: "String",
                },
            },
            requestId: {
                serializedName: "x-ms-request-id",
                xmlName: "x-ms-request-id",
                type: {
                    name: "String",
                },
            },
            version: {
                serializedName: "x-ms-version",
                xmlName: "x-ms-version",
                type: {
                    name: "String",
                },
            },
            date: {
                serializedName: "date",
                xmlName: "date",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            isServerEncrypted: {
                serializedName: "x-ms-request-server-encrypted",
                xmlName: "x-ms-request-server-encrypted",
                type: {
                    name: "Boolean",
                },
            },
            encryptionKeySha256: {
                serializedName: "x-ms-encryption-key-sha256",
                xmlName: "x-ms-encryption-key-sha256",
                type: {
                    name: "String",
                },
            },
            encryptionScope: {
                serializedName: "x-ms-encryption-scope",
                xmlName: "x-ms-encryption-scope",
                type: {
                    name: "String",
                },
            },
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const BlockBlobStageBlockFromURLExceptionHeaders = {
    serializedName: "BlockBlob_stageBlockFromURLExceptionHeaders",
    type: {
        name: "Composite",
        className: "BlockBlobStageBlockFromURLExceptionHeaders",
        modelProperties: {
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const BlockBlobCommitBlockListHeaders = {
    serializedName: "BlockBlob_commitBlockListHeaders",
    type: {
        name: "Composite",
        className: "BlockBlobCommitBlockListHeaders",
        modelProperties: {
            etag: {
                serializedName: "etag",
                xmlName: "etag",
                type: {
                    name: "String",
                },
            },
            lastModified: {
                serializedName: "last-modified",
                xmlName: "last-modified",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            contentMD5: {
                serializedName: "content-md5",
                xmlName: "content-md5",
                type: {
                    name: "ByteArray",
                },
            },
            xMsContentCrc64: {
                serializedName: "x-ms-content-crc64",
                xmlName: "x-ms-content-crc64",
                type: {
                    name: "ByteArray",
                },
            },
            clientRequestId: {
                serializedName: "x-ms-client-request-id",
                xmlName: "x-ms-client-request-id",
                type: {
                    name: "String",
                },
            },
            requestId: {
                serializedName: "x-ms-request-id",
                xmlName: "x-ms-request-id",
                type: {
                    name: "String",
                },
            },
            version: {
                serializedName: "x-ms-version",
                xmlName: "x-ms-version",
                type: {
                    name: "String",
                },
            },
            versionId: {
                serializedName: "x-ms-version-id",
                xmlName: "x-ms-version-id",
                type: {
                    name: "String",
                },
            },
            date: {
                serializedName: "date",
                xmlName: "date",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            isServerEncrypted: {
                serializedName: "x-ms-request-server-encrypted",
                xmlName: "x-ms-request-server-encrypted",
                type: {
                    name: "Boolean",
                },
            },
            encryptionKeySha256: {
                serializedName: "x-ms-encryption-key-sha256",
                xmlName: "x-ms-encryption-key-sha256",
                type: {
                    name: "String",
                },
            },
            encryptionScope: {
                serializedName: "x-ms-encryption-scope",
                xmlName: "x-ms-encryption-scope",
                type: {
                    name: "String",
                },
            },
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const BlockBlobCommitBlockListExceptionHeaders = {
    serializedName: "BlockBlob_commitBlockListExceptionHeaders",
    type: {
        name: "Composite",
        className: "BlockBlobCommitBlockListExceptionHeaders",
        modelProperties: {
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const BlockBlobGetBlockListHeaders = {
    serializedName: "BlockBlob_getBlockListHeaders",
    type: {
        name: "Composite",
        className: "BlockBlobGetBlockListHeaders",
        modelProperties: {
            lastModified: {
                serializedName: "last-modified",
                xmlName: "last-modified",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            etag: {
                serializedName: "etag",
                xmlName: "etag",
                type: {
                    name: "String",
                },
            },
            contentType: {
                serializedName: "content-type",
                xmlName: "content-type",
                type: {
                    name: "String",
                },
            },
            blobContentLength: {
                serializedName: "x-ms-blob-content-length",
                xmlName: "x-ms-blob-content-length",
                type: {
                    name: "Number",
                },
            },
            clientRequestId: {
                serializedName: "x-ms-client-request-id",
                xmlName: "x-ms-client-request-id",
                type: {
                    name: "String",
                },
            },
            requestId: {
                serializedName: "x-ms-request-id",
                xmlName: "x-ms-request-id",
                type: {
                    name: "String",
                },
            },
            version: {
                serializedName: "x-ms-version",
                xmlName: "x-ms-version",
                type: {
                    name: "String",
                },
            },
            date: {
                serializedName: "date",
                xmlName: "date",
                type: {
                    name: "DateTimeRfc1123",
                },
            },
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
export const BlockBlobGetBlockListExceptionHeaders = {
    serializedName: "BlockBlob_getBlockListExceptionHeaders",
    type: {
        name: "Composite",
        className: "BlockBlobGetBlockListExceptionHeaders",
        modelProperties: {
            errorCode: {
                serializedName: "x-ms-error-code",
                xmlName: "x-ms-error-code",
                type: {
                    name: "String",
                },
            },
        },
    },
};
//# sourceMappingURL=mappers.js.map
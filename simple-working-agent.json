{"name": "Simple Working AI Agent", "nodes": [{"parameters": {"httpMethod": "POST", "path": "simple-ai", "options": {}}, "id": "webhook", "name": "Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [300, 300]}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"success\": true,\n  \"message\": \"🤖 AI Agent is working!\",\n  \"instruction\": \"{{ $json.body.instruction || $json.body.chatInput || $json.instruction || $json.chatInput || 'Hello' }}\",\n  \"response\": \"I received your message and I am ready to help you build any AI agent or workflow you need! Your instruction was processed successfully.\",\n  \"timestamp\": \"{{ new Date().toISOString() }}\",\n  \"webhook\": \"http://localhost:2410/webhook/simple-ai\",\n  \"status\": \"✅ Working perfectly!\"\n}"}, "id": "response", "name": "Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [500, 300]}], "connections": {"Webhook": {"main": [[{"node": "Response", "type": "main", "index": 0}]]}}, "active": false, "settings": {"timezone": "UTC", "saveManualExecutions": true}, "tags": ["simple", "working"]}
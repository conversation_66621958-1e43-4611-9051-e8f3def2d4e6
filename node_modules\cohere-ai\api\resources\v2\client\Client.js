"use strict";
/**
 * This file was auto-generated by Fern from our API Definition.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.V2 = void 0;
const environments = __importStar(require("../../../../environments"));
const core = __importStar(require("../../../../core"));
const Cohere = __importStar(require("../../../index"));
const serializers = __importStar(require("../../../../serialization/index"));
const url_join_1 = __importDefault(require("url-join"));
const errors = __importStar(require("../../../../errors/index"));
class V2 {
    constructor(_options = {}) {
        this._options = _options;
    }
    /**
     * Generates a message from the model in response to a provided conversation. To learn how to use the Chat API with Streaming and RAG follow our Text Generation guides.
     */
    chatStream(request, requestOptions) {
        var _a, _b;
        return __awaiter(this, void 0, void 0, function* () {
            const _response = yield ((_a = this._options.fetcher) !== null && _a !== void 0 ? _a : core.fetcher)({
                url: (0, url_join_1.default)((_b = (yield core.Supplier.get(this._options.environment))) !== null && _b !== void 0 ? _b : environments.CohereEnvironment.Production, "v2/chat"),
                method: "POST",
                headers: {
                    Authorization: yield this._getAuthorizationHeader(),
                    "X-Client-Name": (yield core.Supplier.get(this._options.clientName)) != null
                        ? yield core.Supplier.get(this._options.clientName)
                        : undefined,
                    "X-Fern-Language": "JavaScript",
                    "X-Fern-SDK-Name": "cohere-ai",
                    "X-Fern-SDK-Version": "7.14.0",
                    "User-Agent": "cohere-ai/7.14.0",
                    "X-Fern-Runtime": core.RUNTIME.type,
                    "X-Fern-Runtime-Version": core.RUNTIME.version,
                },
                contentType: "application/json",
                requestType: "json",
                body: Object.assign(Object.assign({}, serializers.V2ChatStreamRequest.jsonOrThrow(request, {
                    unrecognizedObjectKeys: "passthrough",
                    allowUnrecognizedUnionMembers: true,
                    allowUnrecognizedEnumValues: true,
                })), { stream: true }),
                responseType: "sse",
                timeoutMs: (requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.timeoutInSeconds) != null ? requestOptions.timeoutInSeconds * 1000 : 300000,
                maxRetries: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.maxRetries,
                abortSignal: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.abortSignal,
            });
            if (_response.ok) {
                return new core.Stream({
                    stream: _response.body,
                    parse: (data) => __awaiter(this, void 0, void 0, function* () {
                        return serializers.StreamedChatResponseV2.parseOrThrow(data, {
                            unrecognizedObjectKeys: "passthrough",
                            allowUnrecognizedUnionMembers: true,
                            allowUnrecognizedEnumValues: true,
                            skipValidation: true,
                            breadcrumbsPrefix: ["response"],
                        });
                    }),
                    signal: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.abortSignal,
                    eventShape: {
                        type: "sse",
                        streamTerminator: "[DONE]",
                    },
                });
            }
            if (_response.error.reason === "status-code") {
                switch (_response.error.statusCode) {
                    case 400:
                        throw new Cohere.BadRequestError(_response.error.body);
                    case 401:
                        throw new Cohere.UnauthorizedError(_response.error.body);
                    case 403:
                        throw new Cohere.ForbiddenError(_response.error.body);
                    case 404:
                        throw new Cohere.NotFoundError(_response.error.body);
                    case 422:
                        throw new Cohere.UnprocessableEntityError(serializers.UnprocessableEntityErrorBody.parseOrThrow(_response.error.body, {
                            unrecognizedObjectKeys: "passthrough",
                            allowUnrecognizedUnionMembers: true,
                            allowUnrecognizedEnumValues: true,
                            skipValidation: true,
                            breadcrumbsPrefix: ["response"],
                        }));
                    case 429:
                        throw new Cohere.TooManyRequestsError(serializers.TooManyRequestsErrorBody.parseOrThrow(_response.error.body, {
                            unrecognizedObjectKeys: "passthrough",
                            allowUnrecognizedUnionMembers: true,
                            allowUnrecognizedEnumValues: true,
                            skipValidation: true,
                            breadcrumbsPrefix: ["response"],
                        }));
                    case 499:
                        throw new Cohere.ClientClosedRequestError(serializers.ClientClosedRequestErrorBody.parseOrThrow(_response.error.body, {
                            unrecognizedObjectKeys: "passthrough",
                            allowUnrecognizedUnionMembers: true,
                            allowUnrecognizedEnumValues: true,
                            skipValidation: true,
                            breadcrumbsPrefix: ["response"],
                        }));
                    case 500:
                        throw new Cohere.InternalServerError(_response.error.body);
                    case 501:
                        throw new Cohere.NotImplementedError(serializers.NotImplementedErrorBody.parseOrThrow(_response.error.body, {
                            unrecognizedObjectKeys: "passthrough",
                            allowUnrecognizedUnionMembers: true,
                            allowUnrecognizedEnumValues: true,
                            skipValidation: true,
                            breadcrumbsPrefix: ["response"],
                        }));
                    case 503:
                        throw new Cohere.ServiceUnavailableError(_response.error.body);
                    case 504:
                        throw new Cohere.GatewayTimeoutError(serializers.GatewayTimeoutErrorBody.parseOrThrow(_response.error.body, {
                            unrecognizedObjectKeys: "passthrough",
                            allowUnrecognizedUnionMembers: true,
                            allowUnrecognizedEnumValues: true,
                            skipValidation: true,
                            breadcrumbsPrefix: ["response"],
                        }));
                    default:
                        throw new errors.CohereError({
                            statusCode: _response.error.statusCode,
                            body: _response.error.body,
                        });
                }
            }
            switch (_response.error.reason) {
                case "non-json":
                    throw new errors.CohereError({
                        statusCode: _response.error.statusCode,
                        body: _response.error.rawBody,
                    });
                case "timeout":
                    throw new errors.CohereTimeoutError();
                case "unknown":
                    throw new errors.CohereError({
                        message: _response.error.errorMessage,
                    });
            }
        });
    }
    /**
     * Generates a message from the model in response to a provided conversation. To learn how to use the Chat API with Streaming and RAG follow our Text Generation guides.
     *
     * @param {Cohere.V2ChatRequest} request
     * @param {V2.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link Cohere.BadRequestError}
     * @throws {@link Cohere.UnauthorizedError}
     * @throws {@link Cohere.ForbiddenError}
     * @throws {@link Cohere.NotFoundError}
     * @throws {@link Cohere.UnprocessableEntityError}
     * @throws {@link Cohere.TooManyRequestsError}
     * @throws {@link Cohere.ClientClosedRequestError}
     * @throws {@link Cohere.InternalServerError}
     * @throws {@link Cohere.NotImplementedError}
     * @throws {@link Cohere.ServiceUnavailableError}
     * @throws {@link Cohere.GatewayTimeoutError}
     *
     * @example
     *     await client.v2.chat({
     *         model: "model",
     *         messages: [{
     *                 role: "tool",
     *                 toolCallId: "messages"
     *             }]
     *     })
     */
    chat(request, requestOptions) {
        var _a, _b;
        return __awaiter(this, void 0, void 0, function* () {
            const _response = yield ((_a = this._options.fetcher) !== null && _a !== void 0 ? _a : core.fetcher)({
                url: (0, url_join_1.default)((_b = (yield core.Supplier.get(this._options.environment))) !== null && _b !== void 0 ? _b : environments.CohereEnvironment.Production, "v2/chat"),
                method: "POST",
                headers: {
                    Authorization: yield this._getAuthorizationHeader(),
                    "X-Client-Name": (yield core.Supplier.get(this._options.clientName)) != null
                        ? yield core.Supplier.get(this._options.clientName)
                        : undefined,
                    "X-Fern-Language": "JavaScript",
                    "X-Fern-SDK-Name": "cohere-ai",
                    "X-Fern-SDK-Version": "7.14.0",
                    "User-Agent": "cohere-ai/7.14.0",
                    "X-Fern-Runtime": core.RUNTIME.type,
                    "X-Fern-Runtime-Version": core.RUNTIME.version,
                },
                contentType: "application/json",
                requestType: "json",
                body: Object.assign(Object.assign({}, serializers.V2ChatRequest.jsonOrThrow(request, {
                    unrecognizedObjectKeys: "passthrough",
                    allowUnrecognizedUnionMembers: true,
                    allowUnrecognizedEnumValues: true,
                })), { stream: false }),
                timeoutMs: (requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.timeoutInSeconds) != null ? requestOptions.timeoutInSeconds * 1000 : 300000,
                maxRetries: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.maxRetries,
                abortSignal: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.abortSignal,
            });
            if (_response.ok) {
                return serializers.ChatResponse.parseOrThrow(_response.body, {
                    unrecognizedObjectKeys: "passthrough",
                    allowUnrecognizedUnionMembers: true,
                    allowUnrecognizedEnumValues: true,
                    skipValidation: true,
                    breadcrumbsPrefix: ["response"],
                });
            }
            if (_response.error.reason === "status-code") {
                switch (_response.error.statusCode) {
                    case 400:
                        throw new Cohere.BadRequestError(_response.error.body);
                    case 401:
                        throw new Cohere.UnauthorizedError(_response.error.body);
                    case 403:
                        throw new Cohere.ForbiddenError(_response.error.body);
                    case 404:
                        throw new Cohere.NotFoundError(_response.error.body);
                    case 422:
                        throw new Cohere.UnprocessableEntityError(serializers.UnprocessableEntityErrorBody.parseOrThrow(_response.error.body, {
                            unrecognizedObjectKeys: "passthrough",
                            allowUnrecognizedUnionMembers: true,
                            allowUnrecognizedEnumValues: true,
                            skipValidation: true,
                            breadcrumbsPrefix: ["response"],
                        }));
                    case 429:
                        throw new Cohere.TooManyRequestsError(serializers.TooManyRequestsErrorBody.parseOrThrow(_response.error.body, {
                            unrecognizedObjectKeys: "passthrough",
                            allowUnrecognizedUnionMembers: true,
                            allowUnrecognizedEnumValues: true,
                            skipValidation: true,
                            breadcrumbsPrefix: ["response"],
                        }));
                    case 499:
                        throw new Cohere.ClientClosedRequestError(serializers.ClientClosedRequestErrorBody.parseOrThrow(_response.error.body, {
                            unrecognizedObjectKeys: "passthrough",
                            allowUnrecognizedUnionMembers: true,
                            allowUnrecognizedEnumValues: true,
                            skipValidation: true,
                            breadcrumbsPrefix: ["response"],
                        }));
                    case 500:
                        throw new Cohere.InternalServerError(_response.error.body);
                    case 501:
                        throw new Cohere.NotImplementedError(serializers.NotImplementedErrorBody.parseOrThrow(_response.error.body, {
                            unrecognizedObjectKeys: "passthrough",
                            allowUnrecognizedUnionMembers: true,
                            allowUnrecognizedEnumValues: true,
                            skipValidation: true,
                            breadcrumbsPrefix: ["response"],
                        }));
                    case 503:
                        throw new Cohere.ServiceUnavailableError(_response.error.body);
                    case 504:
                        throw new Cohere.GatewayTimeoutError(serializers.GatewayTimeoutErrorBody.parseOrThrow(_response.error.body, {
                            unrecognizedObjectKeys: "passthrough",
                            allowUnrecognizedUnionMembers: true,
                            allowUnrecognizedEnumValues: true,
                            skipValidation: true,
                            breadcrumbsPrefix: ["response"],
                        }));
                    default:
                        throw new errors.CohereError({
                            statusCode: _response.error.statusCode,
                            body: _response.error.body,
                        });
                }
            }
            switch (_response.error.reason) {
                case "non-json":
                    throw new errors.CohereError({
                        statusCode: _response.error.statusCode,
                        body: _response.error.rawBody,
                    });
                case "timeout":
                    throw new errors.CohereTimeoutError();
                case "unknown":
                    throw new errors.CohereError({
                        message: _response.error.errorMessage,
                    });
            }
        });
    }
    /**
     * This endpoint returns text embeddings. An embedding is a list of floating point numbers that captures semantic information about the text that it represents.
     *
     * Embeddings can be used to create text classifiers as well as empower semantic search. To learn more about embeddings, see the embedding page.
     *
     * If you want to learn more how to use the embedding model, have a look at the [Semantic Search Guide](/docs/semantic-search).
     *
     * @param {Cohere.V2EmbedRequest} request
     * @param {V2.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link Cohere.BadRequestError}
     * @throws {@link Cohere.UnauthorizedError}
     * @throws {@link Cohere.ForbiddenError}
     * @throws {@link Cohere.NotFoundError}
     * @throws {@link Cohere.UnprocessableEntityError}
     * @throws {@link Cohere.TooManyRequestsError}
     * @throws {@link Cohere.ClientClosedRequestError}
     * @throws {@link Cohere.InternalServerError}
     * @throws {@link Cohere.NotImplementedError}
     * @throws {@link Cohere.ServiceUnavailableError}
     * @throws {@link Cohere.GatewayTimeoutError}
     *
     * @example
     *     await client.v2.embed({
     *         model: "model"
     *     })
     */
    embed(request, requestOptions) {
        var _a, _b;
        return __awaiter(this, void 0, void 0, function* () {
            const _response = yield ((_a = this._options.fetcher) !== null && _a !== void 0 ? _a : core.fetcher)({
                url: (0, url_join_1.default)((_b = (yield core.Supplier.get(this._options.environment))) !== null && _b !== void 0 ? _b : environments.CohereEnvironment.Production, "v2/embed"),
                method: "POST",
                headers: {
                    Authorization: yield this._getAuthorizationHeader(),
                    "X-Client-Name": (yield core.Supplier.get(this._options.clientName)) != null
                        ? yield core.Supplier.get(this._options.clientName)
                        : undefined,
                    "X-Fern-Language": "JavaScript",
                    "X-Fern-SDK-Name": "cohere-ai",
                    "X-Fern-SDK-Version": "7.14.0",
                    "User-Agent": "cohere-ai/7.14.0",
                    "X-Fern-Runtime": core.RUNTIME.type,
                    "X-Fern-Runtime-Version": core.RUNTIME.version,
                },
                contentType: "application/json",
                requestType: "json",
                body: serializers.V2EmbedRequest.jsonOrThrow(request, {
                    unrecognizedObjectKeys: "passthrough",
                    allowUnrecognizedUnionMembers: true,
                    allowUnrecognizedEnumValues: true,
                }),
                timeoutMs: (requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.timeoutInSeconds) != null ? requestOptions.timeoutInSeconds * 1000 : 300000,
                maxRetries: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.maxRetries,
                abortSignal: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.abortSignal,
            });
            if (_response.ok) {
                return serializers.EmbedByTypeResponse.parseOrThrow(_response.body, {
                    unrecognizedObjectKeys: "passthrough",
                    allowUnrecognizedUnionMembers: true,
                    allowUnrecognizedEnumValues: true,
                    skipValidation: true,
                    breadcrumbsPrefix: ["response"],
                });
            }
            if (_response.error.reason === "status-code") {
                switch (_response.error.statusCode) {
                    case 400:
                        throw new Cohere.BadRequestError(_response.error.body);
                    case 401:
                        throw new Cohere.UnauthorizedError(_response.error.body);
                    case 403:
                        throw new Cohere.ForbiddenError(_response.error.body);
                    case 404:
                        throw new Cohere.NotFoundError(_response.error.body);
                    case 422:
                        throw new Cohere.UnprocessableEntityError(serializers.UnprocessableEntityErrorBody.parseOrThrow(_response.error.body, {
                            unrecognizedObjectKeys: "passthrough",
                            allowUnrecognizedUnionMembers: true,
                            allowUnrecognizedEnumValues: true,
                            skipValidation: true,
                            breadcrumbsPrefix: ["response"],
                        }));
                    case 429:
                        throw new Cohere.TooManyRequestsError(serializers.TooManyRequestsErrorBody.parseOrThrow(_response.error.body, {
                            unrecognizedObjectKeys: "passthrough",
                            allowUnrecognizedUnionMembers: true,
                            allowUnrecognizedEnumValues: true,
                            skipValidation: true,
                            breadcrumbsPrefix: ["response"],
                        }));
                    case 499:
                        throw new Cohere.ClientClosedRequestError(serializers.ClientClosedRequestErrorBody.parseOrThrow(_response.error.body, {
                            unrecognizedObjectKeys: "passthrough",
                            allowUnrecognizedUnionMembers: true,
                            allowUnrecognizedEnumValues: true,
                            skipValidation: true,
                            breadcrumbsPrefix: ["response"],
                        }));
                    case 500:
                        throw new Cohere.InternalServerError(_response.error.body);
                    case 501:
                        throw new Cohere.NotImplementedError(serializers.NotImplementedErrorBody.parseOrThrow(_response.error.body, {
                            unrecognizedObjectKeys: "passthrough",
                            allowUnrecognizedUnionMembers: true,
                            allowUnrecognizedEnumValues: true,
                            skipValidation: true,
                            breadcrumbsPrefix: ["response"],
                        }));
                    case 503:
                        throw new Cohere.ServiceUnavailableError(_response.error.body);
                    case 504:
                        throw new Cohere.GatewayTimeoutError(serializers.GatewayTimeoutErrorBody.parseOrThrow(_response.error.body, {
                            unrecognizedObjectKeys: "passthrough",
                            allowUnrecognizedUnionMembers: true,
                            allowUnrecognizedEnumValues: true,
                            skipValidation: true,
                            breadcrumbsPrefix: ["response"],
                        }));
                    default:
                        throw new errors.CohereError({
                            statusCode: _response.error.statusCode,
                            body: _response.error.body,
                        });
                }
            }
            switch (_response.error.reason) {
                case "non-json":
                    throw new errors.CohereError({
                        statusCode: _response.error.statusCode,
                        body: _response.error.rawBody,
                    });
                case "timeout":
                    throw new errors.CohereTimeoutError();
                case "unknown":
                    throw new errors.CohereError({
                        message: _response.error.errorMessage,
                    });
            }
        });
    }
    /**
     * This endpoint takes in a query and a list of texts and produces an ordered array with each text assigned a relevance score.
     *
     * @param {Cohere.V2RerankRequest} request
     * @param {V2.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link Cohere.BadRequestError}
     * @throws {@link Cohere.UnauthorizedError}
     * @throws {@link Cohere.ForbiddenError}
     * @throws {@link Cohere.NotFoundError}
     * @throws {@link Cohere.UnprocessableEntityError}
     * @throws {@link Cohere.TooManyRequestsError}
     * @throws {@link Cohere.ClientClosedRequestError}
     * @throws {@link Cohere.InternalServerError}
     * @throws {@link Cohere.NotImplementedError}
     * @throws {@link Cohere.ServiceUnavailableError}
     * @throws {@link Cohere.GatewayTimeoutError}
     *
     * @example
     *     await client.v2.rerank({
     *         model: "model",
     *         query: "query",
     *         documents: ["documents"]
     *     })
     */
    rerank(request, requestOptions) {
        var _a, _b;
        return __awaiter(this, void 0, void 0, function* () {
            const _response = yield ((_a = this._options.fetcher) !== null && _a !== void 0 ? _a : core.fetcher)({
                url: (0, url_join_1.default)((_b = (yield core.Supplier.get(this._options.environment))) !== null && _b !== void 0 ? _b : environments.CohereEnvironment.Production, "v2/rerank"),
                method: "POST",
                headers: {
                    Authorization: yield this._getAuthorizationHeader(),
                    "X-Client-Name": (yield core.Supplier.get(this._options.clientName)) != null
                        ? yield core.Supplier.get(this._options.clientName)
                        : undefined,
                    "X-Fern-Language": "JavaScript",
                    "X-Fern-SDK-Name": "cohere-ai",
                    "X-Fern-SDK-Version": "7.14.0",
                    "User-Agent": "cohere-ai/7.14.0",
                    "X-Fern-Runtime": core.RUNTIME.type,
                    "X-Fern-Runtime-Version": core.RUNTIME.version,
                },
                contentType: "application/json",
                requestType: "json",
                body: serializers.V2RerankRequest.jsonOrThrow(request, {
                    unrecognizedObjectKeys: "passthrough",
                    allowUnrecognizedUnionMembers: true,
                    allowUnrecognizedEnumValues: true,
                }),
                timeoutMs: (requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.timeoutInSeconds) != null ? requestOptions.timeoutInSeconds * 1000 : 300000,
                maxRetries: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.maxRetries,
                abortSignal: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.abortSignal,
            });
            if (_response.ok) {
                return serializers.V2RerankResponse.parseOrThrow(_response.body, {
                    unrecognizedObjectKeys: "passthrough",
                    allowUnrecognizedUnionMembers: true,
                    allowUnrecognizedEnumValues: true,
                    skipValidation: true,
                    breadcrumbsPrefix: ["response"],
                });
            }
            if (_response.error.reason === "status-code") {
                switch (_response.error.statusCode) {
                    case 400:
                        throw new Cohere.BadRequestError(_response.error.body);
                    case 401:
                        throw new Cohere.UnauthorizedError(_response.error.body);
                    case 403:
                        throw new Cohere.ForbiddenError(_response.error.body);
                    case 404:
                        throw new Cohere.NotFoundError(_response.error.body);
                    case 422:
                        throw new Cohere.UnprocessableEntityError(serializers.UnprocessableEntityErrorBody.parseOrThrow(_response.error.body, {
                            unrecognizedObjectKeys: "passthrough",
                            allowUnrecognizedUnionMembers: true,
                            allowUnrecognizedEnumValues: true,
                            skipValidation: true,
                            breadcrumbsPrefix: ["response"],
                        }));
                    case 429:
                        throw new Cohere.TooManyRequestsError(serializers.TooManyRequestsErrorBody.parseOrThrow(_response.error.body, {
                            unrecognizedObjectKeys: "passthrough",
                            allowUnrecognizedUnionMembers: true,
                            allowUnrecognizedEnumValues: true,
                            skipValidation: true,
                            breadcrumbsPrefix: ["response"],
                        }));
                    case 499:
                        throw new Cohere.ClientClosedRequestError(serializers.ClientClosedRequestErrorBody.parseOrThrow(_response.error.body, {
                            unrecognizedObjectKeys: "passthrough",
                            allowUnrecognizedUnionMembers: true,
                            allowUnrecognizedEnumValues: true,
                            skipValidation: true,
                            breadcrumbsPrefix: ["response"],
                        }));
                    case 500:
                        throw new Cohere.InternalServerError(_response.error.body);
                    case 501:
                        throw new Cohere.NotImplementedError(serializers.NotImplementedErrorBody.parseOrThrow(_response.error.body, {
                            unrecognizedObjectKeys: "passthrough",
                            allowUnrecognizedUnionMembers: true,
                            allowUnrecognizedEnumValues: true,
                            skipValidation: true,
                            breadcrumbsPrefix: ["response"],
                        }));
                    case 503:
                        throw new Cohere.ServiceUnavailableError(_response.error.body);
                    case 504:
                        throw new Cohere.GatewayTimeoutError(serializers.GatewayTimeoutErrorBody.parseOrThrow(_response.error.body, {
                            unrecognizedObjectKeys: "passthrough",
                            allowUnrecognizedUnionMembers: true,
                            allowUnrecognizedEnumValues: true,
                            skipValidation: true,
                            breadcrumbsPrefix: ["response"],
                        }));
                    default:
                        throw new errors.CohereError({
                            statusCode: _response.error.statusCode,
                            body: _response.error.body,
                        });
                }
            }
            switch (_response.error.reason) {
                case "non-json":
                    throw new errors.CohereError({
                        statusCode: _response.error.statusCode,
                        body: _response.error.rawBody,
                    });
                case "timeout":
                    throw new errors.CohereTimeoutError();
                case "unknown":
                    throw new errors.CohereError({
                        message: _response.error.errorMessage,
                    });
            }
        });
    }
    _getAuthorizationHeader() {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            const bearer = (_a = (yield core.Supplier.get(this._options.token))) !== null && _a !== void 0 ? _a : process === null || process === void 0 ? void 0 : process.env["CO_API_KEY"];
            if (bearer == null) {
                throw new errors.CohereError({
                    message: "Please specify CO_API_KEY when instantiating the client.",
                });
            }
            return `Bearer ${bearer}`;
        });
    }
}
exports.V2 = V2;

/*! @azure/msal-node v2.16.2 2024-11-19 */
'use strict';
export { Serializer } from './cache/serializer/Serializer.mjs';
export { Deserializer } from './cache/serializer/Deserializer.mjs';

/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
/**
 * Warning: This set of exports is purely intended to be used by other MSAL libraries, and should be considered potentially unstable. We strongly discourage using them directly, you do so at your own risk.
 * Breaking changes to these APIs will be shipped under a minor version, instead of a major version.
 */
//# sourceMappingURL=internals.mjs.map

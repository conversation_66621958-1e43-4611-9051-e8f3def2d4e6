{"version": 3, "file": "BatchUtils.browser.js", "sourceRoot": "", "sources": ["../../../src/BatchUtils.browser.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EAAE,YAAY,EAAE,MAAM,uBAAuB,CAAC;AAErD,MAAM,CAAC,KAAK,UAAU,aAAa,CACjC,aAA8C;IAE9C,MAAM,IAAI,GAAG,CAAC,MAAM,aAAa,CAAC,QAAQ,CAAS,CAAC;IACpD,OAAO,YAAY,CAAC,IAAI,CAAC,CAAC;AAC5B,CAAC;AAED,MAAM,UAAU,cAAc,CAAC,GAAW;IACxC,OAAO,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC;AAC9B,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { ServiceSubmitBatchResponseModel } from \"./generatedModels\";\nimport { blobToString } from \"./utils/utils.browser\";\n\nexport async function getBodyAsText(\n  batchResponse: ServiceSubmitBatchResponseModel,\n): Promise<string> {\n  const blob = (await batchResponse.blobBody) as Blob;\n  return blobToString(blob);\n}\n\nexport function utf8ByteLength(str: string): number {\n  return new Blob([str]).size;\n}\n"]}
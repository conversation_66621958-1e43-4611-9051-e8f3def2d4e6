"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.readAVSCAsync = exports.readAVSC = exports.avdlToAVSCAsync = exports.avdlToAVSC = void 0;
var avdlToAVSC_1 = require("./avdlToAVSC");
Object.defineProperty(exports, "avdlToAVSC", { enumerable: true, get: function () { return avdlToAVSC_1.avdlToAVSC; } });
Object.defineProperty(exports, "avdlToAVSCAsync", { enumerable: true, get: function () { return avdlToAVSC_1.avdlToAVSCAsync; } });
var readAVSC_1 = require("./readAVSC");
Object.defineProperty(exports, "readAVSC", { enumerable: true, get: function () { return readAVSC_1.readAVSC; } });
Object.defineProperty(exports, "readAVSCAsync", { enumerable: true, get: function () { return readAVSC_1.readAVSCAsync; } });
//# sourceMappingURL=index.js.map
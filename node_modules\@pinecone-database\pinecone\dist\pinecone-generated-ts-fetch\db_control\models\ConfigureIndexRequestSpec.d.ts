/**
 * Pinecone Control Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import type { ConfigureIndexRequestSpecPod } from './ConfigureIndexRequestSpecPod';
/**
 *
 * @export
 * @interface ConfigureIndexRequestSpec
 */
export interface ConfigureIndexRequestSpec {
    /**
     *
     * @type {ConfigureIndexRequestSpecPod}
     * @memberof ConfigureIndexRequestSpec
     */
    pod: ConfigureIndexRequestSpecPod;
}
/**
 * Check if a given object implements the ConfigureIndexRequestSpec interface.
 */
export declare function instanceOfConfigureIndexRequestSpec(value: object): boolean;
export declare function ConfigureIndexRequestSpecFromJSON(json: any): ConfigureIndexRequestSpec;
export declare function ConfigureIndexRequestSpecFromJSONTyped(json: any, ignoreDiscriminator: boolean): ConfigureIndexRequestSpec;
export declare function ConfigureIndexRequestSpecToJSON(value?: ConfigureIndexRequestSpec | null): any;

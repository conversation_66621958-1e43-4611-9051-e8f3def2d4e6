/**
 * Pinecone Inference API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
/**
 * Model-specific parameters.
 * @export
 * @interface EmbedRequestParameters
 */
export interface EmbedRequestParameters {
    /**
     * Common property used to distinguish between types of data.
     * @type {string}
     * @memberof EmbedRequestParameters
     */
    inputType?: string;
    /**
     * How to handle inputs longer than those supported by the model. If `"END"`, truncate the input sequence at the token limit. If `"NONE"`, return an error when the input exceeds the token limit.
     * @type {string}
     * @memberof EmbedRequestParameters
     */
    truncate?: string;
}
/**
 * Check if a given object implements the EmbedRequestParameters interface.
 */
export declare function instanceOfEmbedRequestParameters(value: object): boolean;
export declare function EmbedRequestParametersFromJSON(json: any): EmbedRequestParameters;
export declare function EmbedRequestParametersFromJSONTyped(json: any, ignoreDiscriminator: boolean): EmbedRequestParameters;
export declare function EmbedRequestParametersToJSON(value?: EmbedRequestParameters | null): any;

{"version": 3, "file": "AvroConstants.js", "sourceRoot": "", "sources": ["../../../../storage-internal-avro/src/AvroConstants.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,MAAM,CAAC,MAAM,qBAAqB,GAAW,EAAE,CAAC;AAChD,MAAM,CAAC,MAAM,eAAe,GAAe,IAAI,UAAU,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;AAC5E,MAAM,CAAC,MAAM,cAAc,GAAW,YAAY,CAAC;AACnD,MAAM,CAAC,MAAM,eAAe,GAAW,aAAa,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nexport const AVRO_SYNC_MARKER_SIZE: number = 16;\nexport const AVRO_INIT_BYTES: Uint8Array = new Uint8Array([79, 98, 106, 1]);\nexport const AVRO_CODEC_KEY: string = \"avro.codec\";\nexport const AVRO_SCHEMA_KEY: string = \"avro.schema\";\n"]}
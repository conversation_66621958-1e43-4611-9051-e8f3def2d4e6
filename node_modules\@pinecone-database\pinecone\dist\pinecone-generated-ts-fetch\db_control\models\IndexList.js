"use strict";
/* tslint:disable */
/* eslint-disable */
/**
 * Pinecone Control Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2024-10
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.IndexListToJSON = exports.IndexListFromJSONTyped = exports.IndexListFromJSON = exports.instanceOfIndexList = void 0;
var runtime_1 = require("../runtime");
var IndexModel_1 = require("./IndexModel");
/**
 * Check if a given object implements the IndexList interface.
 */
function instanceOfIndexList(value) {
    var isInstance = true;
    return isInstance;
}
exports.instanceOfIndexList = instanceOfIndexList;
function IndexListFromJSON(json) {
    return IndexListFromJSONTyped(json, false);
}
exports.IndexListFromJSON = IndexListFromJSON;
function IndexListFromJSONTyped(json, ignoreDiscriminator) {
    if ((json === undefined) || (json === null)) {
        return json;
    }
    return {
        'indexes': !(0, runtime_1.exists)(json, 'indexes') ? undefined : (json['indexes'].map(IndexModel_1.IndexModelFromJSON)),
    };
}
exports.IndexListFromJSONTyped = IndexListFromJSONTyped;
function IndexListToJSON(value) {
    if (value === undefined) {
        return undefined;
    }
    if (value === null) {
        return null;
    }
    return {
        'indexes': value.indexes === undefined ? undefined : (value.indexes.map(IndexModel_1.IndexModelToJSON)),
    };
}
exports.IndexListToJSON = IndexListToJSON;
//# sourceMappingURL=IndexList.js.map